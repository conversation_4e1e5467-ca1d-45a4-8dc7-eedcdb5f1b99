package com.xtc.marketing.adapterservice.rpc.sf.sfdto.command;

import com.google.gson.annotations.SerializedName;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.SfWarehouseBaseRequest;
import lombok.*;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class SfInboundResponseCmd extends SfWarehouseBaseRequest {

    /**
     * 请求体
     */
    @SerializedName("Body")
    private SfInboundResponseBodyCmd body;

}
