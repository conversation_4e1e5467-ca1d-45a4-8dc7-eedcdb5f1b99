package com.xtc.marketing.adapterservice.shop.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xtc.marketing.adapterservice.config.BaseDO;
import com.xtc.marketing.adapterservice.shop.enums.ShopTypeEnum;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
@TableName("t_shop")
public class ShopDO extends BaseDO {

    /**
     * 平台代码
     */
    private String platformCode;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 店铺代码
     */
    private String shopCode;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 店铺id
     */
    private String shopId;
    /**
     * 店铺类型
     */
    private ShopTypeEnum shopType;
    /**
     * 代理代码
     */
    private String agentCode;
    /**
     * api地址
     */
    private String apiUrl;
    /**
     * 应用key
     */
    private String appKey;
    /**
     * 应用secret
     */
    private String appSecret;
    /**
     * 应用过期时间
     */
    private LocalDateTime appExpireTime;
    /**
     * 应用session_key
     */
    private String appSessionKey;
    /**
     * 应用access_token
     */
    private String appAccessToken;
    /**
     * 应用refresh_token
     */
    private String appRefreshToken;
    /**
     * 启用token刷新任务
     */
    private Boolean enabledTokenRefresh;
    /**
     * 启用店铺
     */
    private Boolean enabled;
    /**
     * 入驻时间
     */
    private LocalDateTime entryTime;
    /**
     * 关店时间
     */
    private LocalDateTime closeTime;

}
