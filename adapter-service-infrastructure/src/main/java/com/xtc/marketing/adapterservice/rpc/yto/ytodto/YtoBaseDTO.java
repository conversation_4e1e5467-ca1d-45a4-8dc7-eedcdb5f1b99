package com.xtc.marketing.adapterservice.rpc.yto.ytodto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@ToString
public class YtoBaseDTO {

    /**
     * statusCode请求成功代码
     */
    public static final String SUCCESS_STATUS_CODE = "0";

    private Boolean success;

    private String code;

    private String reason;

    private String originResponse;

    private String statusCode;

    private String statusMessage;

    public boolean isFailure() {
        // 圆通不同接口返回值不同，有的是success，有的是statusCode，优先判断statusCode
        if (StringUtils.isNotBlank(statusCode)) {
            return !SUCCESS_STATUS_CODE.equals(statusCode);
        } else {
            return BooleanUtils.isFalse(success);
        }
    }

}
