package com.xtc.marketing.adapterservice.notify.repository;

import com.xtc.marketing.adapterservice.config.BaseRepository;
import com.xtc.marketing.adapterservice.notify.dataobject.PushConfigDO;
import com.xtc.marketing.adapterservice.notify.repository.mapper.PushConfigMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class PushConfigRepository extends BaseRepository<PushConfigMapper, PushConfigDO> {

    /**
     * 查询已启用的推送配置
     *
     * @return 配置列表
     */
    public List<PushConfigDO> listEnabled() {
        return this.listBy(PushConfigDO::getEnabled, true);
    }

}
