package com.xtc.marketing.adapterservice.rpc.sf;

import com.google.common.collect.Lists;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.SfBaseDTO;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.SfStockDTO;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.SfWarehouseBaseRequest;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.SfWarehouseResponseBaseDTO;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.command.SfInboundApplyBaseCmd;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.command.SfOutboundApplyBaseCmd;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.command.SfOutboundCmd;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.query.SfSkuQry;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.query.SfStockQry;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpServerErrorException;

import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;
import java.util.List;

/**
 * 顺丰仓库RPC
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class SfWarehouseRpc {

    /**
     * 生产环境
     */
    private static final String DOMAIN = "http://bsp-oisp.sf-express.com/bsp-wms/OmsCommons";
    /**
     * 接入编码
     */
    private static final String ACCESS_CODE = "ldmqUWpPZdRm3lsBGNiHuw==";
    /**
     * client secret
     */
    private static final String CLIENT_SECRET = "qFpubwOAQ2a8CmMR2TcfT47tHWX1w02w";
    /**
     * 校验码
     */
    private static final String CHECK_WORK = "MQTjKpNXhR4MfT2JM43yplW14XRo5luW";
    /**
     * 货主编码
     */
    public static final String COMPANY_CODE = "W7698089145";

    /**
     * 实时库存查询返回值编码
     */
    private static final String RESPONSE_CODE_STOCK_QUERY = "RTInventorys";
    /**
     * 出库返回值编码
     */
    private static final String RESPONSE_CODE_OUT_BOUND = "SaleOrders";
    /**
     * 入库返回值编码
     */
    private static final String RESPONSE_CODE_IN_BOUND = "PurchaseOrders";

    /**
     * API：实时库存查询
     */
    private static final String API_STOCK_QUERY = "RT_INVENTORY_QUERY_SERVICE";
    /**
     * API：申请出库
     */
    private static final String API_OUT_BOUND = "SALE_ORDER_SERVICE";
    /**
     * API：取消出库
     */
    private static final String API_CANCEL_OUT_BOUND = "CancelSaleOrderRequest";
    /**
     * API：拦截出库
     */
    private static final String API_INTERCEPT_OUT_BOUND = "INTERCEPT_SALE_ORDER_SERVICE";
    /**
     * API：申请入库
     */
    private static final String API_IN_BOUND = "PURCHASE_ORDER_SERVICE";
    /**
     * API：取消入库
     */
    private static final String API_CANCEL_IN_BOUND = "CANCEL_PURCHASE_ORDER_SERVICE";

    /**
     * 查询库存
     *
     * @param qry 参数
     * @return 库存
     */
    public List<SfStockDTO> queryStock(SfStockQry qry) {
        SfWarehouseResponseBaseDTO responseBaseDTO = this.call(API_STOCK_QUERY, RESPONSE_CODE_STOCK_QUERY,
                qry, SfWarehouseResponseBaseDTO.class);

        List<SfStockDTO> listStockDTO = Lists.newArrayList();
        responseBaseDTO.getMsgData().getAsJsonArray().forEach(jsonElement -> {
            JsonObject jsonObject = jsonElement.getAsJsonObject();
            String header = jsonObject.get("Header").getAsString();
            SfStockDTO sfStockDTO = GsonUtil.jsonToBean(header, SfStockDTO.class);
            listStockDTO.add(sfStockDTO);
        });
        return listStockDTO;
    }

    /**
     * 出库申请
     *
     * @param cmd 参数
     * @return 平台出库单号
     */
    public String applyOutbound(SfOutboundApplyBaseCmd cmd) {
        SfWarehouseResponseBaseDTO responseBaseDTO = this.call(API_OUT_BOUND, RESPONSE_CODE_OUT_BOUND,
                cmd, SfWarehouseResponseBaseDTO.class);
        return responseBaseDTO.getMsgData().getAsJsonArray()
                .get(0).getAsJsonObject()
                .get("ShipmentId").getAsString();
    }

    /**
     * 取消出库
     *
     * @param cmd 参数
     * @return 执行结果
     */
    public boolean cancelOutbound(SfOutboundApplyBaseCmd cmd) {
        SfWarehouseResponseBaseDTO call = this.call(API_CANCEL_OUT_BOUND, RESPONSE_CODE_OUT_BOUND,
                cmd, SfWarehouseResponseBaseDTO.class);
        return call.success();
    }

    /**
     * 拦截出库
     *
     * @param cmd 参数
     * @return 执行结果
     */
    public boolean interceptOutBound(SfOutboundApplyBaseCmd cmd) {
        SfWarehouseResponseBaseDTO call = this.call(API_INTERCEPT_OUT_BOUND, RESPONSE_CODE_IN_BOUND,
                cmd, SfWarehouseResponseBaseDTO.class);
        return call.success();
    }

    /**
     * 申请入库
     *
     * @param cmd 参数
     * @return 平台入库单号
     */
    public String applyInbound(SfInboundApplyBaseCmd cmd) {
        SfWarehouseResponseBaseDTO responseBaseDTO = this.call(API_IN_BOUND, RESPONSE_CODE_IN_BOUND,
                cmd, SfWarehouseResponseBaseDTO.class);
        return responseBaseDTO.getMsgData().getAsJsonArray()
                .get(0).getAsJsonObject()
                .get("ReceiptId").getAsString();
    }

    /**
     * 取消入库
     *
     * @param cmd 参数
     * @return 执行结果
     */
    public boolean cancelInbound(SfInboundApplyBaseCmd cmd) {
        SfWarehouseResponseBaseDTO call = this.call(API_CANCEL_IN_BOUND, RESPONSE_CODE_IN_BOUND,
                cmd, SfWarehouseResponseBaseDTO.class);
        return call.success();
    }

    /**
     * 获取拦截出库参数xml
     *
     * @param orderList 订单号
     * @return xml
     */
    private static String getInterceptOutboundXml(List<SfOutboundCmd> orderList) {
        String headerXml = "<?xml version=\"1.0\" encoding=\"utf-8\"?><Request service=\""
                + API_INTERCEPT_OUT_BOUND
                + "\" lang=\"zh-CN\"><Head><AccessCode>"
                + ACCESS_CODE
                + "</AccessCode><Checkword>"
                + CHECK_WORK
                + "</Checkword></Head><Body>";

        // 组装订单号
        StringBuilder orderXml = new StringBuilder();
        for (SfOutboundCmd orderCmd : orderList) {
            orderXml.append("<SaleOrder><ErpOrder>")
                    .append(orderCmd.getOrderId())
                    .append("<SaleOrder><ErpOrder>");
        }

        headerXml += "<InterceptSaleOrderRequest><CompanyCode>"
                + COMPANY_CODE
                + "</CompanyCode><SaleOrders>"
                + orderXml
                + "</SaleOrders></InterceptSaleOrderRequest></Body></Request>";

        return headerXml;
    }

    /**
     * 接口调用
     *
     * @param serviceCode   接口编码
     * @param responseCode  返回值编码
     * @param request       请求参数
     * @param responseClass 响应类型
     * @param <R>           继承 SfWarehouseResponseBaseDTO 的类型
     * @return 返回值
     */
    private <T extends SfWarehouseBaseRequest, R extends SfWarehouseResponseBaseDTO> R call(String serviceCode,
                                                                                            String responseCode,
                                                                                            T request,
                                                                                            Class<R> responseClass) {
        // 构建请求参数封装
        MultiValueMap<String, Object> body = this.buildBody(serviceCode, request);

        // 发送请求
        String response = "";
        SfWarehouseResponseBaseDTO baseDTO;
        try {
            response = HttpUtil.postForForm(DOMAIN, body);
            baseDTO = GsonUtil.jsonToBean(response, SfWarehouseResponseBaseDTO.class);
            if (baseDTO.isFailure()) {
                throw new RemoteException("RPC返回异常状态码");
            }

            JsonObject jsonObject = GsonUtil.jsonToObject(response);
            String msgData = jsonObject.get(responseCode).toString();
            JsonElement jsonElement = GsonUtil.jsonToBean(msgData, JsonElement.class);
            baseDTO.setMsgData(jsonElement);
        } catch (Exception e) {
            if (e instanceof HttpServerErrorException) {
                response = ((HttpServerErrorException) e).getResponseBodyAsString();
            }
            String msg = String.format("顺丰仓库调用异常 response: %s", response);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, response, msg, e);
        }

        // 解析响应结果
        return this.parseResponse(response, responseClass);
    }

    /**
     * 解析响应结果
     *
     * @param response      响应结果
     * @param responseClass 响应类型
     * @param <T>           继承 SfWarehouseBaseDTO 的类型
     * @return 返回值
     */
    private <T extends SfWarehouseResponseBaseDTO> T parseResponse(String response, Class<T> responseClass) {
        try {
            // 解析业务返回值
            SfWarehouseResponseBaseDTO baseDTO = GsonUtil.jsonToBean(response, SfBaseDTO.class);
            boolean jsonToBean = responseClass != SfWarehouseResponseBaseDTO.class && baseDTO.getMsgData().isJsonObject();
            T resultDTO = jsonToBean ? GsonUtil.jsonToBean(baseDTO.getMsgData().toString(), responseClass)
                    : responseClass.newInstance();
            BeanUtils.copyProperties(baseDTO, resultDTO);
            return resultDTO;
        } catch (Exception e) {
            String msg = String.format("顺丰仓库响应结果解析异常 response: %s", response);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, response, msg, e);
        }
    }

    /**
     * 构建请求参数封装
     *
     * @param serviceCode 接口编码
     * @param request     请求参数
     * @return 请求参数封装
     */
    private <T extends SfWarehouseBaseRequest> MultiValueMap<String, Object> buildBody(String serviceCode, T request) {
        try {
            request.setServiceCode(serviceCode);
            request.setAccessCode(ACCESS_CODE);
            request.setCheckWord(CHECK_WORK);
            request.setCompanyCode(COMPANY_CODE);
            String msgData = GsonUtil.objectToJson(request);
            String msgDigest = this.buildMsgDigest(msgData, CLIENT_SECRET);

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("logistics_interface", msgData);
            body.add("data_digest", msgDigest);
            return body;
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, "顺丰仓库调用异常，组装参数异常", e);
        }
    }

    /**
     * 生成签名
     *
     * @param msgData      业务数据
     * @param clientSecret 客户密钥
     * @return 签名
     */
    private String buildMsgDigest(String msgData, String clientSecret) throws NoSuchAlgorithmException {
        String plantText = msgData + clientSecret;
        String urlEncode = HttpUtil.urlEncode(plantText);
        byte[] md5 = this.md5Encode(urlEncode);
        return HttpUtil.base64Encode(md5);
    }

    /**
     * md5加密
     *
     * @param encryptStr 加密字符串
     * @return 密文字节
     */
    private byte[] md5Encode(String encryptStr) throws NoSuchAlgorithmException {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        md5.update(encryptStr.getBytes(StandardCharsets.UTF_8));
        return md5.digest();
    }

    public static void main(String[] args) {
        SfStockQry stockQry = SfStockQry.builder()
                .warehouseCode("xxx")
                .companyCode(COMPANY_CODE)
                .accessCode(ACCESS_CODE)
                .checkWord(CHECK_WORK)
                .build();

        SfSkuQry skuQry = SfSkuQry.builder()
                .skuId("xxx")
                .build();
        stockQry.setSkus(Collections.singletonList(skuQry));

        SfWarehouseRpc rpc = new SfWarehouseRpc();
        List<SfStockDTO> sfStockList = rpc.queryStock(stockQry);
        System.out.println(GsonUtil.objectToJson(sfStockList));
    }

}
