package com.xtc.marketing.adapterservice.rpc.sf.sfdto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class SfOrderDTO extends SfBaseDTO {

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 路由标签
     */
    private List<RouteLabelInfo> routeLabelInfo;

    /**
     * 路由标签
     */
    @Getter
    @Setter
    @ToString
    public static class RouteLabelInfo {

        /**
         * 返回调用结果，1000：调用成功； 其他调用失败
         */
        private String code;

        /**
         * 路由标签数据详细数据，除少量特殊场景用户外，其余均会返回
         */
        private RouteLabelData routeLabelData;

        /**
         * 失败异常描述
         */
        private String message;

    }

    /**
     * 路由标签数据详细数据
     */
    @Getter
    @Setter
    @ToString
    public static class RouteLabelData {

        /**
         * 运单号
         */
        private String waybillNo;

        /**
         * 入港映射码
         */
        private String codingMapping;

        /**
         * 出港映射码
         */
        private String codingMappingOut;

        /**
         * 顺丰面单路由标签信息
         */
        private String destRouteLabel;

        /**
         * 时效类型
         */
        private String proCode;

        /**
         * 顺丰面单二维码
         */
        private String twoDimensionCode;

    }

}
