package com.xtc.marketing.adapterservice.rpc.orderservice;

import com.alibaba.excel.annotation.ExcelProperty;
import com.google.common.collect.Lists;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.orderservice.xtcshopdto.XtcShopDecryptDTO;
import com.xtc.marketing.adapterservice.rpc.orderservice.xtcshopdto.query.XtcShopEncryptQry;
import com.xtc.marketing.adapterservice.util.BeanCopier;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import jodd.io.FileUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpServerErrorException;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.rmi.RemoteException;
import java.util.List;

/**
 * 官方商城RPC
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class XtcShopRpc {

    /**
     * 官方商城卖家端地址
     */
    private static final String DOMAIN_XTC_SHOP_SELLER = "https://xtc-shop-seller.okii.com";

    /**
     * 解密
     *
     * @param qry 参数
     * @return 解密数据
     */
    public XtcShopDecryptDTO xtcShopOrderDecrypt(XtcShopEncryptQry qry) {
        String url = DOMAIN_XTC_SHOP_SELLER + "/api/order/decryptSensitiveData";
        String responseStr = "";
        try {
            responseStr = HttpUtil.post(url, qry);

            SingleResponse<XtcShopDecryptDTO> response = GsonUtil.jsonToBean(responseStr,
                    SingleResponse.class, XtcShopDecryptDTO.class);
            if (response.isFailure()) {
                throw new RemoteException("RPC返回异常状态码");
            }
            return response.getData();
        } catch (Exception e) {
            if (e instanceof HttpServerErrorException) {
                responseStr = ((HttpServerErrorException) e).getResponseBodyAsString();
            }
            String msg = String.format("官方商城调用异常 response: %s, url: %s", responseStr, url);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
        }
    }

    /**
     * 数据导出申请处理
     */
    public static void main(String[] args) throws Exception {
        // 读取数据
        List<ExportDTO> exportDataList = Lists.newArrayList();
        InputStream fileInputStream = Files.newInputStream(Paths.get("D:\\文档\\Desktop\\Untitled.xlsx"));
        EasyExcelUtil.readExcel(fileInputStream, ImportDTO.class, importDataList -> {
            for (ImportDTO importDTO : importDataList) {
                ExportDTO exportDTO = BeanCopier.copy(importDTO, ExportDTO::new);
                exportDataList.add(exportDTO);
            }
        });
        // 解密数据
        XtcShopRpc xtcShopRpc = new XtcShopRpc();
        for (ExportDTO exportDTO : exportDataList) {
            XtcShopEncryptQry decryptQry = XtcShopEncryptQry.builder()
                    .receiverName(exportDTO.getReceiverName())
                    .receiverMobile(exportDTO.getReceiverMobile())
                    .receiverAddress(exportDTO.getReceiverAddress())
                    .build();
            XtcShopDecryptDTO xtcShopDecryptDTO = xtcShopRpc.xtcShopOrderDecrypt(decryptQry);
            exportDTO.setReceiverName(xtcShopDecryptDTO.getReceiverName());
            exportDTO.setReceiverMobile(xtcShopDecryptDTO.getReceiverMobile());
            exportDTO.setReceiverAddress(xtcShopDecryptDTO.getReceiverAddress());
        }
        // 导出数据
        byte[] fileBytes = EasyExcelUtil.writeExcel(ExportDTO.class, exportDataList);
        File exportFile = FileUtil.file("D:\\文档\\Desktop\\数据导出申请EXPT2409001.xlsx");
        FileUtil.writeBytes(exportFile, fileBytes);
    }

    @Data
    public static class ImportDTO {

        @ExcelProperty("订单号")
        private String orderNo;
        @ExcelProperty("订单状态")
        private String orderState;
        @ExcelProperty("创建时间")
        private String createTime;
        @ExcelProperty("付款时间")
        private String payTime;
        @ExcelProperty("收件人姓名")
        private String receiverName;
        @ExcelProperty("收件人电话")
        private String receiverMobile;
        @ExcelProperty("收件人地址")
        private String receiverAddress;
        @ExcelProperty("商品名称 + SKU名称")
        private String skuName;

    }

    @Data
    public static class ExportDTO {

        @ExcelProperty("订单号")
        private String orderNo;
        @ExcelProperty("订单状态")
        private String orderState;
        @ExcelProperty("创建时间")
        private String createTime;
        @ExcelProperty("付款时间")
        private String payTime;
        @ExcelProperty("收件人姓名")
        private String receiverName;
        @ExcelProperty("收件人电话")
        private String receiverMobile;
        @ExcelProperty("收件人地址")
        private String receiverAddress;
        @ExcelProperty("商品名称 + SKU名称")
        private String skuName;

    }

}
