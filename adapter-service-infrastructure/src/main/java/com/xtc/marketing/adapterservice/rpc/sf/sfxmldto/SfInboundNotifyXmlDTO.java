package com.xtc.marketing.adapterservice.rpc.sf.sfxmldto;

import com.google.common.collect.Lists;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamConverter;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import com.xtc.marketing.adapterservice.rpc.sf.util.IntFromStringConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 顺丰仓库，入库通知
 */
@Getter
@Setter
@ToString
@XStreamAlias("PurchaseOrder")
public class SfInboundNotifyXmlDTO extends SfNotifyXmlDTO {

    /**
     * 仓库编码
     */
    @XStreamAlias("WarehouseCode")
    private String warehouseCode;

    /**
     * 入库单号（业务方单号）
     */
    @XStreamAlias("ErpOrder")
    private String orderId;

    /**
     * 顺丰入库单号（顺丰单号）
     */
    @XStreamAlias("ReceiptId")
    private String receiptId;

    /**
     * 入库单类型
     */
    @XStreamAlias("ErpOrderType")
    private String orderType;

    /**
     * 顺丰入库单类型
     */
    @XStreamAlias("SfOrderType")
    private String receiptOrderType;

    /**
     * 订单状态
     * <pre>
     *     3000: 部分收货
     *     3900: 收货完成
     * </pre>
     */
    @XStreamAlias("Status")
    private String orderStatus;

    /**
     * 供应商编号
     */
    @XStreamAlias("VendorCode")
    private String vendorCode;

    /**
     * 货物集合
     */
    @XStreamAlias("Items")
    private List<Item> items;

    @Getter
    @Setter
    @ToString
    @XStreamAlias("Item")
    public static class Item {

        /**
         * 商品编号
         */
        @XStreamAlias("SkuNo")
        private String skuId;

        /**
         * 商品名称
         */
        @XStreamAlias("SkuName")
        private String skuName;

        /**
         * 库存状态
         * 10:正品
         * 20:残品
         */
        @XStreamAlias("InventoryStatus")
        private String inventoryStatus;

        /**
         * 收货时间（格式：YYYY-MM-DD HH24:MI:SS）
         */
        @XStreamAlias("ReceiptTime")
        private String receiptTime;

        /**
         * 计划数量
         */
        @XStreamConverter(value = IntFromStringConverter.class)
        @XStreamAlias("PlanQty")
        private Integer planQuantity;

        /**
         * 实收数量
         */
        @XStreamConverter(value = IntFromStringConverter.class)
        @XStreamAlias("ActualQty")
        private Integer actualQuantity;

        /**
         * 拒收数量
         */
        @XStreamConverter(value = IntFromStringConverter.class)
        @XStreamAlias("RejectionQty")
        private Integer rejectionQuantity;

        /**
         * 序列号集合（产品条码）
         */
        @XStreamAlias("SerialNumbers")
        private SerialNumbers serialNumbers;

    }

    @Getter
    @Setter
    @ToString
    @XStreamAlias("SerialNumbers")
    public static class SerialNumbers {

        /**
         * 序列号集合（产品条码）
         */
        @XStreamImplicit(itemFieldName = "SerialNumber")
        private List<String> serialNumber;

    }

    private static final List<String> NOTIFY_PARSE_ELEMENTS =
            Lists.newArrayList("PurchaseOrderInboundRequest", "PurchaseOrders", "PurchaseOrder");

    @Override
    public List<String> getNotifyParseElements() {
        return NOTIFY_PARSE_ELEMENTS;
    }

    @Override
    public String getNotifyResponse() {
        return this.buildNotifyResponse("PURCHASE_ORDER_INBOUND_PUSH_SERVICE",
                "PurchaseOrderInboundResponse");
    }

    /**
     * 获取入库单状态文本
     *
     * @return 入库单状态文本
     */
    public String getOrderStatusText() {
        switch (this.orderStatus) {
            case "3000":
                return "部分收货";
            case "3900":
                return "收货完成";
            default:
                return this.orderStatus;
        }
    }

}
