package com.xtc.marketing.adapterservice.rpc.sto.stodto.command;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 申通客户信息
 */
@Getter
@Setter
@ToString
public class StoCustomerCmd {

    /**
     * 网点编码
     */
    private String siteCode;
    /**
     * 客户编码
     */
    private String customerName;
    /**
     * 电子面单密码
     */
    @SerializedName("sitePwd")
    private String logisticsOrderPassword;

}
