package com.xtc.marketing.adapterservice.rpc.pdd;

import com.google.common.collect.Lists;
import com.pdd.pop.sdk.http.PopBaseHttpRequest;
import com.pdd.pop.sdk.http.PopBaseHttpResponse;
import com.pdd.pop.sdk.http.PopHttpClient;
import com.pdd.pop.sdk.http.api.pop.request.*;
import com.pdd.pop.sdk.http.api.pop.response.*;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.pdd.pdddto.PddFdsWaybillGetRequest;
import com.xtc.marketing.adapterservice.rpc.pdd.pdddto.PddInvoiceDetailUploadRequest;
import com.xtc.marketing.adapterservice.rpc.pdd.pdddto.PddWaybillGetRequest;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.rmi.RemoteException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 拼多多商城(PDD)RPC
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class PddRpc {

    /**
     * PopHttpClient 是线程安全的，所以没有必要每次请求都 new 对象，设置缓存减少对象生成
     */
    private static final Map<String, PopHttpClient> CLIENT_CACHE = new ConcurrentHashMap<>();
    /**
     * 商家优惠类型
     */
    private static final List<Integer> SELLER_PROMOTION_TYPE = Lists.newArrayList(30);

    /**
     * 判断商家优惠类型
     *
     * @param promotionType 优惠类型
     * @return 执行结果
     */
    public static boolean isSellerPromotionType(int promotionType) {
        return SELLER_PROMOTION_TYPE.contains(promotionType);
    }

    /**
     * 获取 AccessToken
     *
     * @param shop      店铺
     * @param grantCode 授权code
     * @return AccessToken
     */
    public PddPopAuthTokenCreateResponse getAccessToken(ShopDO shop, String grantCode) {
        PddPopAuthTokenCreateRequest request = new PddPopAuthTokenCreateRequest();
        request.setCode(grantCode);
        return this.call(shop, request);
    }

    /**
     * 刷新 AccessToken
     * <p>刷新授权不会延长 AccessToken 的有效期，只能当作重新查询 AccessToken 数据</p>
     *
     * @param shop 店铺
     * @return AccessToken
     */
    public PddPopAuthTokenRefreshResponse refreshAccessToken(ShopDO shop) {
        PddPopAuthTokenRefreshRequest request = new PddPopAuthTokenRefreshRequest();
        request.setRefreshToken(shop.getAppRefreshToken());
        return this.call(shop, request);
    }

    /**
     * 订单列表查询
     *
     * @param shop    店铺
     * @param request 参数
     * @return 执行结果
     */
    public PddOrderNumberListIncrementGetResponse pageOrders(ShopDO shop, PddOrderNumberListIncrementGetRequest request) {
        return this.call(shop, request);
    }

    /**
     * 订单列表查询（代发）
     *
     * @param shop    店铺
     * @param request 参数
     * @return 执行结果
     */
    public PddFdsOrderListGetResponse.InnerPddFdsOrderListGetResponse pageOrdersDistr(ShopDO shop, PddFdsOrderListGetRequest request) {
        return this.call(shop, request).getPddFdsOrderListGetResponse();
    }

    /**
     * 订单详情查询
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 执行结果
     */
    public PddOrderInformationGetResponse.OrderInfoGetResponseOrderInfo getOrder(ShopDO shop, String orderNo) {
        PddOrderInformationGetRequest request = new PddOrderInformationGetRequest();
        request.setOrderSn(orderNo);
        PddOrderInformationGetResponse response = this.call(shop, request);
        return response.getOrderInfoGetResponse().getOrderInfo();
    }

    /**
     * 查询订单详情（代发）
     *
     * @param shop         店铺
     * @param distrOrderNo 代发订单号
     * @param distrShopId  代发店铺id
     * @return 订单详情
     */
    public PddFdsOrderGetResponse.InnerPddFdsOrderGetResponse getOrderDistr(ShopDO shop, String distrOrderNo, String distrShopId) {
        PddFdsOrderGetRequest request = new PddFdsOrderGetRequest();
        PddFdsOrderGetRequest.ParamFdsOrderGetRequest paramFdsOrderGetRequest = new PddFdsOrderGetRequest.ParamFdsOrderGetRequest();
        paramFdsOrderGetRequest.setMallMaskId(distrShopId);
        paramFdsOrderGetRequest.setOrderMaskSn(distrOrderNo);
        request.setParamFdsOrderGetRequest(paramFdsOrderGetRequest);
        PddFdsOrderGetResponse response = this.call(shop, request);
        return response.getPddFdsOrderGetResponse();
    }

    /**
     * 退款列表查询
     *
     * @param shop    店铺
     * @param request 参数
     * @return 执行结果
     */
    public PddRefundListIncrementGetResponse.RefundIncrementGetResponse pageRefunds(ShopDO shop, PddRefundListIncrementGetRequest request) {
        PddRefundListIncrementGetResponse response = this.call(shop, request);
        return response.getRefundIncrementGetResponse();
    }

    /**
     * 退款详情查询
     *
     * @param shop     店铺
     * @param orderNo  订单号
     * @param refundId 退款单号
     * @return 执行结果
     */
    public PddRefundInformationGetResponse getRefund(ShopDO shop, String orderNo, Long refundId) {
        PddRefundInformationGetRequest request = new PddRefundInformationGetRequest();
        request.setOrderSn(orderNo);
        request.setAfterSalesId(refundId);
        return this.call(shop, request);
    }

    /**
     * 物流发货
     * <p>物流公司编码 <a href="https://open.pinduoduo.com/application/document/api?id=pdd.logistics.companies.get">文档</a></p>
     *
     * @param shop    店铺
     * @param request 参数
     * @return 执行结果
     */
    public boolean orderShipping(ShopDO shop, PddLogisticsOnlineSendRequest request) {
        PddLogisticsOnlineSendResponse response = this.call(shop, request);
        return response.getLogisticsOnlineSendResponse().getIsSuccess();
    }

    /**
     * 物流发货(代发)
     *
     * @param relationShop     关联店铺
     * @param agentCode        代理店铺代码
     * @param orderNo          订单号
     * @param logisticsCompany 物流公司编码
     * @param waybillNo        运单号
     * @return 执行结果
     */
    public boolean orderShippingDistr(ShopDO relationShop, String agentCode, String orderNo, String logisticsCompany, String waybillNo) {
        PddFdsWaybillReturnRequest.ParamFdsWaybillReturnRequest request = new PddFdsWaybillReturnRequest.ParamFdsWaybillReturnRequest();
        request.setMallMaskId(agentCode);
        request.setOrderMaskSn(orderNo);
        request.setWaybillCode(waybillNo);
        request.setWpCode(logisticsCompany);
        PddFdsWaybillReturnRequest requestParqam = new PddFdsWaybillReturnRequest();
        requestParqam.setParamFdsWaybillReturnRequest(request);
        PddFdsWaybillReturnResponse response = this.call(relationShop, requestParqam);
        return response.getPddFdsWaybillReturnResponse().getReturnResult();
    }

    /**
     * 取消物流发货
     *
     * @param shop     店铺
     * @param orderNo  订单号
     * @param refundId 退款单号
     * @return 执行结果
     */
    public boolean orderShippingCancel(ShopDO shop, String orderNo, String refundId) {
        PddRdcPddgeniusSendgoodsCancelRequest request = new PddRdcPddgeniusSendgoodsCancelRequest();
        PddRdcPddgeniusSendgoodsCancelRequest.Param param = new PddRdcPddgeniusSendgoodsCancelRequest.Param();
        param.setTid(orderNo);
        param.setRefundId(Long.parseLong(refundId));
        param.setStatus("SUCCESS");
        request.setParam(param);
        PddRdcPddgeniusSendgoodsCancelResponse response = this.call(shop, request);
        return Optional.ofNullable(response)
                .map(PddRdcPddgeniusSendgoodsCancelResponse::getRdcPddgeniusSendgoodsCancelResponse)
                .map(PddRdcPddgeniusSendgoodsCancelResponse.RdcPddgeniusSendgoodsCancelResponse::getResult)
                .map(PddRdcPddgeniusSendgoodsCancelResponse.RdcPddgeniusSendgoodsCancelResponseResult::getResultData)
                .map(PddRdcPddgeniusSendgoodsCancelResponse.RdcPddgeniusSendgoodsCancelResponseResultResultData::getRefundId)
                .map(ObjectUtils::isNotEmpty)
                .orElse(false);
    }

    /**
     * 备注
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @param remark  备注
     * @return 执行结果
     */
    public boolean orderRemark(ShopDO shop, String orderNo, String remark) {
        PddOrderNoteUpdateRequest request = new PddOrderNoteUpdateRequest();
        request.setOrderSn(orderNo);
        request.setNote(remark);
        request.setTag(4);
        request.setTagName("erp");
        PddOrderNoteUpdateResponse response = this.call(shop, request);
        return response.getResponse().getSuccess();
    }

    /**
     * 订单解密
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @param ciphers 密文数据列表, 默认列表大小不超过100
     * @return 执行结果
     */
    public PddOpenDecryptBatchResponse orderDecrypt(ShopDO shop, String orderNo, List<String> ciphers) {
        // 密文数据转换
        List<PddOpenDecryptBatchRequest.DataListItem> items = ciphers.stream()
                .filter(StringUtils::isNotBlank)
                .map(cipher -> {
                    PddOpenDecryptBatchRequest.DataListItem item = new PddOpenDecryptBatchRequest.DataListItem();
                    item.setDataTag(orderNo);
                    item.setEncryptedData(cipher);
                    return item;
                })
                .collect(Collectors.toList());

        PddOpenDecryptBatchRequest request = new PddOpenDecryptBatchRequest();
        request.setDataList(items);
        return this.call(shop, request);
    }

    /**
     * 分页查询发票申请
     *
     * @param shop    店铺
     * @param request 参数
     * @return 发票申请
     */
    public List<PddInvoiceApplicationQueryResponse.InvoiceApplicationQueryResponseInvoiceApplicationListItem> pageInvoiceApply(
            ShopDO shop,
            PddInvoiceApplicationQueryRequest request
    ) {
        PddInvoiceApplicationQueryResponse response = this.call(shop, request);
        return Optional.ofNullable(response)
                .map(PddInvoiceApplicationQueryResponse::getInvoiceApplicationQueryResponse)
                .map(PddInvoiceApplicationQueryResponse.InvoiceApplicationQueryResponse::getInvoiceApplicationList)
                .orElse(null);
    }

    /**
     * 查询发票申请
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 发票申请
     */
    public PddInvoiceApplicationQueryResponse.InvoiceApplicationQueryResponseInvoiceApplicationListItem getInvoiceApply(ShopDO shop, String orderNo) {
        PddInvoiceApplicationQueryRequest request = new PddInvoiceApplicationQueryRequest();
        request.setOrderSn(orderNo);
        PddInvoiceApplicationQueryResponse response = this.call(shop, request);
        return Optional.ofNullable(response)
                .map(PddInvoiceApplicationQueryResponse::getInvoiceApplicationQueryResponse)
                .map(PddInvoiceApplicationQueryResponse.InvoiceApplicationQueryResponse::getInvoiceApplicationList)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.get(0))
                .orElse(null);
    }

    /**
     * 生成电子面单
     *
     * @param shop    店铺
     * @param request 参数
     * @return 电子面单
     */
    public PddWaybillGetResponse.InnerPddWaybillGetResponseModulesItem createLogisticsOrder(ShopDO shop, PddWaybillGetRequest request) {
        PddWaybillGetResponse response = this.call(shop, request);
        return Optional.ofNullable(response)
                .map(PddWaybillGetResponse::getPddWaybillGetResponse)
                .map(PddWaybillGetResponse.InnerPddWaybillGetResponse::getModules)
                .filter(CollectionUtils::isNotEmpty)
                .map(modules -> modules.get(0))
                .orElseThrow(() -> this.rpcSysException(response));
    }

    /**
     * 生成电子面单（代发）
     *
     * @param shop    店铺
     * @param request 参数
     * @return 电子面单
     */
    public PddFdsWaybillGetResponse.InnerPddFdsWaybillGetResponseModulesItem createLogisticsOrderDistr(ShopDO shop, PddFdsWaybillGetRequest request) {
        PddFdsWaybillGetResponse response = this.call(shop, request);
        return Optional.ofNullable(response)
                .map(PddFdsWaybillGetResponse::getPddFdsWaybillGetResponse)
                .map(PddFdsWaybillGetResponse.InnerPddFdsWaybillGetResponse::getModules)
                .filter(CollectionUtils::isNotEmpty)
                .map(modules -> modules.get(0))
                .orElseThrow(() -> this.rpcSysException(response));
    }

    /**
     * 发票上传
     *
     * @param shop    店铺
     * @param request 请求参数
     */
    public boolean invoiceUpload(ShopDO shop, PddInvoiceDetailUploadRequest request) {
        PddInvoiceDetailUploadResponse response = this.call(shop, request);
        String serialNo = response.getInvoiceDetailUploadResponse().getSerialNo();
        return StringUtils.isNotBlank(serialNo);
    }

    /**
     * 退货确认入仓
     *
     * @param shop      店铺
     * @param orderNo   订单号
     * @param refundId  退款单号
     * @param waybillNo 运单号
     */
    public void refundGoodsToWarehouse(ShopDO shop, String orderNo, String refundId, String waybillNo) {
        PddNextoneLogisticsWarehouseUpdateRequest.Request param = new PddNextoneLogisticsWarehouseUpdateRequest.Request();
        param.setAfterSalesId(Long.parseLong(refundId));
        param.setOperateTime(DateUtil.nowEpochSecond());
        param.setOrderSn(orderNo);
        param.setReverseTrackingNumber(waybillNo);
        param.setWarehouseStatus(1);
        PddNextoneLogisticsWarehouseUpdateRequest request = new PddNextoneLogisticsWarehouseUpdateRequest();
        request.setRequest(param);
        this.call(shop, request);
    }

    /**
     * 同意退款
     *
     * @param shop     店铺
     * @param orderNo  订单号
     * @param refundId 退款单号
     */
    public void autoRefund(ShopDO shop, String orderNo, String refundId) {
        PddRefundAgreeRequest.Request pddRefundAgreeRequest = new PddRefundAgreeRequest.Request();
        pddRefundAgreeRequest.setAfterSalesId(Long.parseLong(refundId));
        pddRefundAgreeRequest.setOperateDesc("系统同意退款");
        pddRefundAgreeRequest.setOrderSn(orderNo);
        PddRefundAgreeRequest request = new PddRefundAgreeRequest();
        request.setRequest(pddRefundAgreeRequest);
        PddRefundAgreeResponse response = this.call(shop, request);
        boolean success = Optional.ofNullable(response)
                .map(PddRefundAgreeResponse::getResponse)
                .map(PddRefundAgreeResponse.Response::getResult)
                .map(PddRefundAgreeResponse.ResponseResult::getSucc)
                .orElse(false);
        if (!success) {
            throw this.rpcSysException(response);
        }
    }

    /**
     * 查询政府补贴
     *
     * @param shop    店铺
     * @param orderId 订单号
     * @return 补贴
     */
    public PddOrderTradeinInfoResponse getNationalSubsidy(ShopDO shop, String orderId) {
        PddOrderTradeinInfoRequest request = new PddOrderTradeinInfoRequest();
        request.setOrderSnList(Collections.singletonList(orderId));
        return this.call(shop, request);
    }

    /**
     * 执行远程调用
     *
     * @param shop    店铺
     * @param request 参数
     * @param <T>     返回参数类型
     * @return 执行结果
     */
    public <T extends PopBaseHttpResponse> T call(ShopDO shop, PopBaseHttpRequest<T> request) {
        log.info("拼多多参数: {}", GsonUtil.objectToJson(request));
        String responseStr = "";
        try {
            T response = this.getClient(shop).syncInvoke(request, shop.getAppAccessToken());

            responseStr = GsonUtil.objectToJson(response);
            log.info("拼多多返回值: {}", responseStr);

            if (response.getErrorResponse() != null) {
                throw new RemoteException("RPC返回异常状态码");
            }
            return response;
        } catch (Exception e) {
            throw this.rpcSysException(responseStr, e);
        }
    }

    /**
     * 获取请求客户端
     * <p>PopHttpClient 是线程安全的，所以没有必要每次请求都 new 对象，设置缓存减少对象生成</p>
     *
     * @param shop 店铺
     * @return 客户端
     */
    private PopHttpClient getClient(ShopDO shop) {
        return CLIENT_CACHE.computeIfAbsent(shop.getShopCode(),
                key -> new PopHttpClient(shop.getAppKey(), shop.getAppSecret()));
    }

    /**
     * 远程调用异常
     *
     * @param response 响应
     * @return 异常
     */
    private SysException rpcSysException(Object response) {
        String responseStr = GsonUtil.objectToJson(response);
        String msg = String.format("拼多多平台调用异常 response: %s", responseStr);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg);
    }

    /**
     * 远程调用异常
     *
     * @param responseStr 响应结果
     * @param e           异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, Exception e) {
        String msg = String.format("拼多多平台调用异常 response: %s", responseStr);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
    }

    public static void main(String[] args) {
        PddRpc rpc = new PddRpc();
        ShopDO shop = ShopDO.builder()
                .shopCode("PDD_XTC")
                .appKey("xxxxx")
                .appSecret("xxxxx")
                .appAccessToken("xxxxx")
                .appRefreshToken("xxxxx")
                .build();
        rpc.getOrder(shop, "240124-609982893030761");
    }

}
