package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.query;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 微信视频号刷新token
 */
@Getter
@Setter
@ToString
public class WechatChannelsShopRefreshTokenQry {

    /**
     * 授权类型
     */
    @SerializedName("grant_type")
    private String type = "client_credential";
    /**
     * 账号唯一凭证，即 AppID
     */
    private String appid;
    /**
     * 账号唯一凭证密钥
     */
    private String secret;
    /**
     * 强制刷新，默认使用 false。
     * <P>1. force_refresh = false 时为普通调用模式，access_token 有效期内重复调用该接口不会更新 access_token；<P/>
     * <P>2. 当force_refresh = true 时为强制刷新模式，会导致上次获取的 access_token 失效，并返回新的 access_token<P/>
     */
    @SerializedName("force_refresh")
    private boolean forceRefresh;

}
