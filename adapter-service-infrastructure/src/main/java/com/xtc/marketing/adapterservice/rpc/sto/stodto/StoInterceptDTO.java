package com.xtc.marketing.adapterservice.rpc.sto.stodto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 申通拦截
 */
@Getter
@Setter
@ToString
public class StoInterceptDTO extends StoBaseDTO {

    /**
     * 运单号
     */
    private String waybillNo;
    /**
     * 是否新增成功
     */
    private Boolean isAddSuccess;
    /**
     * 错误描述
     */
    private String errorReason;
    /**
     * 任务id
     */
    private String taskId;

}
