package com.xtc.marketing.adapterservice.rpc.tenserpay;

import com.google.common.collect.Maps;
import com.google.gson.JsonObject;
import com.xtc.dividendcenter.dividend.dto.InnerMchInfoTenserDTO;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.tenserpay.tenserpaydto.TenserPayBaseDTO;
import com.xtc.marketing.adapterservice.rpc.tenserpay.tenserpaydto.TenserPayRefundDTO;
import com.xtc.marketing.adapterservice.rpc.tenserpay.tenserpaydto.command.TenserPayOrderCmd;
import com.xtc.marketing.adapterservice.rpc.tenserpay.tenserpaydto.command.TenserPayRefundCmd;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import com.xtc.marketing.adapterservice.util.RsaUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.SortedMap;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 腾盛支付RPC
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class TenserPayRpc {

    /**
     * 时间格式
     */
    public static final String DATE_PATTERN = "yyyyMMddHHmmss";
    /**
     * 腾盛支付域名，正式域名
     */
    private static final String TENSER_PAY_DOMAIN = "https://pay-api.tenserpay.com";
    /**
     * 渠道号
     */
    private static final String ORG_CODE = "okiionline";

    /**
     * 聚合码支付H5
     */
    private static final String API_PRE_H5_PAY = "/trans/pay/preH5Pay";
    /**
     * 聚合支付 - 直接支付
     */
    private static final String API_JS_API_PAY = "/trans/pay/jsApiPay";
    /**
     * 小程序支付 - 直接支付
     */
    private static final String API_MINI_PROGRAM_PAY = "/trans/pay/miniProgramPay";
    /**
     * 订单查询
     */
    private static final String API_ORDER_QUERY = "/trans/order/query";
    /**
     * 订单关闭支付
     */
    private static final String API_ORDER_PAY_CLOSE = "/trans/order/payClose";
    /**
     * 退款
     */
    private static final String API_REFUND = "/trans/pay/refund";
    /**
     * 退款查询
     */
    private static final String API_REFUND_QUERY = "/trans/refundOrder/query";

    /**
     * 聚合码支付H5
     *
     * @param mch 商户
     * @param cmd 下单参数
     * @return 收款的聚合二维码信息
     */
    public TenserPayBaseDTO preH5Pay(InnerMchInfoTenserDTO mch, TenserPayOrderCmd cmd) {
        return this.call(mch, API_PRE_H5_PAY, cmd);
    }

    /**
     * 聚合支付 - 直接支付
     *
     * @param mch 商户
     * @param cmd 下单参数
     * @return 预支付参数
     */
    public TenserPayBaseDTO jsApiPay(InnerMchInfoTenserDTO mch, TenserPayOrderCmd cmd) {
        return this.call(mch, API_JS_API_PAY, cmd);
    }

    /**
     * 小程序支付 - 直接支付
     *
     * @param mch 商户
     * @param cmd 下单参数
     * @return 预支付参数
     */
    public TenserPayBaseDTO miniProgramPay(InnerMchInfoTenserDTO mch, TenserPayOrderCmd cmd) {
        return this.call(mch, API_MINI_PROGRAM_PAY, cmd);
    }

    /**
     * 订单查询
     *
     * @param mch        商户
     * @param outOrderNo 商户订单号
     * @return 订单
     */
    public TenserPayBaseDTO orderQuery(InnerMchInfoTenserDTO mch, String outOrderNo) {
        Map<String, String> params = Maps.newHashMapWithExpectedSize(1);
        params.put("outOrderNo", outOrderNo);
        return this.call(mch, API_ORDER_QUERY, params);
    }

    /**
     * 订单关闭支付
     *
     * @param mch        商户
     * @param outOrderNo 商户订单号
     * @return 执行结果
     */
    public TenserPayBaseDTO orderPayClose(InnerMchInfoTenserDTO mch, String outOrderNo) {
        Map<String, String> params = Maps.newHashMapWithExpectedSize(1);
        params.put("outOrderNo", outOrderNo);
        return this.call(mch, API_ORDER_PAY_CLOSE, params);
    }

    /**
     * 退款
     *
     * @param mch 商户
     * @param cmd 下单参数
     * @return 退款结果
     */
    public TenserPayBaseDTO refund(InnerMchInfoTenserDTO mch, TenserPayRefundCmd cmd) {
        // 商户退款订单号，默认随机生成
        String outRefundOrderNo = StringUtils.defaultIfBlank(cmd.getOutRefundOrderNo(), this.uuid());
        cmd.setOutRefundOrderNo(outRefundOrderNo);
        return this.call(mch, API_REFUND, cmd);
    }

    /**
     * 退款订单查询
     *
     * @param mch              商户
     * @param refundOrderNo    平台退款订单号
     * @param outRefundOrderNo 商户退款订单号
     * @return 退款订单
     */
    public TenserPayRefundDTO refundQuery(InnerMchInfoTenserDTO mch, String refundOrderNo, String outRefundOrderNo) {
        Map<String, String> params = Maps.newHashMapWithExpectedSize(2);
        params.put("refundOrderNo", refundOrderNo);
        params.put("outRefundOrderNo", outRefundOrderNo);
        return this.call(mch, API_REFUND_QUERY, params, TenserPayRefundDTO.class);
    }

    /**
     * 验证平台数据
     *
     * @param mch  商户
     * @param data 平台数据（请求的返回值，回调的参数）
     * @return 接口返回值
     */
    public TenserPayBaseDTO verifyPlatformData(InnerMchInfoTenserDTO mch, String data) {
        return this.verifyPlatformData(mch, data, TenserPayBaseDTO.class);
    }

    /**
     * 接口统一请求入口
     *
     * @param mch          商户
     * @param api          接口
     * @param paramsObject 接口参数
     * @return 返回值
     */
    private TenserPayBaseDTO call(InnerMchInfoTenserDTO mch, String api, Object paramsObject) {
        return this.call(mch, api, paramsObject, TenserPayBaseDTO.class);
    }

    /**
     * 接口统一请求入口
     *
     * @param mch           商户
     * @param api           接口
     * @param paramsObject  接口参数
     * @param responseClass 响应类型
     * @param <T>           继承 TenserPayBaseDTO 的类型
     * @return 返回值
     */
    private <T extends TenserPayBaseDTO> T call(InnerMchInfoTenserDTO mch, String api, Object paramsObject, Class<T> responseClass) {
        // 组装参数
        String url = TENSER_PAY_DOMAIN + api;
        String params = paramsObject instanceof String ? paramsObject.toString() : GsonUtil.objectToJson(paramsObject);
        Map<String, Object> body = this.getRequestBody(params, mch.getMchPrivateKey());

        // 发起请求
        log.info("腾盛支付参数 url：{}, params: {}", url, params);
        String response = HttpUtil.post(url, body);
        log.info("腾盛支付返回值：{}", response);

        // 验证返回值
        return this.verifyPlatformData(mch, response, responseClass);
    }

    /**
     * 验证平台数据
     *
     * @param mch           商户
     * @param data          平台数据（请求的返回值，回调的参数）
     * @param responseClass 响应类型
     * @param <T>           继承 TenserPayBaseDTO 的类型
     * @return 返回值
     */
    private <T extends TenserPayBaseDTO> T verifyPlatformData(InnerMchInfoTenserDTO mch, String data, Class<T> responseClass) {
        TenserPayBaseDTO baseDTO = new TenserPayBaseDTO();
        baseDTO.setResponse(data);

        JsonObject responseJsonObject = GsonUtil.jsonToObject(data);
        String responseHead = responseJsonObject.get("head").toString();
        baseDTO.setResponseHead(responseHead);

        String responseBody = responseJsonObject.get("body").toString();
        baseDTO.setResponseBody(responseBody);

        // 判断请求是否成功，通知数据里没有 systemCode 所以默认成功
        String systemCode = GsonUtil.getAsString(responseHead, "systemCode");
        boolean success = "".equals(systemCode) || TenserPayBaseDTO.SUCCESS_CODE.equals(systemCode);

        // 验证返回数据的签名
        boolean verifyPlatformSignature = this.verifyPlatformSignature(responseHead, responseBody, mch.getTenserPublicKey());

        // 判断请求成功，并且签名验证通过
        baseDTO.setSuccess(success && verifyPlatformSignature);
        if (Boolean.TRUE.equals(baseDTO.getSuccess())) {
            log.info("腾盛支付业务结果: {}", responseBody);
            try {
                // 组装返回值
                T resultDTO = responseClass == TenserPayBaseDTO.class ? responseClass.newInstance() :
                        GsonUtil.jsonToBean(responseBody, responseClass);
                BeanUtils.copyProperties(baseDTO, resultDTO);
                return resultDTO;
            } catch (Exception e) {
                throw SysException.of(SysErrorCode.S_RPC_ERROR, data, e);
            }
        }
        throw SysException.of(SysErrorCode.S_RPC_ERROR, data);
    }

    /**
     * 构建请求的 body
     *
     * @param params             接口业务参数
     * @param merchantPrivateKey 商户私钥
     * @return 请求的 body
     */
    private Map<String, Object> getRequestBody(String params, String merchantPrivateKey) {
        // 构建请求的 body 中的 head 参数
        Map<String, String> bodyBody = GsonUtil.jsonToMapString(params);
        bodyBody.entrySet().removeIf(entry -> entry.getValue() == null);
        Map<String, String> bodyHead = this.getRequestBodyHead(bodyBody, merchantPrivateKey);

        // 组装请求的 body，包含 head body 两个参数
        Map<String, Object> body = Maps.newHashMapWithExpectedSize(2);
        body.put("head", bodyHead);
        body.put("body", bodyBody);
        return body;
    }

    /**
     * 构建请求的 body 中的 head 参数
     *
     * @param bodyBody           请求的 body 中的 body 参数
     * @param merchantPrivateKey 商户私钥内容
     * @return 请求的 body 中的 head 参数
     */
    private Map<String, String> getRequestBodyHead(Map<String, String> bodyBody, String merchantPrivateKey) {
        // 组装 bodyHead
        Map<String, String> bodyHead = Maps.newHashMapWithExpectedSize(8);
        bodyHead.put("charset", "01");
        bodyHead.put("version", "1.0.0");
        bodyHead.put("signType", "0");
        bodyHead.put("requestId", this.uuid());
        bodyHead.put("requestTime", DateUtil.nowDateTime(DATE_PATTERN));
        bodyHead.put("nonceStr", this.uuid());
        bodyHead.put("orgCode", ORG_CODE);

        // 组装签名内容
        SortedMap<String, String> signatureParams = Maps.newTreeMap();
        signatureParams.putAll(bodyHead);
        signatureParams.putAll(bodyBody);
        String signatureContent = this.buildSignatureContent(signatureParams);

        // 生成签名，然后将签名参数加入 bodyHead
        try {
            String sign = RsaUtil.encryptByPrivateKey(signatureContent, merchantPrivateKey);
            bodyHead.put("sign", sign);
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, e);
        }
        return bodyHead;
    }

    /**
     * 验证平台签名
     *
     * @param responseHead    平台返回数据的 head
     * @param responseBody    平台返回数据的 body
     * @param tenserPublicKey 平台公钥内容
     * @return 验证结果
     */
    private boolean verifyPlatformSignature(String responseHead, String responseBody, String tenserPublicKey) {
        // 获取平台返回数据的 sign
        String platformSignature = GsonUtil.getAsString(responseHead, "sign");
        if (StringUtils.isBlank(platformSignature)) {
            return false;
        }

        // 去除平台返回数据的 sign 参数，并生成签名内容
        SortedMap<String, String> signatureParams = Maps.newTreeMap();
        signatureParams.putAll(GsonUtil.jsonToMapString(responseHead));
        signatureParams.putAll(GsonUtil.jsonToMapString(responseBody));
        signatureParams.entrySet().removeIf(entry -> "sign".equals(entry.getKey()));
        String signatureContent = this.buildSignatureContent(signatureParams);

        try {
            boolean verifyPlatformSignature = RsaUtil.verifyByPublicKey(signatureContent, platformSignature, tenserPublicKey);
            log.info("验证平台签名 verifyPlatformSignature：{}", verifyPlatformSignature);
            return verifyPlatformSignature;
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, e);
        }
    }

    /**
     * 生成签名内容
     *
     * @param signatureParams 签名参数
     * @return 签名内容
     */
    private String buildSignatureContent(SortedMap<String, String> signatureParams) {
        String signatureContent = signatureParams.entrySet().stream()
                .filter(entry -> StringUtils.isNotBlank(entry.getValue()))
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        log.info("签名内容 signatureContent: {}", signatureContent);
        return signatureContent;
    }

    /**
     * 生成 uuid
     *
     * @return uuid
     */
    private String uuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

}
