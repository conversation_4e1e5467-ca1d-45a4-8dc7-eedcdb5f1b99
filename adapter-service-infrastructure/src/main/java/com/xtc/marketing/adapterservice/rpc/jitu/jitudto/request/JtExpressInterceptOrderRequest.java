package com.xtc.marketing.adapterservice.rpc.jitu.jitudto.request;

import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.JtExpressBaseRequest;
import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.response.JtInterceptOrderResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 极兔快递拦截订单参数DTO
 */
@Getter
@Setter
@ToString
public class JtExpressInterceptOrderRequest extends JtExpressBaseRequest<JtInterceptOrderResponse> {

    /**
     * 运单号
     */
    private String mailNo;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 拦截原因
     */
    private String reason;

    /**
     * 4：退回 5：转寄
     */
    private Integer applyTypeCode;

    /**
     * 转寄 地址省，applyTypeCode = 5 必传
     */
    private String receiveProvince;

    /**
     * 转寄 地址市，applyTypeCode = 5 必传
     */
    private String receiveCity;

    /**
     * 转寄 地址区，applyTypeCode = 5 必传
     */
    private String receiveDistrict;

    /**
     * 转寄 详细地址，applyTypeCode = 5 必传
     */
    private String receiveAddress;

    /**
     * 转寄 收件⼈姓名，applyTypeCode = 5 必传
     */
    private String receiveUsername;

    /**
     * 收件⼈⼿机（⼿机与电话⾄少有⼀项不为空）applyTypeCode = 5 必传
     */
    private String receiveMobilPhone;

    /**
     * 收件⼈电话 （⼿机与电话⾄少有⼀项不为空）applyTypeCode = 5 必传
     */
    private String receivePhone;

    @Override
    public Class<JtInterceptOrderResponse> getResponseClass() {
        return JtInterceptOrderResponse.class;
    }

    @Override
    public String getApiPath() {
        return "/webopenplatformapi/api/waybill/intercept";
    }

}