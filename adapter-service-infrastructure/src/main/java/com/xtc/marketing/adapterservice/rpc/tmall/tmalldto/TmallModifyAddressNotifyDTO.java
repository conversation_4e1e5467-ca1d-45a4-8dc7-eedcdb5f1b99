package com.xtc.marketing.adapterservice.rpc.tmall.tmalldto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 天猫修改地址通知
 */
@Getter
@Setter
@ToString
public class TmallModifyAddressNotifyDTO {

    /**
     * 店铺主账号
     */
    private String sellerNick;
    /**
     * 买家账号名
     */
    private String buyerNick;
    /**
     * 交易订单ID
     */
    private String bizOrderId;
    /**
     * 地址密文
     */
    private String oaid;
    /**
     * 订单原始收货地址信息
     */
    private Address originalAddress;
    /**
     * 要修改的地址信息
     */
    private Address modifiedAddress;

    @Getter
    @Setter
    @ToString
    public static class Address {

        /**
         * 收货人姓名（可不填，无表示没有修改）
         */
        private String name;
        /**
         * 收货人电话（可不填，无表示没有修改）
         */
        private String phone;
        /**
         * 邮编（非必须有可能为空）
         */
        private String postCode;
        /**
         * 国家（可不填，不填的情况下默认中国）
         */
        private String country;
        /**
         * 省份、州等
         */
        private String province;
        /**
         * 市
         */
        private String city;
        /**
         * 区
         */
        private String area;
        /**
         * 乡、镇、街道信息
         */
        private String town;
        /**
         * 详细地址（不带town信息，非必须有可能为空）
         */
        private String addressDetail;

    }

}
