package com.xtc.marketing.adapterservice.rpc.sf.sfxmldto;

import java.util.List;

/**
 * 顺丰仓库 xml 通知
 */
public abstract class SfNotifyXmlDTO {

    /**
     * 通知响应结果格式
     */
    private static final String NOTIFY_RESPONSE_FORMATE =
            "<Response service=\"%s\" lang=\"zh-CN\">" +
                    "<Head>OK</Head>" +
                    "<Body><%s><Result>1</Result><Note></Note></%s></Body>" +
                    "</Response>";

    /**
     * 获取通知结果的 xml 元素解析层次
     *
     * @return xml 元素解析层次
     */
    public abstract List<String> getNotifyParseElements();

    /**
     * 获取通知的响应结果
     *
     * @return 响应结果
     */
    public abstract String getNotifyResponse();

    /**
     * 构建通知响应结果
     *
     * @param serviceCode  服务编码
     * @param responseCode 响应编码
     * @return 响应结果
     */
    public String buildNotifyResponse(String serviceCode, String responseCode) {
        return String.format(NOTIFY_RESPONSE_FORMATE, serviceCode, responseCode, responseCode);
    }

}
