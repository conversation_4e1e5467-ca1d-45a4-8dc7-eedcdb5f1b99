package com.xtc.marketing.adapterservice.rpc.jd.jddto.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.UUID;

/**
 * 京东物流-创建订单请求
 * <a href="https://jos.jd.com/commondoc?listId=657">接口文档</a>
 */
@Getter
@Setter
@ToString
public class LdopAlphaWaybillReceiveRequest {

    /**
     * 商家编码，普通商家传商家ID，厂直商家传vc拼接供应商简码
     */
    private String vendorCode;
    /**
     * 商家名称
     */
    private String vendorName;
    /**
     * 平台订单号，即pop订单号，脱敏项目必须传真实的京东pop订单号
     */
    private String platformOrderNo;
    /**
     * 承运商id (providerId与providerCode两者必填一个)
     */
    private String providerId;
    /**
     * 承运商编码(providerId与providerCode两者必填一个)
     */
    private String providerCode;
    /**
     * 结算编码（直营型承运商必传，如顺丰）
     */
    private String settlementCode;
    /**
     * 网点编码（加盟型承运商必传）
     */
    private String branchCode;
    /**
     * 快件产品类别 目前顺丰使用，后续可根据承运商自己定义 1:顺丰次日 2:顺丰标件，下单时商家选择产品类别，长度1
     */
    private Integer expressType;
    /**
     * 寄件人
     */
    private Address fromAddress;
    /**
     * 收件人
     */
    private Address toAddress;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 当前包裹金额，商品金额两位小数
     */
    private String goodsMoney;
    /**
     * 商家自有订单号
     */
    private String vendorOrderCode = UUID.randomUUID().toString().replace("-", "");
    /**
     * 销售平台，字段长度1~10 京东： 0010001，其他平台：0030001
     */
    private String salePlatform = "0010001";
    /**
     * 运单类型：1普通运单
     */
    private Integer waybillType = 1;
    /**
     * 所需运单的数量
     */
    private Integer waybillCount = 1;
    /**
     * 快递费付款方式（运费付款方式）1:寄方付 2:收方付 3:第三方付，填写下单接口下传的商家选择填写
     */
    private Integer expressPayMethod = 1;
    /**
     * 重量，单位为千克两位小数
     */
    private String weight = "0";
    /**
     * 体积，单位为统一为立方厘米两位小数
     */
    private String volume = "0";
    /**
     * 付款方式：0-在线支付，1-货到付款
     */
    private Integer payType = 0;
    /**
     * 代收金额两位小数
     */
    private String shouldPayMoney = "0";
    /**
     * 是否要保价
     */
    private Boolean needGuarantee = false;
    /**
     * 保价金额两位小数
     */
    private String guaranteeMoney = "0";
    /**
     * 收货时间类型，0任何时间，1工作日2节假日
     */
    private Integer receiveTimeType = 0;

    @Getter
    @Setter
    @ToString
    public static class Address {

        /**
         * 收发件地址加密信息
         */
        private String oaid;
        /**
         * 收货/发货联系人
         */
        private String contact;
        /**
         * 收货/发货人电话
         */
        private String phone;
        /**
         * 收货/发货人手机
         */
        private String mobile;
        /**
         * 省/直辖市名称
         */
        private String provinceName;
        /**
         * 市名称
         */
        private String cityName;
        /**
         * 区/县名称
         */
        private String countryName;
        /**
         * 县镇/街道名称
         */
        private String countrysideName;
        /**
         * 收货/发货详细地址
         */
        private String address;

    }

}
