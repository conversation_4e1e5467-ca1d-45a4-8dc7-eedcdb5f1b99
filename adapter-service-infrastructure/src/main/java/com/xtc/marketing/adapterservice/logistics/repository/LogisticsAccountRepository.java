package com.xtc.marketing.adapterservice.logistics.repository;

import com.xtc.marketing.adapterservice.config.BaseRepository;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.logistics.repository.mapper.LogisticsAccountMapper;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public class LogisticsAccountRepository extends BaseRepository<LogisticsAccountMapper, LogisticsAccountDO> {

    /**
     * 查询物流账号
     *
     * @param bizAccount 业务账号
     * @return 物流账号
     */
    public Optional<LogisticsAccountDO> getByBizAccount(String bizAccount) {
        return super.getBy(LogisticsAccountDO::getBizAccount, bizAccount);
    }

    /**
     * 查询物流账号
     *
     * @param clientCode 客户代码
     * @return 物流账号
     */
    public Optional<LogisticsAccountDO> getByClientCode(String clientCode) {
        return super.getBy(LogisticsAccountDO::getClientCode, clientCode);
    }

}
