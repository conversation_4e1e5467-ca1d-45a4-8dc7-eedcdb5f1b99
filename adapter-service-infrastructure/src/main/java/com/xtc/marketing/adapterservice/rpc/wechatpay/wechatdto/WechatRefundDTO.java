package com.xtc.marketing.adapterservice.rpc.wechatpay.wechatdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
public class WechatRefundDTO {

    /**
     * 订单支付成功状态
     */
    public static final String REFUND_STATE_SUCCESS = "SUCCESS";

    /**
     * 微信支付退款单号
     */
    @SerializedName("refund_id")
    private String refundId;

    /**
     * 商户退款单号
     */
    @SerializedName("out_refund_no")
    private String outRefundNo;

    /**
     * 微信支付订单号
     */
    @SerializedName("transaction_id")
    private String transactionId;

    /**
     * 商户订单号
     */
    @SerializedName("out_trade_no")
    private String outTradeNo;

    /**
     * 退款状态
     */
    @SerializedName(value = "refund_status", alternate = "status")
    private RefundState refundState;

    /**
     * 退款成功时间
     */
    @SerializedName("success_time")
    private String successTime;

    /**
     * 退款创建时间
     */
    @SerializedName("create_time")
    private String createTime;

    /**
     * 订单金额
     */
    @SerializedName("amount")
    private Amount amount;

    /**
     * 金额信息
     */
    @Getter
    @Setter
    @ToString
    public static class Amount {

        /**
         * 订单金额
         */
        @SerializedName("total")
        private Integer total;

        /**
         * 退款金额
         */
        @SerializedName("refund")
        private Integer refund;

        /**
         * 用户支付金额
         */
        @SerializedName("payer_total")
        private Integer payerTotal;

        /**
         * 用户退款金额
         */
        @SerializedName("payer_refund")
        private Integer payerRefund;

    }

    /**
     * 退款状态
     */
    public enum RefundState {
        /**
         * 退款成功
         */
        SUCCESS,
        /**
         * 退款处理中
         */
        PROCESSING,
        /**
         * 退款关闭
         */
        CLOSED,
        /**
         * 退款异常
         */
        ABNORMAL
    }

}
