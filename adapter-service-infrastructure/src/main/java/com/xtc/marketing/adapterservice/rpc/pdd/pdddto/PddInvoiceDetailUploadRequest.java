package com.xtc.marketing.adapterservice.rpc.pdd.pdddto;

import com.pdd.pop.ext.fasterxml.jackson.annotation.JsonProperty;
import com.pdd.pop.sdk.common.util.JsonUtil;
import com.pdd.pop.sdk.http.HttpMethod;
import com.pdd.pop.sdk.http.PopBaseHttpRequest;
import com.pdd.pop.sdk.http.api.pop.response.PddInvoiceDetailUploadResponse;
import lombok.Getter;

import java.util.List;
import java.util.Map;

@Getter
public class PddInvoiceDetailUploadRequest extends PopBaseHttpRequest<PddInvoiceDetailUploadResponse> {

    /**
     * 申请流水号
     */
    @JsonProperty("application_id")
    private Long applicationId;

    /**
     * 抬头类型：0-个人，1-企业
     */
    @JsonProperty("business_type")
    private Integer businessType;

    /**
     * 发票校验码，票面上有则填写，非必填
     */
    @JsonProperty("check_code")
    private String checkCode;

    /**
     * 开票金额，整数，单位：分
     */
    @JsonProperty("invoice_amount")
    private Long invoiceAmount;

    /**
     * 发票类型，即发票票面上标题对应的类型，枚举值如下： 1、增值税普通发票 2、增值税专用发票 3、增值税电子普通发票 4、增值税电子专用发票 5、数电普票 6、数电专票 99、其他(以旧换新·国家补贴订单必填)
     */
    @JsonProperty("invoice_category")
    private Integer invoiceCategory;

    /**
     * 发票代码
     */
    @JsonProperty("invoice_code")
    private String invoiceCode;

    /**
     * 发票内容，pdf文件(电票回传)，图片文件(专票回传)，转码base64编码
     */
    @JsonProperty("invoice_file_content")
    private String invoiceFileContent;

    /**
     * 发票列表(单张发票场景 只需传入1个item，拆票场景 可以传入多个item)；  【传参方式1（优先建议）：invoice_item_list字段传值（有值时优先使用），外层的字段（check_code、invoice_amount、invoice_code、invoice_file_content、invoice_no、original_invoice_code、original_invoice_no、product_name、quantity、specification、unit）可不用传】； 【传参方式2：invoice_item_list字段未传值，系统默认取外层传的字段（check_code、invoice_amount、invoice_code、invoice_file_content、invoice_no、original_invoice_code、original_invoice_no、product_name、quantity、specification、unit），其中invoice_code、invoice_no、invoice_amount、invoice_file_content这四个字段必须填写】； 【其他要求：以旧换新国家补贴订单 参考公告 https: open.pinduoduo.com/application/document/announcement?id=327 】
     */
    @JsonProperty("invoice_item_list")
    private List<InvoiceItemListItem> invoiceItemList;

    /**
     * 发票种类：0-电子发票，1-纸质发票，2-专票；目前只支持0
     */
    @JsonProperty("invoice_kind")
    private Integer invoiceKind;

    /**
     * 发票号码
     */
    @JsonProperty("invoice_no")
    private String invoiceNo;

    /**
     * 开票日期,时间戳（毫秒）
     */
    @JsonProperty("invoice_time")
    private Long invoiceTime;

    /**
     * 开票类型：0-蓝票，1-红票；目前 只支持0
     */
    @JsonProperty("invoice_type")
    private Integer invoiceType;

    /**
     * 备注
     */
    @JsonProperty("memo")
    private String memo;

    /**
     * 订单号
     */
    @JsonProperty("order_sn")
    private String orderSn;

    /**
     * 原蓝票代码（红票必填）
     */
    @JsonProperty("original_invoice_code")
    private String originalInvoiceCode;

    /**
     * 原蓝票号码（红票必填）
     */
    @JsonProperty("original_invoice_no")
    private String originalInvoiceNo;

    /**
     * 专票回传必填，专票邮寄快递公司编码，见https: open.pinduoduo.com/application/document/api?id=pdd.logistics.companies.get返回的快递公司编码
     */
    @JsonProperty("paper_shipping_id")
    private Integer paperShippingId;

    /**
     * 专票回传必填，专票邮寄运单号
     */
    @JsonProperty("paper_tracking_number")
    private String paperTrackingNumber;

    /**
     * 开票人
     */
    @JsonProperty("payee_operator")
    private String payeeOperator;

    /**
     * （企业抬头）开户账号
     */
    @JsonProperty("payer_account")
    private String payerAccount;

    /**
     * （企业抬头）地址
     */
    @JsonProperty("payer_address")
    private String payerAddress;

    /**
     * （企业抬头）开户银行
     */
    @JsonProperty("payer_bank")
    private String payerBank;

    /**
     * 发票抬头
     */
    @JsonProperty("payer_name")
    private String payerName;

    /**
     * （企业抬头）电话
     */
    @JsonProperty("payer_phone")
    private String payerPhone;

    /**
     * 税号，企业必填
     */
    @JsonProperty("payer_register_no")
    private String payerRegisterNo;

    /**
     * 项目名称，票面上对应的项目名称（每张票需注意仅能包含一个项目）
     */
    @JsonProperty("product_name")
    private String productName;

    /**
     * 数量，票面上项目对应的数量（每张票需注意仅能包含一个项目）
     */
    @JsonProperty("quantity")
    private String quantity;

    /**
     * 销方名称(以旧换新·国家补贴订单必填)
     */
    @JsonProperty("seller_name")
    private String sellerName;

    /**
     * 销方税号(以旧换新·国家补贴订单必填)
     */
    @JsonProperty("seller_register_no")
    private String sellerRegisterNo;

    /**
     * 规格型号，票面上对应的规格型号（每张票需注意仅能包含一个项目）
     */
    @JsonProperty("specification")
    private String specification;

    /**
     * 不含税金额，整数，单位：分
     */
    @JsonProperty("sum_price")
    private Long sumPrice;

    /**
     * 总税额，整数，单位：分
     */
    @JsonProperty("sum_tax")
    private Integer sumTax;

    /**
     * 税率,整数
     */
    @JsonProperty("tax_rate")
    private Integer taxRate;

    /**
     * 单位 (比如：件、个、台等)
     */
    @JsonProperty("unit")
    private String unit;

    @Override
    public String getVersion() {
        return "V1";
    }

    @Override
    public String getDataType() {
        return "JSON";
    }

    @Override
    public Integer getPlatform() {
        return 0;
    }

    @Override
    public String getType() {
        return "pdd.invoice.detail.upload";
    }

    @Override
    public HttpMethod getHttpMethod() {
        return HttpMethod.POST;
    }

    @Override
    public Class<PddInvoiceDetailUploadResponse> getResponseClass() {
        return PddInvoiceDetailUploadResponse.class;
    }

    @Override
    protected void setUserParams(Map<String, String> params) {
        setUserParam(params, "application_id", applicationId);
        setUserParam(params, "business_type", businessType);
        setUserParam(params, "check_code", checkCode);
        setUserParam(params, "invoice_amount", invoiceAmount);
        setUserParam(params, "invoice_category", invoiceCategory);
        setUserParam(params, "invoice_code", invoiceCode);
        setUserParam(params, "invoice_file_content", invoiceFileContent);
        setUserParam(params, "invoice_item_list", invoiceItemList);
        setUserParam(params, "invoice_kind", invoiceKind);
        setUserParam(params, "invoice_no", invoiceNo);
        setUserParam(params, "invoice_time", invoiceTime);
        setUserParam(params, "invoice_type", invoiceType);
        setUserParam(params, "memo", memo);
        setUserParam(params, "order_sn", orderSn);
        setUserParam(params, "original_invoice_code", originalInvoiceCode);
        setUserParam(params, "original_invoice_no", originalInvoiceNo);
        setUserParam(params, "paper_shipping_id", paperShippingId);
        setUserParam(params, "paper_tracking_number", paperTrackingNumber);
        setUserParam(params, "payee_operator", payeeOperator);
        setUserParam(params, "payer_account", payerAccount);
        setUserParam(params, "payer_address", payerAddress);
        setUserParam(params, "payer_bank", payerBank);
        setUserParam(params, "payer_name", payerName);
        setUserParam(params, "payer_phone", payerPhone);
        setUserParam(params, "payer_register_no", payerRegisterNo);
        setUserParam(params, "product_name", productName);
        setUserParam(params, "quantity", quantity);
        setUserParam(params, "seller_name", sellerName);
        setUserParam(params, "seller_register_no", sellerRegisterNo);
        setUserParam(params, "specification", specification);
        setUserParam(params, "sum_price", sumPrice);
        setUserParam(params, "sum_tax", sumTax);
        setUserParam(params, "tax_rate", taxRate);
        setUserParam(params, "unit", unit);
    }

    public void setApplicationId(Long applicationId) {
        this.applicationId = applicationId;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public void setCheckCode(String checkCode) {
        this.checkCode = checkCode;
    }

    public void setInvoiceAmount(Long invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public void setInvoiceCategory(Integer invoiceCategory) {
        this.invoiceCategory = invoiceCategory;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public void setInvoiceFileContent(String invoiceFileContent) {
        this.invoiceFileContent = invoiceFileContent;
    }

    public void setInvoiceItemList(List<InvoiceItemListItem> invoiceItemList) {
        this.invoiceItemList = invoiceItemList;
    }

    public void setInvoiceKind(Integer invoiceKind) {
        this.invoiceKind = invoiceKind;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public void setInvoiceTime(Long invoiceTime) {
        this.invoiceTime = invoiceTime;
    }

    public void setInvoiceType(Integer invoiceType) {
        this.invoiceType = invoiceType;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public void setOriginalInvoiceCode(String originalInvoiceCode) {
        this.originalInvoiceCode = originalInvoiceCode;
    }

    public void setOriginalInvoiceNo(String originalInvoiceNo) {
        this.originalInvoiceNo = originalInvoiceNo;
    }

    public void setPaperShippingId(Integer paperShippingId) {
        this.paperShippingId = paperShippingId;
    }

    public void setPaperTrackingNumber(String paperTrackingNumber) {
        this.paperTrackingNumber = paperTrackingNumber;
    }

    public void setPayeeOperator(String payeeOperator) {
        this.payeeOperator = payeeOperator;
    }

    public void setPayerAccount(String payerAccount) {
        this.payerAccount = payerAccount;
    }

    public void setPayerAddress(String payerAddress) {
        this.payerAddress = payerAddress;
    }

    public void setPayerBank(String payerBank) {
        this.payerBank = payerBank;
    }

    public void setPayerName(String payerName) {
        this.payerName = payerName;
    }

    public void setPayerPhone(String payerPhone) {
        this.payerPhone = payerPhone;
    }

    public void setPayerRegisterNo(String payerRegisterNo) {
        this.payerRegisterNo = payerRegisterNo;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }

    public void setSellerRegisterNo(String sellerRegisterNo) {
        this.sellerRegisterNo = sellerRegisterNo;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public void setSumPrice(Long sumPrice) {
        this.sumPrice = sumPrice;
    }

    public void setSumTax(Integer sumTax) {
        this.sumTax = sumTax;
    }

    public void setTaxRate(Integer taxRate) {
        this.taxRate = taxRate;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public static class InvoiceItemListItem {

        /**
         * 发票校验码，票面上有则填写，非必填
         */
        @JsonProperty("check_code")
        private String checkCode;

        /**
         * 开票金额 单位:分
         */
        @JsonProperty("invoice_amount")
        private Long invoiceAmount;

        /**
         * 发票代码
         */
        @JsonProperty("invoice_code")
        private String invoiceCode;

        /**
         * 发票内容，pdf文件(电票回传)，图片文件(专票回传)，转码base64编码
         */
        @JsonProperty("invoice_file_content")
        private String invoiceFileContent;

        /**
         * 发票号码
         */
        @JsonProperty("invoice_no")
        private String invoiceNo;

        /**
         * 原蓝票代码（红票必填）
         */
        @JsonProperty("original_invoice_code")
        private String originalInvoiceCode;

        /**
         * 原蓝票号码（红票必填）
         */
        @JsonProperty("original_invoice_no")
        private String originalInvoiceNo;

        /**
         * 项目名称，票面上对应的项目名称（每张票需注意仅能包含一个项目）
         */
        @JsonProperty("product_name")
        private String productName;

        /**
         * 数量，票面上项目对应的数量（每张票需注意仅能包含一个项目）
         */
        @JsonProperty("quantity")
        private String quantity;

        /**
         * 规格型号，票面上对应的规格型号（每张票需注意仅能包含一个项目）
         */
        @JsonProperty("specification")
        private String specification;

        /**
         * 单位 (比如：件、个、台等)
         */
        @JsonProperty("unit")
        private String unit;

        public void setCheckCode(String checkCode) {
            this.checkCode = checkCode;
        }

        public void setInvoiceAmount(Long invoiceAmount) {
            this.invoiceAmount = invoiceAmount;
        }

        public void setInvoiceCode(String invoiceCode) {
            this.invoiceCode = invoiceCode;
        }

        public void setInvoiceFileContent(String invoiceFileContent) {
            this.invoiceFileContent = invoiceFileContent;
        }

        public void setInvoiceNo(String invoiceNo) {
            this.invoiceNo = invoiceNo;
        }

        public void setOriginalInvoiceCode(String originalInvoiceCode) {
            this.originalInvoiceCode = originalInvoiceCode;
        }

        public void setOriginalInvoiceNo(String originalInvoiceNo) {
            this.originalInvoiceNo = originalInvoiceNo;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }

        public void setQuantity(String quantity) {
            this.quantity = quantity;
        }

        public void setSpecification(String specification) {
            this.specification = specification;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }

        @Override
        public String toString() {
            return JsonUtil.transferToJson(this);
        }

    }

}