package com.xtc.marketing.adapterservice.rpc.sf.sfdto.command;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SfOutboundCmd {

    /**
     * 业务订单号
     */
    @SerializedName("ErpOrder")
    private String orderId;

    /**
     * SF订单类型
     * 10  销售订单
     * 20  返厂订单
     * 30  换货订单
     * 40  调拨订单
     * 50  退仓订单
     * 90  NPR订单
     * 如果超出以上6种类型，请单独联系业务部门。
     */
    @SerializedName("SfOrderType")
    private String sfOrderType;

    /**
     * 收件方
     */
    @SerializedName("OrderReceiverInfo")
    private SfOutboundReceiverCmd receiver;

    /**
     * 寄件方
     */
    @SerializedName("OrderSenderInfo")
    private SfOutboundSenderCmd sender;

    /**
     * 货物集合
     */
    @SerializedName("OrderItems")
    private List<SfOutboundItemCmd> items;

}
