package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 微信视频号小店物流
 */
@Getter
@Setter
@ToString
public class WechatChannelsShopLogisticsDTO extends WechatChannelsShopBaseDTO {

    /**
     * 快递单号
     */
    @SerializedName("waybill_id")
    private String waybillNo;
    /**
     * 如果请求参数填了template_id，则返回打印报文信息，可以传给打印组件打印面单
     */
    @SerializedName("print_info")
    private String orderDetail;

    @Override
    public String getResponseDataKey() {
        return "";
    }

}
