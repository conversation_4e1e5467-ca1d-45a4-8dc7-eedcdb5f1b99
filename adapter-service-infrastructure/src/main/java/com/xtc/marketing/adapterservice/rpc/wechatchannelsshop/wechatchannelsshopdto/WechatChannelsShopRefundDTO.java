package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 微信视频号小店退款详情
 */
@Getter
@Setter
@ToString
public class WechatChannelsShopRefundDTO extends WechatChannelsShopBaseDTO {

    /**
     * 退款单号
     */
    @SerializedName("after_sale_order_id")
    private String serviceNo;
    /**
     * 退款单状态
     */
    @SerializedName("status")
    private String serviceState;
    /**
     * 用户openid
     */
    @SerializedName("openid")
    private String buyerId;
    /**
     * 订单id
     */
    @SerializedName("order_id")
    private String orderNo;
    /**
     * 商品信息
     */
    @SerializedName("product_info")
    private ProductDTO product;
    /**
     * 退款信息
     */
    @SerializedName("refund_info")
    private RefundDTO refundInfo;
    /**
     * 退货信息
     */
    @SerializedName("return_info")
    private ReturnDTO returnInfo;
    /**
     * 创建时间
     */
    @SerializedName("create_time")
    private Long createTime;
    /**
     * 更新时间
     */
    @SerializedName("update_time")
    private Long updateTime;
    /**
     * 退款原因
     */
    @SerializedName("reason")
    private String applyReason;
    /**
     * 退款单类型
     */
    @SerializedName("type")
    private String serviceType;

    @Override
    public String getResponseDataKey() {
        return "after_sale_order";
    }

    @Getter
    @Setter
    @ToString
    public static class ProductDTO {

        /**
         * 商品spuid
         */
        @SerializedName("product_id")
        private String productId;
        /**
         * 商品skuid
         */
        @SerializedName("sku_id")
        private String skuId;
        /**
         * 售后数量
         */
        @SerializedName("count")
        private Integer num;

    }

    @Getter
    @Setter
    @ToString
    public static class RefundDTO {

        /**
         * 退款金额（分）
         */
        @SerializedName("amount")
        private Integer refundAmount;
        /**
         * 标明售后单退款直接原因, 枚举值详情请参考RefundReason
         */
        @SerializedName("refund_reason")
        private Integer refundReason;

    }

    @Getter
    @Setter
    @ToString
    public static class ReturnDTO {

        /**
         * 快递单号
         */
        @SerializedName("waybill_id")
        private String returnWaybillNo;
        /**
         * 物流公司名称
         */
        @SerializedName("delivery_name")
        private String returnExpressCompany;

    }

}
