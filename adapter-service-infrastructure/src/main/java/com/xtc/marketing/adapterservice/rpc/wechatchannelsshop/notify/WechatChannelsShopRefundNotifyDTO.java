package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.notify;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 微信视频号小店，售后单更新通知
 * <p><a href="https://developers.weixin.qq.com/doc/channels/API/aftersale/ec_callback/channels_ec_aftersale_update.html">文档</a></p>
 */
@Getter
@Setter
@ToString
public class WechatChannelsShopRefundNotifyDTO {

    /**
     * 小店UserName（账号信息-原始ID）
     */
    @SerializedName("ToUserName")
    private String toUserName;
    /**
     * 固定的OpenID
     */
    @SerializedName("FromUserName")
    private String fromUserName;
    /**
     * 时间戳（秒）
     */
    @SerializedName("CreateTime")
    private Long createTime;
    /**
     * 消息类型
     */
    @SerializedName("MsgType")
    private String msgType;
    /**
     * 事件类型
     */
    @SerializedName("Event")
    private String event;
    /**
     * 售后单更新通知
     */
    @SerializedName("finder_shop_aftersale_status_update")
    private EcAftersaleInfo data;

    /**
     * 售后单更新通知
     */
    @Getter
    @Setter
    @ToString
    public static class EcAftersaleInfo {

        /**
         * 订单号
         */
        @SerializedName("order_id")
        private String orderNo;
        /**
         * 退款单号
         */
        @SerializedName("after_sale_order_id")
        private String refundId;
        /**
         * 售后单状态
         */
        @SerializedName("status")
        private String status;

    }

}
