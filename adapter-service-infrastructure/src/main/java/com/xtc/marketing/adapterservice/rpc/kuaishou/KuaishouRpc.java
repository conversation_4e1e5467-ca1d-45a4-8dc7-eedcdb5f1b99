package com.xtc.marketing.adapterservice.rpc.kuaishou;

import com.kuaishou.merchant.open.api.AccessTokenKsMerchantRequestSupport;
import com.kuaishou.merchant.open.api.KsMerchantClient;
import com.kuaishou.merchant.open.api.KsMerchantResponse;
import com.kuaishou.merchant.open.api.client.AccessTokenKsMerchantClient;
import com.kuaishou.merchant.open.api.client.oauth.OauthAccessTokenKsClient;
import com.kuaishou.merchant.open.api.common.utils.PlatformEventSecurityUtil;
import com.kuaishou.merchant.open.api.domain.express.GetEbillOrderDTO;
import com.kuaishou.merchant.open.api.domain.express.GetEbillOrderResponse;
import com.kuaishou.merchant.open.api.domain.order.OrderDetail;
import com.kuaishou.merchant.open.api.domain.order.OrderListData;
import com.kuaishou.merchant.open.api.domain.refund.MerchantRefundDetailDataView;
import com.kuaishou.merchant.open.api.domain.refund.MerchantRefundListDataView;
import com.kuaishou.merchant.open.api.request.dropshipping.OpenDropshippingEbillBatchGetRequest;
import com.kuaishou.merchant.open.api.request.dropshipping.OpenDropshippingOrderDeliverRequest;
import com.kuaishou.merchant.open.api.request.dropshipping.OpenDropshippingOrderDetailQueryRequest;
import com.kuaishou.merchant.open.api.request.dropshipping.OpenDropshippingOrderListRequest;
import com.kuaishou.merchant.open.api.request.express.OpenExpressEbillGetRequest;
import com.kuaishou.merchant.open.api.request.order.*;
import com.kuaishou.merchant.open.api.request.refund.OpenSellerOrderRefundDetailRequest;
import com.kuaishou.merchant.open.api.request.refund.OpenSellerOrderRefundPcursorListRequest;
import com.kuaishou.merchant.open.api.response.dropshipping.OpenDropshippingEbillBatchGetResponse;
import com.kuaishou.merchant.open.api.response.dropshipping.OpenDropshippingOrderDeliverResponse;
import com.kuaishou.merchant.open.api.response.dropshipping.OpenDropshippingOrderDetailQueryResponse;
import com.kuaishou.merchant.open.api.response.dropshipping.OpenDropshippingOrderListResponse;
import com.kuaishou.merchant.open.api.response.express.OpenExpressEbillGetResponse;
import com.kuaishou.merchant.open.api.response.oauth.KsAccessTokenResponse;
import com.kuaishou.merchant.open.api.response.order.OpenOrderCursorListResponse;
import com.kuaishou.merchant.open.api.response.order.OpenOrderDetailResponse;
import com.kuaishou.merchant.open.api.response.refund.OpenSellerOrderRefundDetailResponse;
import com.kuaishou.merchant.open.api.response.refund.OpenSellerOrderRefundPcursorListResponse;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.kuaishou.kuaishoudto.KuaishouMessageDTO;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.rmi.RemoteException;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 快手商城RPC
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class KuaishouRpc {

    /**
     * 缓存 client 对象
     */
    private static final Map<String, KsMerchantClient> CLIENT_CACHE = new ConcurrentHashMap<>();
    /**
     * 快手消息推送密钥
     */
    private static final String KUAISHOU_MESSAGE_KEY = "rcx0BKb3+2tkei0J+gyvwQ==";
    /**
     * 快手参数，表示没有下一页数据
     */
    public static final String NO_MORE = "nomore";

    /**
     * 刷新 AccessToken
     *
     * @param shop 店铺
     * @return AccessToken
     */
    public KsAccessTokenResponse refreshAccessToken(ShopDO shop) {
        return this.getAccessToken(shop, null);
    }

    /**
     * 获取 AccessToken
     *
     * @param shop      店铺
     * @param grantCode 授权code
     * @return AccessToken
     */
    public KsAccessTokenResponse getAccessToken(ShopDO shop, String grantCode) {
        String responseStr = "";
        try {
            KsAccessTokenResponse response;
            OauthAccessTokenKsClient client = new OauthAccessTokenKsClient(shop.getAppKey(), shop.getAppSecret());
            // 判断是生成操作或者刷新操作
            if (StringUtils.isNotBlank(grantCode)) {
                log.info("快手平台参数 grantCode: {}", grantCode);
                response = client.getAccessToken(grantCode);
            } else {
                log.info("快手平台参数 refreshToken: {}", shop.getAppRefreshToken());
                response = client.refreshAccessToken(shop.getAppRefreshToken());
            }
            responseStr = GsonUtil.objectToJson(response);
            log.info("快手平台返回值: {}", responseStr);
            if (response.getResult() != 1) {
                throw this.remoteException();
            }
            return response;
        } catch (Exception e) {
            throw this.rpcSysException(responseStr, e);
        }
    }

    /**
     * 查询订单分页列表
     *
     * @param shop    店铺
     * @param request 参数
     * @return 订单分页列表
     */
    public OrderListData pageOrders(ShopDO shop, OpenOrderCursorListRequest request) {
        // 筛选全部状态
        request.setOrderViewStatus(1);
        // 按更新时间查询
        request.setQueryType(2);
        // 倒序排序
        request.setSort(1);
        // 分销类型 0-全部 1-普通订单 2-分销订单
        request.setCpsType(0);
        OpenOrderCursorListResponse response = this.call(shop, request);
        return response.getData();
    }

    /**
     * 查询订单详情
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 订单详情
     */
    public OrderDetail getOrder(ShopDO shop, String orderNo) {
        OpenOrderDetailRequest request = new OpenOrderDetailRequest();
        request.setOid(Long.parseLong(orderNo));
        OpenOrderDetailResponse response = this.call(shop, request);
        return response.getData();
    }

    /**
     * 订单发货
     *
     * @param shop    店铺
     * @param request 请求
     * @return 结果码 1，成功
     */
    public boolean orderShipping(ShopDO shop, OpenSellerOrderGoodsDeliverRequest request) {
        KsMerchantResponse response = this.call(shop, request);
        return response.getResult() == 1;
    }

    /**
     * 订单备注
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @param remark  备注
     * @return 结果码 1，成功
     */
    public boolean orderRemark(ShopDO shop, String orderNo, String remark) {
        OpenSellerOrderNoteAddRequest request = new OpenSellerOrderNoteAddRequest();
        request.setOrderId(Long.parseLong(orderNo));
        request.setNote(remark);
        request.setFlag(4);
        KsMerchantResponse response = this.call(shop, request);
        return response.getResult() == 1;
    }

    /**
     * 查询退款单分页列表
     *
     * @param shop    店铺
     * @param request 参数
     * @return 退款单分页列表
     */
    public MerchantRefundListDataView pageRefunds(ShopDO shop, OpenSellerOrderRefundPcursorListRequest request) {
        // 按更新时间查询
        request.setQueryType(2);
        // 倒序排序
        request.setSort(1);
        // 全部退款订单
        request.setType(9);
        OpenSellerOrderRefundPcursorListResponse response = this.call(shop, request);
        return response.getData();
    }

    /**
     * 查询退款单详情
     *
     * @param shop     店铺
     * @param refundId 退款单id
     * @return 退款单详情
     */
    public MerchantRefundDetailDataView getRefund(ShopDO shop, String refundId) {
        OpenSellerOrderRefundDetailRequest request = new OpenSellerOrderRefundDetailRequest();
        request.setRefundId(Long.parseLong(refundId));
        OpenSellerOrderRefundDetailResponse response = this.call(shop, request);
        return response.getData();
    }

    /**
     * 生成电子面单
     *
     * @param shop    店铺
     * @param request 请求参数
     * @return 电子面单
     */
    public GetEbillOrderDTO createLogisticsOrder(ShopDO shop, OpenExpressEbillGetRequest request) {
        OpenExpressEbillGetResponse response = this.call(shop, request);
        return Optional.of(response)
                .map(OpenExpressEbillGetResponse::getData)
                .filter(CollectionUtils::isNotEmpty)
                .map(items -> items.get(0))
                .map(GetEbillOrderResponse::getData)
                .map(item -> item.get(0))
                .orElseThrow(() -> this.rpcSysException(response));
    }

    /**
     * 审核同意地址修改申请
     *
     * @param shop    店铺
     * @param orderId 订单号
     */
    public void approveAddressAudit(ShopDO shop, String orderId) {
        OpenOrderAddressAuditApproveRequest request = new OpenOrderAddressAuditApproveRequest();
        request.setOid(Long.parseLong(orderId));
        this.call(shop, request);
    }

    /**
     * 审核拒绝地址修改申请
     *
     * @param shop    店铺
     * @param orderId 订单号
     */
    public void rejectAddressAudit(ShopDO shop, String orderId) {
        OpenOrderAddressAuditRejectRequest request = new OpenOrderAddressAuditRejectRequest();
        request.setOid(Long.parseLong(orderId));
        this.call(shop, request);
    }

    /**
     * 解密并解析快手消息
     *
     * @param encryptedBody 加密的请求体
     * @return 解析后的消息DTO
     * @throws Exception 解析或解密异常
     */
    public KuaishouMessageDTO parseMessage(String encryptedBody) throws Exception {
        if (StringUtils.isBlank(encryptedBody)) {
            throw this.rpcSysException("加密数据为空");
        }
        // 使用固定私钥解密消息体
        String decryptedMessage = PlatformEventSecurityUtil.decode(encryptedBody, KUAISHOU_MESSAGE_KEY);
        // 解析为通用消息DTO
        return GsonUtil.jsonToBean(decryptedMessage, KuaishouMessageDTO.class);
    }

    /**
     * 查询代发订单分页列表（代发）
     *
     * @param shop    店铺
     * @param request 请求
     * @return 代发订单分页列表
     */
    public OpenDropshippingOrderListResponse pageOrdersDistr(ShopDO shop, OpenDropshippingOrderListRequest request) {
        return this.call(shop, request);
    }

    /**
     * 查询订单详情（代发）
     *
     * @param shop    店铺
     * @param orderId 订单号
     * @param shopId  店铺编号
     * @return 订单详情
     */
    public OpenDropshippingOrderDetailQueryResponse getOrderDistr(ShopDO shop, String orderId, String shopId) {
        OpenDropshippingOrderDetailQueryRequest request = new OpenDropshippingOrderDetailQueryRequest();
        request.setAllocateOrderCode(orderId);
        request.setUserCode(shopId);
        return this.call(shop, request);
    }

    /**
     * 生成电子面单（代发）
     *
     * @param shop    店铺
     * @param request 请求参数
     * @return 电子面单
     */
    public com.kuaishou.merchant.open.api.domain.dropshipping.GetEbillOrderDTO createLogisticsOrderDistr(ShopDO shop, OpenDropshippingEbillBatchGetRequest request) {
        OpenDropshippingEbillBatchGetResponse response = this.call(shop, request);
        return response.getDsOrderGetResp().get(0).getData().get(0);
    }

    /**
     * 订单发货（代发）
     *
     * @param shop    店铺
     * @param request 参数
     * @return 执行结果
     */
    public boolean orderShippingDistr(ShopDO shop, OpenDropshippingOrderDeliverRequest request) {
        OpenDropshippingOrderDeliverResponse response = this.call(shop, request);
        return response.getResult() == 1;
    }

    /**
     * 执行远程调用
     *
     * @param shop    店铺
     * @param request 参数
     * @param <T>     返回值类型
     * @return 执行结果
     */
    private <T extends KsMerchantResponse> T call(ShopDO shop, AccessTokenKsMerchantRequestSupport<T> request) {
        request.setAccessToken(shop.getAppAccessToken());
        log.info("快手平台参数: {}", GsonUtil.objectToJson(request));
        String responseStr = "";
        try {
            // 发起请求
            KsMerchantClient client = this.getClient(shop);
            T response = client.execute(request);
            // 解析响应结果
            responseStr = GsonUtil.objectToJson(response);
            log.info("快手平台返回值: {}", responseStr);
            if (BooleanUtils.isNotTrue(response.isSuccess())) {
                throw this.remoteException();
            }
            return response;
        } catch (Exception e) {
            throw this.rpcSysException(responseStr, e);
        }
    }

    /**
     * 获取请求客户端
     *
     * @param shop 店铺
     * @return 客户端
     */
    private KsMerchantClient getClient(ShopDO shop) {
        String key = shop.getShopCode();
        if (CLIENT_CACHE.get(key) == null) {
            KsMerchantClient client = new AccessTokenKsMerchantClient(shop.getAppKey(),
                    shop.getAppSessionKey(), 500, 3000);
            CLIENT_CACHE.put(key, client);
        }
        return CLIENT_CACHE.get(key);
    }

    /**
     * 远程接口异常
     *
     * @return 异常
     */
    private RemoteException remoteException() {
        return new RemoteException("RPC返回异常状态码");
    }

    /**
     * 远程调用异常
     *
     * @param response 响应
     * @return 异常
     */
    private SysException rpcSysException(Object response) {
        String responseStr = GsonUtil.objectToJson(response);
        String msg = String.format("快手平台调用异常 response: %s", responseStr);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg);
    }

    /**
     * 远程调用异常
     *
     * @param responseStr 响应结果
     * @param e           异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, Exception e) {
        String msg = String.format("快手平台调用异常 response: %s", responseStr);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
    }

    public static void main(String[] args) {
        KuaishouRpc rpc = new KuaishouRpc();
        ShopDO shop = ShopDO.builder()
                .shopCode("KUAISHOU_XTC")
                .appKey("xxxxx")
                .appSecret("xxxxx")
                .appSessionKey("xxxxx")
                .appAccessToken("xxxx")
                .build();
        rpc.getOrder(shop, "xxxxx");
    }

}
