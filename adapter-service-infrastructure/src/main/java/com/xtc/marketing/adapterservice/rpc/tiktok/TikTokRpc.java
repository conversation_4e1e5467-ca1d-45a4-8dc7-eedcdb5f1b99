package com.xtc.marketing.adapterservice.rpc.tiktok;

import com.doudian.open.api.afterSale_CancelSendGoodsSuccess.AfterSaleCancelSendGoodsSuccessRequest;
import com.doudian.open.api.afterSale_CancelSendGoodsSuccess.AfterSaleCancelSendGoodsSuccessResponse;
import com.doudian.open.api.afterSale_CancelSendGoodsSuccess.param.AfterSaleCancelSendGoodsSuccessParam;
import com.doudian.open.api.afterSale_Detail.AfterSaleDetailRequest;
import com.doudian.open.api.afterSale_Detail.AfterSaleDetailResponse;
import com.doudian.open.api.afterSale_Detail.data.AfterSaleDetailData;
import com.doudian.open.api.afterSale_List.AfterSaleListRequest;
import com.doudian.open.api.afterSale_List.AfterSaleListResponse;
import com.doudian.open.api.afterSale_List.data.AfterSaleListData;
import com.doudian.open.api.afterSale_returnGoodsToWareHouseSuccess.AfterSaleReturnGoodsToWareHouseSuccessRequest;
import com.doudian.open.api.afterSale_returnGoodsToWareHouseSuccess.AfterSaleReturnGoodsToWareHouseSuccessResponse;
import com.doudian.open.api.afterSale_returnGoodsToWareHouseSuccess.param.AfterSaleReturnGoodsToWareHouseSuccessParam;
import com.doudian.open.api.iop_orderInfo.IopOrderInfoRequest;
import com.doudian.open.api.iop_orderInfo.IopOrderInfoResponse;
import com.doudian.open.api.iop_orderInfo.data.IopOrderInfoData;
import com.doudian.open.api.iop_orderInfo.param.IopOrderInfoParam;
import com.doudian.open.api.iop_orderList.IopOrderListRequest;
import com.doudian.open.api.iop_orderList.IopOrderListResponse;
import com.doudian.open.api.iop_orderList.data.IopOrderListData;
import com.doudian.open.api.iop_waybillGet.IopWaybillGetRequest;
import com.doudian.open.api.iop_waybillGet.IopWaybillGetResponse;
import com.doudian.open.api.iop_waybillGet.data.IopWaybillGetData;
import com.doudian.open.api.iop_waybillGet.param.IopWaybillGetParam;
import com.doudian.open.api.iop_waybillReturn.IopWaybillReturnRequest;
import com.doudian.open.api.iop_waybillReturn.IopWaybillReturnResponse;
import com.doudian.open.api.logistics_newCreateOrder.LogisticsNewCreateOrderRequest;
import com.doudian.open.api.logistics_newCreateOrder.LogisticsNewCreateOrderResponse;
import com.doudian.open.api.logistics_newCreateOrder.data.EbillInfosItem;
import com.doudian.open.api.logistics_newCreateOrder.data.LogisticsNewCreateOrderData;
import com.doudian.open.api.logistics_newCreateOrder.param.LogisticsNewCreateOrderParam;
import com.doudian.open.api.logistics_waybillApply.LogisticsWaybillApplyRequest;
import com.doudian.open.api.logistics_waybillApply.LogisticsWaybillApplyResponse;
import com.doudian.open.api.logistics_waybillApply.data.LogisticsWaybillApplyData;
import com.doudian.open.api.logistics_waybillApply.data.WaybillInfosItem;
import com.doudian.open.api.logistics_waybillApply.param.LogisticsWaybillApplyParam;
import com.doudian.open.api.logistics_waybillApply.param.WaybillAppliesItem;
import com.doudian.open.api.order_addOrderRemark.OrderAddOrderRemarkRequest;
import com.doudian.open.api.order_addOrderRemark.OrderAddOrderRemarkResponse;
import com.doudian.open.api.order_addressConfirm.OrderAddressConfirmRequest;
import com.doudian.open.api.order_addressConfirm.OrderAddressConfirmResponse;
import com.doudian.open.api.order_addressConfirm.param.OrderAddressConfirmParam;
import com.doudian.open.api.order_batchDecrypt.OrderBatchDecryptRequest;
import com.doudian.open.api.order_batchDecrypt.OrderBatchDecryptResponse;
import com.doudian.open.api.order_batchDecrypt.data.OrderBatchDecryptData;
import com.doudian.open.msg.trade_TradeAddressChangeApplied.TradeTradeAddressChangeAppliedRequest;
import com.doudian.open.msg.trade_TradeAddressChangeApplied.param.TradeTradeAddressChangeAppliedParam;
import com.doudian.open.msg.trade_TradeAddressChanged.TradeTradeAddressChangedRequest;
import com.doudian.open.msg.trade_TradeAddressChanged.param.TradeTradeAddressChangedParam;
import com.doudian.open.api.order_invoiceList.OrderInvoiceListRequest;
import com.doudian.open.api.order_invoiceList.OrderInvoiceListResponse;
import com.doudian.open.api.order_invoiceList.data.OrderInvoiceListData;
import com.doudian.open.api.order_logisticsAdd.OrderLogisticsAddRequest;
import com.doudian.open.api.order_logisticsAdd.OrderLogisticsAddResponse;
import com.doudian.open.api.order_logisticsAdd.param.OrderLogisticsAddParam;
import com.doudian.open.api.order_logisticsAddMultiPack.OrderLogisticsAddMultiPackRequest;
import com.doudian.open.api.order_logisticsAddMultiPack.OrderLogisticsAddMultiPackResponse;
import com.doudian.open.api.order_logisticsAddMultiPack.param.OrderLogisticsAddMultiPackParam;
import com.doudian.open.api.order_orderDetail.OrderOrderDetailRequest;
import com.doudian.open.api.order_orderDetail.OrderOrderDetailResponse;
import com.doudian.open.api.order_orderDetail.data.ShopOrderDetail;
import com.doudian.open.api.order_orderDetail.param.OrderOrderDetailParam;
import com.doudian.open.api.order_searchList.OrderSearchListRequest;
import com.doudian.open.api.order_searchList.OrderSearchListResponse;
import com.doudian.open.api.order_searchList.data.OrderSearchListData;
import com.doudian.open.api.token_create.TokenCreateRequest;
import com.doudian.open.api.token_create.TokenCreateResponse;
import com.doudian.open.api.token_create.data.TokenCreateData;
import com.doudian.open.api.token_create.param.TokenCreateParam;
import com.doudian.open.core.*;
import com.doudian.open.spi.order_shopAddress_getReviewResult.OrderShopAddressGetReviewResultRequest;
import com.doudian.open.spi.order_shopAddress_getReviewResult.OrderShopAddressGetReviewResultResponse;
import com.doudian.open.spi.order_shopAddress_getReviewResult.param.OrderShopAddressGetReviewResultParam;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.utils.SignUtil;
import com.google.common.collect.ImmutableMap;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.tiktok.tiktokdto.enums.TikTokLogisticsCompany;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.ByteArrayInputStream;
import java.net.SocketTimeoutException;
import java.rmi.RemoteException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 抖音商城(TikTok)RPC
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class TikTokRpc {

    /**
     * 分页查询使用的排序字段，默认按照更新时间倒序排序
     */
    public static final String ORDER_BY = "update_time";

    /**
     * 中转服务器地址
     */
    private static final String PROXY_DOMAIN = "http://101.126.39.29:8080";
    /**
     * API：订单解密
     */
    private static final String API_ORDER_DECRYPT = "/api/tiktok/order/decrypt";
    /**
     * API：订单详情
     */
    private static final String API_ORDER_DETAILS = "/api/tiktok/order/detail";
    /**
     * API：订单分页
     */
    private static final String API_ORDER_PAGE = "/api/tiktok/order/page";
    /**
     * API：退款分页
     */
    private static final String API_REFUND_PAGE = "/api/tiktok/refund/page";

    /**
     * 查询订单分页列表
     *
     * @param shop    店铺
     * @param request 参数
     * @return 订单分页列表
     */
    public OrderSearchListData pageOrders(ShopDO shop, OrderSearchListRequest request) {
        OrderSearchListResponse response = this.call(shop, request, API_ORDER_PAGE);
        return response.getData();
    }

    /**
     * 查询订单分页列表（代发）
     *
     * @param shop    店铺
     * @param request 参数
     * @return 订单分页列表
     */
    public IopOrderListData pageOrdersDistr(ShopDO shop, IopOrderListRequest request) {
        IopOrderListResponse execute = this.call(shop, request);
        return execute.getData();
    }

    /**
     * 查询订单详情
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 订单详情
     */
    public ShopOrderDetail getOrder(ShopDO shop, String orderNo) {
        OrderOrderDetailRequest request = new OrderOrderDetailRequest();
        OrderOrderDetailParam param = request.getParam();
        param.setShopOrderId(orderNo);
        OrderOrderDetailResponse response = this.call(shop, request, API_ORDER_DETAILS);
        return response.getData().getShopOrderDetail();
    }

    /**
     * 查询订单详情（代发）
     *
     * @param shop         店铺
     * @param distrOrderNo 代发订单号
     * @param distrShopId  代发店铺id
     * @return 订单详情
     */
    public IopOrderInfoData getOrderDistr(ShopDO shop, String distrOrderNo, String distrShopId) {
        IopOrderInfoRequest request = new IopOrderInfoRequest();
        IopOrderInfoParam param = request.getParam();
        param.setUserId(Long.valueOf(distrShopId));
        param.setDistrOrderId(distrOrderNo);
        IopOrderInfoResponse response = this.call(shop, request);
        return response.getData();
    }

    /**
     * 查询退款分页列表
     *
     * @param shop    店铺
     * @param request 参数
     * @return 退款分页列表
     */
    public AfterSaleListData pageRefunds(ShopDO shop, AfterSaleListRequest request) {
        AfterSaleListResponse response = this.call(shop, request, API_REFUND_PAGE);
        return response.getData();
    }

    /**
     * 查询退款详情
     *
     * @param shop    店铺
     * @param request 参数
     * @return 退款详情
     */
    public AfterSaleDetailData getRefund(ShopDO shop, AfterSaleDetailRequest request) {
        AfterSaleDetailResponse response = this.call(shop, request);
        return response.getData();
    }

    /**
     * 订单发货
     * <a href="https://bytedance.larkoffice.com/docx/JAKSd87JQoVxoVxXCqCcr2rLnFb">国补文档参考</a>
     *
     * @param shop  店铺
     * @param param 参数
     * @return 执行结果
     */
    public boolean orderShipping(ShopDO shop, OrderLogisticsAddParam param) {
        OrderLogisticsAddRequest request = new OrderLogisticsAddRequest();
        request.setParam(param);
        OrderLogisticsAddResponse response = this.call(shop, request);
        return response.isSuccess();
    }

    /**
     * 订单发货 - 多包裹部分发货
     *
     * @param shop  店铺
     * @param param 参数
     * @return 执行结果
     */
    public boolean orderShippingMultiPack(ShopDO shop, OrderLogisticsAddMultiPackParam param) {
        OrderLogisticsAddMultiPackRequest request = new OrderLogisticsAddMultiPackRequest();
        request.setParam(param);
        OrderLogisticsAddMultiPackResponse response = this.call(shop, request);
        return response.isSuccess();
    }

    /**
     * 取消物流发货
     *
     * @param shop     店铺
     * @param refundId 退款单号
     * @return 执行结果
     */
    public boolean orderShippingCancel(ShopDO shop, String refundId) {
        AfterSaleCancelSendGoodsSuccessRequest request = new AfterSaleCancelSendGoodsSuccessRequest();
        AfterSaleCancelSendGoodsSuccessParam param = request.getParam();
        param.setAftersaleId(refundId);
        param.setOpTime(DateUtil.nowEpochSecond());
        AfterSaleCancelSendGoodsSuccessResponse response = this.call(shop, request);
        return response.isSuccess();
    }

    /**
     * 物流发货（代发）
     *
     * @param shop    店铺
     * @param request 参数
     * @return 执行结果
     */
    public boolean orderShippingDistr(ShopDO shop, IopWaybillReturnRequest request) {
        IopWaybillReturnResponse response = this.call(shop, request);
        return response.getData().getReturnResult();
    }

    /**
     * 订单备注
     *
     * @param shop    店铺
     * @param request 参数
     * @return 执行结果
     */
    public boolean orderRemark(ShopDO shop, OrderAddOrderRemarkRequest request) {
        OrderAddOrderRemarkResponse response = this.call(shop, request);
        return response.isSuccess();
    }

    /**
     * 订单解密
     *
     * @param shop    店铺
     * @param request 参数
     * @return 解密数据
     */
    public OrderBatchDecryptData orderDecrypt(ShopDO shop, OrderBatchDecryptRequest request) {
        try {
            OrderBatchDecryptResponse response = this.call(shop, request, API_ORDER_DECRYPT);
            return response.getData();
        } catch (SysException e) {
            // 解析部分解密结果 SysException.getErrDesc() > errMessage > data > OrderBatchDecryptData.class
            Optional<String> errMessage = Optional.ofNullable(e.getErrDesc())
                    .map(GsonUtil::jsonToObject)
                    .map(o -> o.get("errMessage"))
                    .filter(JsonElement::isJsonPrimitive)
                    .map(JsonElement::getAsString);
            Optional<String> dataJson = errMessage.map(GsonUtil::jsonToObject)
                    .map(o -> o.get("data"))
                    .filter(JsonElement::isJsonObject)
                    .map(JsonElement::getAsJsonObject)
                    .map(JsonElement::toString);
            return dataJson.map(o -> JsonUtil.fromJson(o, OrderBatchDecryptData.class)).orElse(null);
        }
    }

    /**
     * 查询发票申请
     *
     * @param shop    店铺
     * @param request 参数
     * @return 发票申请
     */
    public OrderInvoiceListData getInvoiceApply(ShopDO shop, OrderInvoiceListRequest request) {
        OrderInvoiceListResponse response = this.call(shop, request);
        return response.getData();
    }

    /**
     * 电子面单取号
     *
     * @param shop         店铺
     * @param requestParam 请求参数
     * @return 取号详情
     */
    public EbillInfosItem createLogisticsOrder(ShopDO shop, LogisticsNewCreateOrderParam requestParam) {
        LogisticsNewCreateOrderRequest request = new LogisticsNewCreateOrderRequest();
        request.setParam(requestParam);
        LogisticsNewCreateOrderResponse response = this.call(shop, request);

        return Optional.of(response)
                .map(LogisticsNewCreateOrderResponse::getData)
                .map(LogisticsNewCreateOrderData::getEbillInfos)
                .filter(CollectionUtils::isNotEmpty)
                .map(items -> items.get(0))
                .orElseThrow(() -> this.rpcSysException(response));
    }

    /**
     * 电子面单取号（代发）
     *
     * @param shop         关联店铺
     * @param requestParam 参数
     * @return 电子面单详情
     */
    public com.doudian.open.api.iop_waybillGet.data.EbillInfosItem createLogisticsOrderDistr(ShopDO shop, IopWaybillGetParam requestParam) {
        IopWaybillGetRequest request = new IopWaybillGetRequest();
        request.setParam(requestParam);
        IopWaybillGetResponse response = this.call(shop, request);
        return Optional.of(response)
                .map(IopWaybillGetResponse::getData)
                .map(IopWaybillGetData::getEbillInfos)
                .filter(CollectionUtils::isNotEmpty)
                .map(items -> items.get(0))
                .orElseThrow(() -> this.rpcSysException(response));
    }

    /**
     * 获取打印数据
     *
     * @param shop             店铺
     * @param orderNo          订单号
     * @param logisticsCompany 物流公司
     * @return 打印信息
     */
    public String printData(ShopDO shop, String orderNo, TikTokLogisticsCompany logisticsCompany) {
        WaybillAppliesItem appliesItem = new WaybillAppliesItem();
        appliesItem.setTrackNo(orderNo);
        appliesItem.setLogisticsCode(logisticsCompany.getCode());
        List<WaybillAppliesItem> appliesItems = new ArrayList<>();
        appliesItems.add(appliesItem);
        LogisticsWaybillApplyParam printParam = new LogisticsWaybillApplyParam();
        printParam.setWaybillApplies(appliesItems);
        LogisticsWaybillApplyRequest printRequest = new LogisticsWaybillApplyRequest();
        printRequest.setParam(printParam);
        // 获取面单
        LogisticsWaybillApplyResponse response = this.call(shop, printRequest);
        WaybillInfosItem item = Optional.of(response)
                .map(LogisticsWaybillApplyResponse::getData)
                .map(LogisticsWaybillApplyData::getWaybillInfos)
                .filter(CollectionUtils::isNotEmpty)
                .map(items -> items.get(0))
                .orElseThrow(() -> this.rpcSysException(response));
        // 打印签名
        String method = "logistics.getShopKey";
        String timestamp = String.valueOf(System.currentTimeMillis());
        String sign = SignUtil.sign(shop.getAppKey(), shop.getAppSecret(), method, timestamp, "{}", "2");
        String requestUrlPattern = "app_key=%s&method=%s&param_json={}&timestamp=%s&v=2&sign=%s&sign_method=md5&access_token=%s";
        String printSign = String.format(requestUrlPattern, shop.getAppKey(), method, timestamp, sign, shop.getAppAccessToken());
        // 返回打印信息
        JsonObject orderDetailJson = new JsonObject();
        orderDetailJson.addProperty("signature", item.getSign());
        orderDetailJson.addProperty("encryptedData", item.getPrintData());
        orderDetailJson.addProperty("params", printSign);
        orderDetailJson.addProperty("templateURL", logisticsCompany.getPrintTemplateUrl());
        return orderDetailJson.toString();
    }

    /**
     * 上传发票文件
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @param file    文件流
     * @return 执行结果
     */
    public boolean uploadInvoiceFile(ShopDO shop, String orderNo, ByteArrayInputStream file) {
        // 数据加密签名
        String paramJson = "{\"order_id\":\"" + orderNo + "\"}";
        String method = "order.invoiceUpload";
        String timestamp = String.valueOf(System.currentTimeMillis());
        String sign = SignUtil.sign(shop.getAppKey(), shop.getAppSecret(), method, timestamp, paramJson, "2");
        // 构建url
        UriComponents url = UriComponentsBuilder.fromUriString("https://openapi-fxg.jinritemai.com/order/invoiceUpload")
                .queryParam("app_key", shop.getAppKey())
                .queryParam("access_token", shop.getAppAccessToken())
                .queryParam("method", method)
                .queryParam("param_json", paramJson)
                .queryParam("timestamp", timestamp)
                .queryParam("sign", sign)
                .queryParam("v", 2)
                .build();
        String responseStr = "";
        try {
            // 构建文件数据，必须要设置 filename 抖音才会判断收到文件
            MultiValueMap<String, String> fileHeaders = new LinkedMultiValueMap<>();
            ContentDisposition contentDisposition = ContentDisposition.builder("form-data")
                    .name("upload_file").filename(orderNo + ".pdf").build();
            fileHeaders.add(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString());
            HttpEntity<InputStreamResource> fileEntity = new HttpEntity<>(new InputStreamResource(file), fileHeaders);
            // 构建请求数据，请求中加入文件数据
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(org.springframework.http.MediaType.MULTIPART_FORM_DATA);
            MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();
            form.add("upload_file", fileEntity);
            HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(form, httpHeaders);
            // 发送文件
            log.info("抖音发票上传开始 url: {}", url.toUriString());
            ResponseEntity<String> response = HttpUtil.postForEntity(url.toUri(), entity);
            responseStr = response.getBody();
            log.info("抖音发票上传结果 url: {}, response: {}", url.toUriString(), responseStr);
            DoudianOpResponse<?> doudianOpResponse = GsonUtil.jsonToBean(responseStr, DoudianOpResponse.class);
            if (doudianOpResponse == null || BooleanUtils.isNotTrue(doudianOpResponse.isSuccess())) {
                throw this.remoteException();
            }
            return true;
        } catch (Exception e) {
            throw this.rpcSysException(responseStr, e);
        }
    }

    /**
     * 生成 AccessToken
     * <ul>
     * <li>在 access_token 过期前 1h 之前，调用该接口，会返回原来的 access_token 和 refresh_token，并且二者有效期不变；
     * <li>在 access_token 过期前 1h 之内，调用该接口，会返回新的 access_token 和 refresh_token，但是原来的 access_token 和 refresh_token 继续有效一个小时；
     * <li>在 access_token 过期后，调用该接口，会返回新的 acces_token 和 refresh_token，同时原来的 acces_token 和 refresh_token 失效；
     * <li>因为自用型应用支持多店铺授权，所以请求入参中需要传 shop_id，才能返回对应店铺的 access_token 信息。如不传shop_id，则默认返回最早授权成功店铺的 access_token 信息；
     * <li>access_token 默认有效期为 7 天，可在本地缓存 access_token 信息，不需要在每次业务接口调用前重复获取 access_token 信息；
     * </ul>
     *
     * @param shop 店铺
     * @return AccessToken
     */
    public TokenCreateData createAccessToken(ShopDO shop) {
        TokenCreateRequest request = new TokenCreateRequest();
        TokenCreateParam param = request.getParam();
        param.setGrantType("authorization_self");
        param.setShopId(shop.getShopId());
        TokenCreateResponse response = this.call(shop, request);
        return response.getData();
    }

    /**
     * 退货确认入库
     *
     * @param shop     店铺
     * @param refundId 退款单号
     */
    public void refundGoodsToWarehouse(ShopDO shop, String refundId) {
        AfterSaleReturnGoodsToWareHouseSuccessParam requestParam = new AfterSaleReturnGoodsToWareHouseSuccessParam();
        requestParam.setAftersaleId(refundId);
        requestParam.setOpTime(DateUtil.toEpochSecond(LocalDateTime.now()));
        AfterSaleReturnGoodsToWareHouseSuccessRequest request = new AfterSaleReturnGoodsToWareHouseSuccessRequest();
        request.setParam(requestParam);
        AfterSaleReturnGoodsToWareHouseSuccessResponse response = this.call(shop, request);
        Optional.of(response)
                .map(AfterSaleReturnGoodsToWareHouseSuccessResponse::getCode)
                .filter("10000"::equals)
                .orElseThrow(() -> this.rpcSysException(response));
    }

    /**
     * 解析修改地址请求参数
     *
     * @param request 请求
     * @return 修改地址请求参数
     */
    public OrderShopAddressGetReviewResultParam paresTiktokModifyAddressParam(OrderShopAddressGetReviewResultRequest request) {
        String requestParam = request.getParam().getParamJson();
        return JsonUtil.fromJson(requestParam, OrderShopAddressGetReviewResultParam.class);
    }

    /**
     * 校验 spi 签名
     *
     * @param shop    店铺
     * @param request 请求
     */
    public void checkSpiSign(ShopDO shop, DoudianOpSpiRequest<?> request) {
        GlobalConfig.initAppKey(shop.getAppKey());
        GlobalConfig.initAppSecret(shop.getAppSecret());
        request.registerHandler(DoudianOpSpiContext::wrapSuccess);
        OrderShopAddressGetReviewResultResponse response = request.execute();
        if (response.getCode() != 0L) {
            throw checkSignFailedException(request.getParam().getParamJson());
        }
    }

    /**
     * 买家地址变更确认
     *
     * @param shop      店铺
     * @param orderId   订单ID
     * @param isApproved 是否同意，0代表确认地址变更申请，其他值代表拒绝原因
     * @return 执行结果
     */
    public boolean addressConfirm(ShopDO shop, String orderId, int isApproved) {
        OrderAddressConfirmRequest request = new OrderAddressConfirmRequest();
        OrderAddressConfirmParam param = request.getParam();
        param.setOrderId(orderId);
        param.setIsApproved((long) isApproved);
        
        OrderAddressConfirmResponse response = this.call(shop, request);
        return response.isSuccess();
    }



    /**
     * 执行远程调用
     *
     * @param shop    店铺
     * @param request 请求
     * @param <R>     响应类型
     * @return 执行结果
     */
    public <R extends DoudianOpResponse<?>> R call(ShopDO shop, DoudianOpRequest<?> request) {
        log.info("抖音参数: {}", GsonUtil.objectToJson(request));

        // 组装令牌参数
        GlobalConfig.AddAppKeyAndAppSecret(shop.getAppKey(), shop.getAppSecret());
        AccessToken accessToken = AccessToken.wrapWithAppKey(shop.getAppAccessToken(),
                shop.getAppRefreshToken(), shop.getAppKey());

        String responseStr = "";
        try {
            R response = request.execute(accessToken);
            // 判断 token 过期，重新获取 token 并重试接口调用
            if ("isv.access-token-expired".equals(response.getSubCode())) {
                accessToken = AccessTokenBuilder.buildWithAppKey(Long.parseLong(shop.getShopId()), shop.getAppKey());
                response = request.execute(accessToken);
            }

            responseStr = response.getOriginResponse();
            log.info("抖音返回值: {}", responseStr);

            if (!response.isSuccess()) {
                throw new RemoteException("RPC返回异常状态码");
            }
            return response;
        } catch (Exception e) {
            throw rpcSysException(responseStr, e);
        }
    }

    /**
     * 执行远程调用
     *
     * @param shop    店铺
     * @param api     接口
     * @param request 请求
     * @param <R>     响应类型
     * @return 执行结果
     */
    private <R extends DoudianOpResponse<?>> R call(ShopDO shop, DoudianOpRequest<?> request, String api) {
        String url = PROXY_DOMAIN + api;
        String responseStr = "";
        try {
            // 生成请求参数
            Map<String, Object> body = ImmutableMap.of("shop", shop,
                    "requestParam", GsonUtil.objectToJson(request.getParam()));
            // 发起请求
            responseStr = HttpUtil.post(url, body);
            // 解析响应结果
            if (StringUtils.isBlank(responseStr)) {
                throw remoteException();
            }
            return GsonUtil.jsonToBean(responseStr, request.getResponseClass());
        } catch (Exception e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                responseStr = "请求超时";
            }
            if (e instanceof HttpServerErrorException) {
                responseStr = ((HttpServerErrorException) e).getResponseBodyAsString();
                log.info("抖音返回值: {}", responseStr);
            }
            throw rpcSysException(responseStr, url, e);
        }
    }

    /**
     * 远程接口异常
     *
     * @return 异常
     */
    private RemoteException remoteException() {
        return new RemoteException("RPC返回异常状态码");
    }

    /**
     * 验签失败异常
     *
     * @param paramJson 参数
     * @return 异常
     */
    private SysException checkSignFailedException(String paramJson) {
        String msg = String.format("抖音平台验签失败 paramJson: %s", this.removeNewline(paramJson));
        return SysException.of(SysErrorCode.S_RPC_ERROR, paramJson, msg);
    }

    /**
     * 远程调用异常
     *
     * @param response 响应
     * @return 异常
     */
    private SysException rpcSysException(Object response) {
        String responseStr = GsonUtil.objectToJson(response);
        String msg = String.format("抖音平台调用异常 response: %s", responseStr);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg);
    }

    /**
     * 远程调用异常
     *
     * @param responseStr 响应结果
     * @param e           异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, Exception e) {
        String msg = String.format("抖音平台调用异常 response: %s", responseStr);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
    }

    /**
     * 远程调用异常
     *
     * @param responseStr 响应结果
     * @param url         url
     * @param e           异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, String url, Exception e) {
        String msg = String.format("抖音平台调用异常 response: %s, url: %s", responseStr, url);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
    }

    /**
     * 去除换行符
     *
     * @param str 字符串
     * @return 去除换行符后的字符串
     */
    private String removeNewline(String str) {
        return str.replaceAll("[\n\r]", "");
    }

    public static void main(String[] args) {
        TikTokRpc rpc = new TikTokRpc();
        ShopDO shop = ShopDO.builder()
                .shopCode("TIKTOK_XTC")
                .appKey("xxxxx")
                .appSecret("xxxxx")
                .appAccessToken("xxxxx")
                .appRefreshToken("xxxxx")
                .build();
        rpc.getOrder(shop, "6917574993680957166");
    }

}
