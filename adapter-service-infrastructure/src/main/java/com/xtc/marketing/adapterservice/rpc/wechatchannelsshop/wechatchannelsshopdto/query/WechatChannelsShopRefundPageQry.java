package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.query;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 微信视频号小店获取售后单分页列表参数
 */
@Getter
@Setter
@ToString
@Builder
public class WechatChannelsShopRefundPageQry {

    /**
     * 订单创建启始时间（秒级时间戳）
     */
    @SerializedName("begin_create_time")
    private Long beginCreateTime;
    /**
     * 订单创建结束时间（秒级时间戳），end_create_time减去begin_create_time不得大于24小时
     */
    @SerializedName("end_create_time")
    private Long endCreateTime;
    /**
     * 分页参数，从第二页开始传，来源于上一页的返回值
     */
    @SerializedName("next_key")
    private String nextKey;

}
