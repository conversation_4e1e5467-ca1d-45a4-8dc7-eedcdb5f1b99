package com.xtc.marketing.adapterservice.rpc.jitu.jitudto.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 极兔快递轨迹查询响应
 */
@Getter
@Setter
@ToString
public class JtExpressRouteResponse {

    /**
     * 运单号
     */
    private String billCode;
    /**
     * 运单轨迹详情
     */
    private List<JtExpressTraceDetail> details;
    /**
     * 轨迹详情
     */
    @Getter
    @Setter
    @ToString
    public static class JtExpressTraceDetail {

        /**
         * 扫描时间
         */
        private String scanTime;

        /**
         * 轨迹描述
         */
        private String desc;

        /**
         * 扫描类型
         * 1、快件揽收
         * 2、入仓扫描（停用）
         * 3、发件扫描
         * 4、到件扫描
         * 5、出仓扫描
         * 6、入库扫描
         * 7、代理点收入扫描
         * 8、快件取出扫描
         * 9、出库扫描
         * 10、快件签收
         * 11、问题件扫描
         * 12、安检扫描
         * 13、其他扫描
         * 14、退件扫描
         */
        private String scanType;

        /**
         * 问题件类型
         */
        private String problemType;

        /**
         * 扫描网点名称
         */
        private String scanNetworkName;

        /**
         * 扫描网点ID
         */
        private String scanNetworkId;

        /**
         * 业务员姓名
         */
        private String staffName;

        /**
         * 业务员联系方式
         */
        private String staffContact;

        /**
         * 扫描网点联系方式
         */
        private String scanNetworkContact;

        /**
         * 扫描网点省份
         */
        private String scanNetworkProvince;

        /**
         * 扫描网点城市
         */
        private String scanNetworkCity;

        /**
         * 扫描网点区/县
         */
        private String scanNetworkArea;

        /**
         * 上一站(到件)或下一站名称(发件)
         */
        private String nextStopName;

        /**
         * 下一站省份（发件扫描类型时提供）
         */
        private String nextNetworkProvinceName;

        /**
         * 下一站城市（发件扫描类型时提供）
         */
        private String nextNetworkCityName;

        /**
         * 下一站区/县（发件扫描类型时提供）
         */
        private String nextNetworkAreaName;

        /**
         * 退回件状态
         * 0正常件 1退回件（10、快件签收时返回）
         */
        private Integer rebackStatus;

        /**
         * 网点类型
         * 中心/网点/集散/其他
         */
        private String networkType;

        /**
         * 代签收类型
         * 驿站/快递柜 (6、入库扫描时返回)
         */
        private String signByOthersType;

        /**
         * 代收名称 (6、入库扫描时返回)
         */
        private String signByOthersName;

        /**
         * 代收点电话 (6、入库扫描时返回)
         */
        private String signByOthersTel;

        /**
         * 取件码 (6、入库扫描时返回)
         */
        private String pickCode;

    }

}
