package com.xtc.marketing.adapterservice.rpc.sf.sfxmldto;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 顺丰仓库，出库申请单响应
 */
@Setter
@Getter
@ToString
@XStreamAlias("SaleOrder")
public class SfOutboundApplyXmlDTO extends SfResponseXmlDTO {

    /**
     * 出库单号（业务方单号）
     */
    @XStreamAlias("ErpOrder")
    private String erpOrder;

    /**
     * 顺丰出库单号（顺丰单号）
     */
    @XStreamAlias("ShipmentId")
    private String shipmentId;

}
