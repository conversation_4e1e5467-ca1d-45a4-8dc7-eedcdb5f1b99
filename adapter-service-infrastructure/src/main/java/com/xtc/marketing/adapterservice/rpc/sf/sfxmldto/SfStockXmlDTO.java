package com.xtc.marketing.adapterservice.rpc.sf.sfxmldto;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 顺丰仓库查询库存响应
 */
@Setter
@Getter
@ToString
@XStreamAlias("RTInventoryQueryResponse")
public class SfStockXmlDTO extends SfResponseXmlDTO {

    /**
     * 库存数据
     */
    @XStreamAlias("RTInventorys")
    private List<StockResponse> stocks;

    @Data
    @XStreamAlias("RTInventory")
    public static class StockResponse {

        @XStreamAlias("Header")
        private Stock stock;

    }

    @Data
    @XStreamAlias("Header")
    public static class Stock {

        /**
         * 商品编码
         */
        @XStreamAlias("SkuNo")
        private String skuId;

        /**
         * 批号
         */
        @XStreamAlias("Lot")
        private String lot;

        /**
         * 库存状态：10-正品、20-残品
         */
        @XStreamAlias("InventoryStatus")
        private String inventoryStatus;

        /**
         * 总库存数量
         */
        @XStreamAlias("TotalQty")
        private Integer totalQty;

        /**
         * 在库库存数量
         */
        @XStreamAlias("OnHandQty")
        private Integer onHandQty;

        /**
         * 可用库存数量
         */
        @XStreamAlias("AvailableQty")
        private Integer availableQty;

        /**
         * 在途库存数量
         */
        @XStreamAlias("InTransitQty")
        private Integer inTransitQty;

    }

}
