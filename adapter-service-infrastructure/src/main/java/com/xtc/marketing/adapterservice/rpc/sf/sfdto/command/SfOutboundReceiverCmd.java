package com.xtc.marketing.adapterservice.rpc.sf.sfdto.command;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SfOutboundReceiverCmd {

    /**
     * 收件方公司
     */
    @SerializedName("ReceiverCompany")
    private String receiverCompany;

    /**
     * 收件方姓名
     */
    @SerializedName("ReceiverName")
    private String receiverName;

    /**
     * 收件方邮编
     */
    @SerializedName("ReceiverZipCode")
    private String receiverZipCode;

    /**
     * 收件方国家
     */
    @SerializedName("ReceiverCountry")
    private String receiverCountry;

    /**
     * 收件方省份
     */
    @SerializedName("ReceiverProvince")
    private String receiverProvince;

    /**
     * 收件方城市
     */
    @SerializedName("ReceiverCity")
    private String receiverCity;

    /**
     * 收件方区县
     */
    @SerializedName("ReceiverArea")
    private String receiverArea;

    /**
     * 收件方地址
     */
    @SerializedName("ReceiverAddress")
    private String receiverAddress;

}
