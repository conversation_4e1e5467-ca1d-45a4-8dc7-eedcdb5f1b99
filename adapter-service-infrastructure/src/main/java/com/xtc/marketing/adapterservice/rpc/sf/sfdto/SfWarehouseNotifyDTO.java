package com.xtc.marketing.adapterservice.rpc.sf.sfdto;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 顺丰仓库通知
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SfWarehouseNotifyDTO {

    /**
     * 处理成功编码
     */
    public static final String HEAD_SUCCESS = "OK";
    /**
     * 处理失败编码
     */
    public static final String HEAD_FAILURE = "ERR";
    /**
     * 处理成功结果
     */
    public static final String RESULT_SUCCESS = "1";
    /**
     * 处理失败结果
     */
    public static final String RESULT_FAILURE = "2";

    /**
     * 处理编码
     */
    @SerializedName("Head")
    private String head;

    /**
     * 处理结果
     */
    @SerializedName("Result")
    private String result;

    /**
     * 备注
     */
    @SerializedName("Note")
    private String note;

    public static SfWarehouseNotifyDTO success() {
        return SfWarehouseNotifyDTO.builder().head(HEAD_SUCCESS).result(RESULT_SUCCESS).build();
    }

    public static SfWarehouseNotifyDTO failure() {
        return SfWarehouseNotifyDTO.builder().head(HEAD_FAILURE).result(RESULT_FAILURE).build();
    }

}
