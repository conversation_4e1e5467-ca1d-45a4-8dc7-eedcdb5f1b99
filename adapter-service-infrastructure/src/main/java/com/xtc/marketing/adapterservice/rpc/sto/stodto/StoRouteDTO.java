package com.xtc.marketing.adapterservice.rpc.sto.stodto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 申通路由
 */
@Getter
@Setter
@ToString
public class StoRouteDTO extends StoBaseDTO {

    /**
     * 运单号
     */
    private String waybillNo;
    /**
     * 扫描网点名称
     */
    private String opOrgName;
    /**
     * 扫描网点编号
     */
    private String opOrgCode;
    /**
     * 扫描网点所在城市
     */
    private String opOrgCityName;
    /**
     * 扫描网点所在省份
     */
    private String opOrgProvinceName;
    /**
     * 扫描网点电话
     */
    private String opOrgTel;
    /**
     * 扫描时间
     */
    private String opTime;
    /**
     * 扫描类型
     */
    private String scanType;
    /**
     * 扫描员
     */
    private String opEmpName;
    /**
     * 扫描员编号
     */
    private String opEmpCode;
    /**
     * 轨迹描述信息
     */
    private String memo;
    /**
     * 派件员或收件员姓名
     */
    private String bizEmpName;
    /**
     * 派件员或收件员编号
     */
    private String bizEmpCode;
    /**
     * 派件员或收件员电话
     */
    private String bizEmpPhone;
    /**
     * 派件员或收件员电话
     */
    private String bizEmpTel;
    /**
     * 下一站名称
     */
    private String nextOrgName;
    /**
     * 下一站编号
     */
    private String nextOrgCode;
    /**
     * 问题件原因名称
     */
    private String issueName;
    /**
     * 签收人
     */
    private String signoffPeople;
    /**
     * 重量，单位：kg
     */
    private Double weight;
    /**
     * 包号
     */
    private String containerNo;
    /**
     * 寄件网点编号
     */
    private String orderOrgCode;
    /**
     * 寄件网点名称
     */
    private String orderOrgName;
    /**
     * 运输任务号
     */
    private String transportTaskNo;
    /**
     * 车牌号
     */
    private String carNo;
    /**
     * 网点类型编号，0003为转运中心,其它都为独立网点
     */
    private String opOrgTypeCode;
    /**
     * 品牌方名称
     */
    private String partnerName;

}
