package com.xtc.marketing.adapterservice.rpc.sf.sfdto.command;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SfOutboundSenderCmd {

    /**
     * 寄件方公司
     */
    @SerializedName("SenderCompany")
    private String senderCompany;

    /**
     * 寄件方姓名
     */
    @SerializedName("SenderName")
    private String senderName;

    /**
     * 寄件方邮编
     */
    @SerializedName("SenderZipCode")
    private String senderZipCode;

    /**
     * 寄件方国家
     */
    @SerializedName("SenderCountry")
    private String senderCountry;

    /**
     * 寄件方省份
     */
    @SerializedName("SenderProvince")
    private String senderProvince;

    /**
     * 寄件方城市
     */
    @SerializedName("SenderCity")
    private String senderCity;

    /**
     * 寄件方区县
     */
    @SerializedName("SenderArea")
    private String senderArea;

    /**
     * 寄件方地址
     */
    @SerializedName("SenderAddress")
    private String senderAddress;

}
