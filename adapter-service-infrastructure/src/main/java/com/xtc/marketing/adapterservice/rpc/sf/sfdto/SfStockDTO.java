package com.xtc.marketing.adapterservice.rpc.sf.sfdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class SfStockDTO {

    /**
     * skuId
     */
    @SerializedName("SkuNo")
    private String skuId;

    /**
     * 库存状态：
     * 10:正品
     * 20:残品
     */
    @SerializedName("InventoryStatus")
    private String inventoryStatus;

    /**
     * 总库存数量=在库库存数量+在途库存数量
     */
    @SerializedName("TotalQty")
    private Integer totalQty;

    /**
     * 在库库存数量
     */
    @SerializedName("OnHandQty")
    private Integer onHandQty;

    /**
     * 可用库存数量=在库库存数量-占用库存数量
     */
    @SerializedName("AvailableQty")
    private Integer availableQty;

    /**
     * 在途库存数量
     */
    @SerializedName("InTransitQty")
    private Integer inTransitQty;

}
