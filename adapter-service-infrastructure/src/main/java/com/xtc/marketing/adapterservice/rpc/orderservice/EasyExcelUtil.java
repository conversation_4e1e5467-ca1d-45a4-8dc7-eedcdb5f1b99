package com.xtc.marketing.adapterservice.rpc.orderservice;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelRuntimeException;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.google.common.collect.Lists;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.IntFunction;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * EasyExcel工具类
 *
 * <p>可使用如下代码开发下载接口</p>
 * <pre>{@code
 * @GetMapping("/download")
 * public ResponseEntity<byte[]> download(@NotBlank @Length(max = 200) String filePath) {
 *     byte[] download = bizService.download(filePath);
 *     String contentDisposition = EasyExcelUtil.buildWriteHttpContentDisposition("下载文件名");
 *     return ResponseEntity.ok()
 *             .header(HttpHeaders.CONTENT_TYPE, EasyExcelUtil.CONTENT_TYPE_XLSX)
 *             .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition)
 *             .body(download);
 * }
 * }</pre>
 *
 * <p>可使用如下代码开发批量上传接口，成功返回 json 内容，失败返回文件</p>
 * <pre>{@code
 * @PostMapping(value = "/batch-import", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
 * public ResponseEntity<Object> batchImport(@NotNull @RequestPart MultipartFile file) {
 *     byte[] importErrorResult = bizService.batchImport(file);
 *     // 导入出现失败数据时，接口返回失败数据的文件
 *     if (importErrorResult.length > 0) {
 *         String contentDisposition = EasyExcelUtil.buildWriteHttpContentDisposition("上传失败数据文件名");
 *         return ResponseEntity.ok()
 *                 .header(HttpHeaders.CONTENT_TYPE, EasyExcelUtil.CONTENT_TYPE_XLSX)
 *                 .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition)
 *                 .body(importErrorResult);
 *     }
 *     return ResponseEntity.ok()
 *             .contentType(MediaType.APPLICATION_JSON)
 *             .body(Response.buildSuccess());
 * }
 * }</pre>
 */
@Slf4j
public class EasyExcelUtil {

    private EasyExcelUtil() {
    }

    /**
     * xlx 扩展名对应的 Content-Type
     */
    public static final String CONTENT_TYPE_XLS = "application/vnd.ms-excel";
    /**
     * xlsx 扩展名对应的 Content-Type
     */
    public static final String CONTENT_TYPE_XLSX = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    /**
     * 批量操作 100 条存储数据库，实际使用中可以 1000 条，然后清理 list 方便内存回收
     */
    public static final int DEFAULT_BATCH_COUNT = 100;

    /**
     * 默认 sheet 页命名
     */
    private static final String DEFAULT_SHEET_NAME = "sheet";
    /**
     * 默认循环次数
     */
    private static final int DEFAULT_LOOP_LIMIT = 1000;
    /**
     * 默认 sheet 行数
     */
    private static final int DEFAULT_SHEET_ROW = 500000;
    /**
     * 默认行数限制
     */
    private static final int DEFAULT_ROW_LIMIT = 2000000;

    /**
     * 生成文件下载的 HTTP {@code Content-Disposition}
     *
     * @param fileName 文件名
     * @return HTTP {@code Content-Disposition}
     * @throws UnsupportedEncodingException 编码异常
     */
    public static String buildWriteHttpContentDisposition(String fileName) throws UnsupportedEncodingException {
        String encodeFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()).replace("\\+", "%20");
        return "attachment;filename*=utf-8''" + encodeFileName + ExcelTypeEnum.XLSX.getValue();
    }

    /**
     * 写excel
     *
     * @param clazz easyexcel 映射类
     * @param data  数据
     * @param <T>   easyexcel 映射类的类型
     * @return 字节数组
     */
    public static <T> byte[] writeExcel(Class<T> clazz, List<T> data) {
        return writeExcel(DEFAULT_SHEET_NAME, clazz, data);
    }

    /**
     * 写excel
     *
     * @param sheetName sheet名称
     * @param clazz     easyexcel 映射类
     * @param data      数据
     * @param <T>       easyexcel 映射类的类型
     * @return 字节数组
     */
    public static <T> byte[] writeExcel(String sheetName, Class<T> clazz, List<T> data) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream(0);
        EasyExcelFactory.write(outputStream)
                .excelType(ExcelTypeEnum.XLSX)
                .charset(StandardCharsets.UTF_8)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .sheet(sheetName != null ? sheetName : DEFAULT_SHEET_NAME)
                .head(clazz)
                .doWrite(data);
        return outputStream.toByteArray();
    }

    /**
     * 写excel
     *
     * @param head 表头
     * @param data 数据
     * @param <T>  easyexcel 映射类的类型
     * @return 字节数组
     */
    public static <T> byte[] writeCustomHeadExcel(List<List<String>> head, List<T> data) {
        return writeCustomHeadExcel(DEFAULT_SHEET_NAME, head, data);
    }

    /**
     * 写excel
     *
     * @param sheetName sheet名称
     * @param head      表头
     * @param data      数据
     * @param <T>       easyexcel 映射类的类型
     * @return 字节数组
     */
    public static <T> byte[] writeCustomHeadExcel(String sheetName, List<List<String>> head, List<T> data) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream(0);
        EasyExcelFactory.write(outputStream)
                .excelType(ExcelTypeEnum.XLSX)
                .charset(StandardCharsets.UTF_8)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .sheet(sheetName != null ? sheetName : DEFAULT_SHEET_NAME)
                .head(head)
                .doWrite(data);
        return outputStream.toByteArray();
    }

    /**
     * 写excel
     * <p>分页加入数据，避免一次获取太多数据到 List 导致内存溢出</p>
     *
     * @param clazz     easyexcel 映射类
     * @param batchData 分批获取数据方法，参数：批次索引
     * @param <T>       easyexcel 映射类的类型
     * @return 字节数组
     */
    public static <T> byte[] writeExcelBatch(Class<T> clazz, IntFunction<List<T>> batchData) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream(0);
        try (ExcelWriter excelWriter = EasyExcelFactory.write(outputStream, clazz).build()) {
            // 初始化 sheet 页
            WriteSheet excelSheet = EasyExcelFactory.writerSheet(DEFAULT_SHEET_NAME).build();
            // 分批写入数据
            List<T> data;
            int index = 0;
            do {
                data = batchData.apply(index);
                excelWriter.write(data, excelSheet);
                index++;
            } while (CollectionUtils.isNotEmpty(data) && index < 1000);
        } catch (Exception e) {
            throw exportException(e);
        }
        return outputStream.toByteArray();
    }

    /**
     * 写excel
     * <p>分页加入数据，避免一次获取太多数据到 List 导致内存溢出</p>
     *
     * @param file      写入的excel文件
     * @param clazz     easyexcel 映射类
     * @param batchData 分批获取数据方法，参数：批次索引，上一批次最后一个数据
     * @param <T>       easyexcel 映射类的类型
     */
    public static <T> void writeExcelBatch(File file, Class<T> clazz, BiFunction<Integer, T, List<T>> batchData) {
        try (ExcelWriter excelWriter = EasyExcelFactory.write(file, clazz).build()) {
            // 初始化 sheet 页
            int sheetPage = 1;
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(DEFAULT_SHEET_NAME + sheetPage).build();
            // 分页写入数据
            List<T> data;
            T lastData = null;
            int dataCount = 0;
            for (int i = 0; i < DEFAULT_LOOP_LIMIT; i++) {
                data = batchData.apply(i, lastData);
                // 数据为空结束循环
                if (data == null || data.isEmpty()) {
                    break;
                }
                // 记录数据行数，并且做默认限制
                dataCount = dataCount + data.size();
                if (dataCount > DEFAULT_ROW_LIMIT) {
                    throw new ExcelRuntimeException("数据超过 " + DEFAULT_ROW_LIMIT + " 行数限制，请分多个文件导出");
                }
                // 数据分页
                if (dataCount > DEFAULT_SHEET_ROW * sheetPage) {
                    sheetPage++;
                    writeSheet = EasyExcelFactory.writerSheet(DEFAULT_SHEET_NAME + sheetPage).build();
                }
                // 写数据
                excelWriter.write(data, writeSheet);
                // 缓存上一次分页最后一个数据，下一次分页时，可以根据该数据进行 sql 筛选查询，提高大数据量分页查询效率
                // 最佳实践：select * from t_data where id >/< lastData.getId() order by id asc/desc limit 1000
                lastData = data.get(data.size() - 1);
            }
        } catch (Exception e) {
            throw exportException(e);
        }
    }

    /**
     * 读excel
     * <p>默认表头有 1 行</p>
     *
     * @param excel    excel文件
     * @param clazz    easyexcel 映射类
     * @param consumer 处理逻辑
     * @param <T>      easyexcel 映射类的类型
     */
    public static <T> void readExcel(InputStream excel, Class<T> clazz, Consumer<List<T>> consumer) {
        readExcel(excel, 1, clazz, new CommonReadListener<>(consumer));
    }

    /**
     * 读excel
     *
     * @param excel         excel文件
     * @param headRowNumber 表头行数
     * @param clazz         easyexcel 映射类
     * @param listener      easyexcel 监听器
     * @param <T>           easyexcel 映射类的类型
     */
    public static <T> void readExcel(InputStream excel, int headRowNumber, Class<T> clazz, AnalysisEventListener<T> listener) {
        EasyExcelFactory.read(excel)
                .autoTrim(true)
                .headRowNumber(headRowNumber)
                .head(clazz)
                .registerReadListener(listener)
                .sheet()
                .doRead();
    }

    /**
     * 读excel，使用事务处理，返回失败数据的文件
     * <p>业务处理逻辑：每次处理单个数据，达到批量提交个数后，通过事务提交</p>
     * <p>默认表头有 1 行</p>
     *
     * @param excel              excel文件
     * @param transactionManager 事务管理器
     * @param clazz              easyexcel 映射类
     * @param bizConsumer        业务处理逻辑（单个数据）
     * @param <T>                easyexcel 映射类的类型
     */
    public static <T> byte[] readExcelWithTransactionThenReturnByte(InputStream excel,
                                                                    PlatformTransactionManager transactionManager,
                                                                    Class<T> clazz,
                                                                    BizConsumer<T> bizConsumer) {
        return readExcelWithTransactionThenReturnByte(excel, 1, transactionManager, clazz, bizConsumer);
    }

    /**
     * 读excel，使用事务处理，返回失败数据的文件
     * <p>业务处理逻辑：每次处理单个数据，达到批量提交个数后，通过事务提交</p>
     *
     * @param excel              excel文件
     * @param headRowNumber      表头行数
     * @param transactionManager 事务管理器
     * @param clazz              easyexcel 映射类
     * @param bizConsumer        业务处理逻辑（单个数据）
     * @param <T>                easyexcel 映射类的类型
     */
    public static <T> byte[] readExcelWithTransactionThenReturnByte(InputStream excel,
                                                                    int headRowNumber,
                                                                    PlatformTransactionManager transactionManager,
                                                                    Class<T> clazz,
                                                                    BizConsumer<T> bizConsumer) {
        // 创建事务监听器，缓存原始表头和异常数据
        TransactionReadListener<T> listenerWithTransaction = new TransactionReadListener<>(transactionManager, bizConsumer);
        // 读取文件数据并处理
        EasyExcelUtil.readExcel(excel, headRowNumber, clazz, listenerWithTransaction);
        List<List<String>> errorData = listenerWithTransaction.getErrorData();
        if (CollectionUtils.isEmpty(listenerWithTransaction.getErrorData())) {
            log.info("成功导入全部数据");
            return new byte[]{};
        }
        // 导出异常数据
        log.info("批量导入存在失败数据 {} 条", errorData.size());
        // 导入的原始行增加一列失败原因，然后把行转成列，空列用空字符串填充
        List<List<String>> rowColumnHead = listenerWithTransaction.getHead();
        rowColumnHead.get(rowColumnHead.size() - 1).add("失败原因");
        int maxColumn = rowColumnHead.stream().mapToInt(List::size).max().orElse(0);
        List<List<String>> columnRowHead = IntStream.range(0, maxColumn)
                .mapToObj(column -> rowColumnHead.stream()
                        .map(row -> column < row.size() ? row.get(column) : "")
                        .collect(Collectors.toList()))
                .collect(Collectors.toList());
        // 导出异常数据，使用原始表头，并增加一列失败原因
        return EasyExcelUtil.writeCustomHeadExcel("失败数据调整后可继续导入", columnRowHead, errorData);
    }

    /**
     * 生成导出异常
     *
     * @param e 异常
     * @return ExcelRuntimeException
     */
    private static ExcelRuntimeException exportException(Exception e) {
        return new ExcelRuntimeException("导出Excel异常：" + e.getMessage(), e);
    }

    /**
     * 业务处理逻辑
     *
     * @param <T> easyexcel 映射类的类型
     */
    @Getter
    @Setter
    @AllArgsConstructor
    public static class BizConsumer<T> {

        /**
         * 业务名称
         */
        private String bizName;
        /**
         * 业务处理逻辑
         */
        private Consumer<T> consumer;

    }

    /**
     * 通用读取监听器
     * <p>业务处理逻辑：每次处理批量数据，无事务</p>
     *
     * @param <T> easyexcel 映射类的类型
     */
    private static class CommonReadListener<T> extends AnalysisEventListener<T> {

        /**
         * 缓存数据
         */
        private final List<T> list = new ArrayList<>(DEFAULT_BATCH_COUNT);
        /**
         * 业务处理逻辑
         */
        private final Consumer<List<T>> bizConsumer;

        public CommonReadListener(Consumer<List<T>> bizConsumer) {
            this.bizConsumer = bizConsumer;
        }

        @Override
        public void invoke(T data, AnalysisContext context) {
            list.add(data);
            // 达到 BATCH_COUNT 了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
            if (list.size() >= DEFAULT_BATCH_COUNT) {
                bizConsumer.accept(list);
                // 保存完成清空 list
                list.clear();
            }
        }

        /**
         * 所有数据解析完成就会调用
         */
        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            if (!list.isEmpty()) {
                // 这里也要保存数据，确保最后遗留的数据也存储到数据库
                bizConsumer.accept(list);
            }
        }

    }

    /**
     * 事务读取监听器
     * <p>业务处理逻辑：每次处理单个数据，达到批量提交个数后，通过事务提交</p>
     *
     * @param <T> easyexcel 映射类的类型
     */
    private static class TransactionReadListener<T> extends AnalysisEventListener<T> {

        /**
         * 缓存表头数据
         */
        @Getter
        private final List<List<String>> head = Lists.newArrayList();
        /**
         * 缓存异常数据
         */
        @Getter
        private final List<List<String>> errorData = Lists.newArrayList();
        /**
         * 业务处理逻辑（单个数据）
         */
        private final BizConsumer<T> bizConsumer;
        /**
         * 事务管理器（建议使用 spring 管理的事务）
         */
        private final PlatformTransactionManager transactionManager;
        /**
         * 事务配置
         */
        private final TransactionDefinition transactionDefinition;
        /**
         * 缓存事务状态，每个批次一个事务，避免长事务操作
         */
        private TransactionStatus transactionStatus;
        /**
         * 缓存当前批次处理数量，避免批量数据过多操作
         */
        private int count = 0;
        /**
         * 缓存当前批次
         */
        private int batchCount = 0;

        /**
         * 构造事务读取监听器
         *
         * @param transactionManager 事务管理器
         * @param bizConsumer        业务处理逻辑（单个数据）
         */
        public TransactionReadListener(PlatformTransactionManager transactionManager, BizConsumer<T> bizConsumer) {
            this.transactionManager = transactionManager;
            this.bizConsumer = bizConsumer;
            // 设置事务配置：创建新事务，超时时间 5 秒
            DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
            definition.setName("easyexcel-transaction-" + bizConsumer.bizName);
            definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            definition.setTimeout(5);
            this.transactionDefinition = definition;
        }

        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            super.invokeHeadMap(headMap, context);
            head.add(Lists.newArrayList(headMap.values()));
        }

        @Override
        public void invoke(T data, AnalysisContext context) {
            // 开启事务
            if (transactionStatus == null) {
                transactionStatus = transactionManager.getTransaction(transactionDefinition);
                log.info("开启事务 [{}]", transactionDefinition.getName());
            }
            log.info("读取数据 {}", GsonUtil.objectToJson(data));
            // 执行业务逻辑
            bizConsumer.getConsumer().accept(data);
            // 处理数量累计
            count++;
            // 达到批次处理数量，提交当前事务，重置缓存数据
            if (count >= DEFAULT_BATCH_COUNT) {
                this.commit();
            }
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // 提交当前事务
            this.commit();
            // 重置批次计数
            batchCount = 0;
        }

        @Override
        public void onException(Exception exception, AnalysisContext context) {
            ReadRowHolder readRowHolder = context.readRowHolder();
            // 表头不处理
            if (readRowHolder.getRowIndex() < context.readWorkbookHolder().getHeadRowNumber()) {
                return;
            }
            // 处理异常数据行，如果数据列与最后的表头列长度不等时，填充空字符串
            int lastHeadSize = head.get(head.size() - 1).size();
            List<String> errorRow = IntStream.range(0, lastHeadSize)
                    .mapToObj(column -> Optional.ofNullable(readRowHolder.getCellMap().get(column))
                            .map(CellData.class::cast).map(CellData::getStringValue).orElse(""))
                    .collect(Collectors.toList());
            // 在行数据中加入异常信息
            errorRow.add(exception.getMessage());
            log.warn("处理数据失败 {}", GsonUtil.objectToJson(errorRow));
            // 记录异常数据
            errorData.add(errorRow);
        }

        /**
         * 提交当前事务
         */
        private void commit() {
            // 提交当前事务
            transactionManager.commit(transactionStatus);
            // 批次计数累计
            batchCount++;
            log.info("提交事务 [{}] batchCount: {}, count: {}", transactionDefinition.getName(), batchCount, count);
            // 重置缓存数据
            this.reset();
        }

        /**
         * 重置缓存数据
         */
        private void reset() {
            // 重置事务
            transactionStatus = null;
            // 重置计数
            count = 0;
        }

    }

}
