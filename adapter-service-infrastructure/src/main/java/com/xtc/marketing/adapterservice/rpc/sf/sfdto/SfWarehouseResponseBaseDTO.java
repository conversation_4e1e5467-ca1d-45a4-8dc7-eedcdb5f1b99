package com.xtc.marketing.adapterservice.rpc.sf.sfdto;

import com.google.gson.JsonElement;
import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class SfWarehouseResponseBaseDTO {

    /**
     * 请求成功代码
     */
    public static final String SUCCESS_CODE = "OK";

    @SerializedName("Head")
    private String code;

    private JsonElement msgData;

    public boolean isFailure() {
        return !SUCCESS_CODE.equals(code);
    }

    public boolean success() {
        return !isFailure();
    }

}
