package com.xtc.marketing.adapterservice.rpc.sf.sfdto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 顺丰快递路由
 */
@Getter
@Setter
@ToString
public class SfRouteDTO {

    /**
     * 路由节点发生的时间，格式：YYYY-MM-DD HH24:MM:SS，示例：2012-7-30 09:30:00
     */
    private String acceptTime;
    /**
     * 路由节点发生的地点
     */
    private String acceptAddress;
    /**
     * 路由节点具体描述
     */
    private String remark;
    /**
     * 路由节点操作码
     */
    private String opCode;

}
