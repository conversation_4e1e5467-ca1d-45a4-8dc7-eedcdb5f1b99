package com.xtc.marketing.adapterservice.rpc.jd.jdexpressdto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 京东快递响应DTO
 *
 * @param <T> 业务数据类型
 */
@Getter
@Setter
@ToString
public class JdExpressResponseDTO<T> {

    /**
     * 成功响应码
     */
    private static final String SUCCESS_CODE = "0";

    /**
     * 响应码
     */
    private String code;
    /**
     * 业务响应
     */
    private Result<T> result;

    /**
     * 判断响应失败
     *
     * @return 执行结果
     */
    public boolean failure() {
        return !SUCCESS_CODE.equals(code) || result == null || !SUCCESS_CODE.equals(result.getCode());
    }

    /**
     * 获取业务数据
     *
     * @return 业务数据
     */
    public T getBizData() {
        if (failure()) {
            return null;
        }
        return result.getData();
    }

    /**
     * 业务响应
     *
     * @param <T> 业务数据类型
     */
    @Getter
    @Setter
    @ToString
    public static class Result<T> {

        /**
         * 响应码
         */
        private String code;
        /**
         * 业务数据
         */
        private T data;

    }

}
