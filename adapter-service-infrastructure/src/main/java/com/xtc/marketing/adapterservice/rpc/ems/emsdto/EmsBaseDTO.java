package com.xtc.marketing.adapterservice.rpc.ems.emsdto;

import com.google.gson.JsonElement;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class EmsBaseDTO {

    /**
     * 请求成功代码
     */
    public static final String SUCCESS_CODE = "S00";

    /**
     * 请求成功状态
     */
    private String message;

    /**
     * 返回编码
     */
    private String code;

    /**
     * 返回体
     */
    private JsonElement body;

    public boolean isFailure() {
        return !SUCCESS_CODE.equals(code);
    }

}
