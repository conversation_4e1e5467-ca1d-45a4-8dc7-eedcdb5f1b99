package com.xtc.marketing.adapterservice.rpc.sf.sfdto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class SfResponseBaseDTO {

    /**
     * 请求成功代码
     */
    public static final String SUCCESS_CODE = "A1000";

    private String apiErrorMsg;

    private String apiResponseID;

    private String apiResultCode;

    private String apiResultData;

    public boolean isFailure() {
        return !SUCCESS_CODE.equals(apiResultCode);
    }

}
