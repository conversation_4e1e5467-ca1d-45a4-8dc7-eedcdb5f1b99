package com.xtc.marketing.adapterservice.rpc.dividendmch;

import org.springframework.web.multipart.MultipartFile;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.util.Base64;

public class AesUtil {

    private static final String KEY_AES = "AES";

    public static String dataKey = "jFeDhfd1rHflxSZ5";

    private AesUtil() {
    }

    public static String encrypt(String src, String key) throws Exception {
        byte[] raw = key.getBytes();
        SecretKeySpec skeySpec = new SecretKeySpec(raw, KEY_AES);
        Cipher cipher = Cipher.getInstance(KEY_AES);
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(src.getBytes());
        return byte2hex(encrypted);
    }

    public static String decrypt(String src, String key) throws Exception {
        byte[] raw = key.getBytes();
        SecretKeySpec skeySpec = new SecretKeySpec(raw, KEY_AES);
        Cipher cipher = Cipher.getInstance(KEY_AES);
        cipher.init(Cipher.DECRYPT_MODE, skeySpec);
        byte[] encrypted1 = hex2byte(src);
        byte[] original = cipher.doFinal(encrypted1);
        String originalString = new String(original);
        return originalString;
    }

    public static byte[] hex2byte(String strhex) {
        if (strhex == null) {
            return null;
        }
        int l = strhex.length();
        if (l % 2 == 1) {
            return null;
        }
        byte[] b = new byte[l / 2];
        for (int i = 0; i != l / 2; i++) {
            b[i] = (byte) Integer.parseInt(strhex.substring(i * 2, i * 2 + 2),
                    16);
        }
        return b;
    }

    public static String byte2hex(byte[] b) {
        String hs = "";
        String stmp = "";
        for (int n = 0; n < b.length; n++) {
            stmp = (Integer.toHexString(b[n] & 0XFF));
            if (stmp.length() == 1) {
                hs = hs + "0" + stmp;
            } else {
                hs = hs + stmp;
            }
        }
        return hs.toUpperCase();
    }

    public static String encryptData(String sourceStr) throws Exception {
        String encrypt = null;
        try {
            encrypt = AesUtil.encrypt(sourceStr, AesUtil.dataKey);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("加密异常");
        }
        return encrypt;
    }

    public static String decryptData(String sourceStr) throws Exception {
        String decrypt = null;
        try {
            decrypt = AesUtil.decrypt(sourceStr, AesUtil.dataKey);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("解密异常");
        }
        return decrypt;
    }

    public static String decryptDataToString(String sourceStr) throws Exception {
        try {
            String base64 = decryptData(sourceStr);
            byte[] decode = Base64.getDecoder().decode(base64);
            String data = new String(decode, "UTF-8");
            return data;
        } catch (Exception e) {
            throw new Exception("解密异常");
        }
    }

    /**
     * 文件通过base64转换成字符串,然后加密
     */
    public static String fileToString(MultipartFile file) throws Exception {
        if (file == null || file.isEmpty()) {
            return "";
        }
        try {
            String encode = Base64.getEncoder().encodeToString(file.getBytes());
            return AesUtil.encryptData(encode);
        } catch (IOException e) {
            throw new Exception("文件读取异常" + e.getMessage());
        }
    }

}
