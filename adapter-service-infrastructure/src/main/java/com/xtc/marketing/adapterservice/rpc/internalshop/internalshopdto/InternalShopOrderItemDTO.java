package com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 内部购机订单明细
 */
@Getter
@Setter
@ToString
public class InternalShopOrderItemDTO {

    /**
     * 子订单号
     */
    @SerializedName("order_items_id")
    private Integer orderItemsId;

    /**
     * 卖家id
     */
    @SerializedName("seller_id")
    private Integer sellerId;

    /**
     * 卖家姓名
     */
    @SerializedName("seller_name")
    private String sellerName;

    /**
     * 商品id
     */
    @SerializedName("goods_id")
    private Integer goodsId;

    /**
     * skuId
     */
    @SerializedName("sku_id")
    private Integer skuId;

    /**
     * 物料代码集合，使用逗号分割
     */
    @SerializedName("sku_sn")
    private String skuSn;

    /**
     * 商品所属的分类id
     */
    @SerializedName("cat_id")
    private Integer catId;

    /**
     * 购买数量
     */
    @SerializedName("num")
    private Integer num;

    /**
     * 商品重量
     */
    @SerializedName("goods_weight")
    private BigDecimal goodsWeight;

    /**
     * 商品原价
     */
    @SerializedName("original_price")
    private BigDecimal originalPrice;

    /**
     * 购买时的成交价
     */
    @SerializedName("purchase_price")
    private BigDecimal purchasePrice;

    /**
     * 小计
     */
    @SerializedName("subtotal")
    private BigDecimal subtotal;

    /**
     * 商品名称
     */
    @SerializedName("name")
    private String goodsName;

    /**
     * 商品图片
     */
    @SerializedName("goods_image")
    private String goodsImage;

    /**
     * 售后状态
     */
    @SerializedName("service_status")
    private String serviceStatus;

}
