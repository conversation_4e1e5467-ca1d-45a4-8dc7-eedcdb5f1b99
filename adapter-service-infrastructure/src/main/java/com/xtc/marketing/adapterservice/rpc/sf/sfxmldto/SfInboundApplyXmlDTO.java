package com.xtc.marketing.adapterservice.rpc.sf.sfxmldto;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 顺丰仓库，入库申请单响应
 */
@Setter
@Getter
@ToString
@XStreamAlias("PurchaseOrder")
public class SfInboundApplyXmlDTO extends SfResponseXmlDTO {

    /**
     * 入库单号（业务方单号）
     */
    @XStreamAlias("ErpOrder")
    private String orderId;

    /**
     * 顺丰入库单号（顺丰单号）
     */
    @XStreamAlias("ReceiptId")
    private String receiptId;

}
