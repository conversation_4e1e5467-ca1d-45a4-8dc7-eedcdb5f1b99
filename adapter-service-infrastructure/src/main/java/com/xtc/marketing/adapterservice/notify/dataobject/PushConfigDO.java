package com.xtc.marketing.adapterservice.notify.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xtc.marketing.adapterservice.config.BaseDO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
@TableName("t_push_config")
public class PushConfigDO extends BaseDO {

    /**
     * 业务名称
     */
    private String bizName;
    /**
     * 模块代码
     */
    private String moduleCode;
    /**
     * 平台代码
     */
    private String platformCode;
    /**
     * 场景代码
     */
    private String scenarioCode;
    /**
     * 推送地址
     */
    private String pushUrl;
    /**
     * 成功的响应码，例：code:0000001
     */
    private String successCode;
    /**
     * 启用
     */
    private Boolean enabled;
    /**
     * 上一次推送的时间
     */
    private LocalDateTime lastPushTime;

}
