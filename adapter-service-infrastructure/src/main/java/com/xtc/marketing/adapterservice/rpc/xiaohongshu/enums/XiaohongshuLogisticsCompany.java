package com.xtc.marketing.adapterservice.rpc.xiaohongshu.enums;

import lombok.Getter;

/**
 * 小红书快递公司
 */
@Getter
public enum XiaohongshuLogisticsCompany {
    /**
     * 圆通
     */
    YTO("圆通", "yuantong", "", "", "", "754096", 175028L),
    /**
     * 京东
     */
    JD("京东", "jd", "ed-m-0001", "020K2415994", "", "", 175096L),
    /**
     * 顺丰
     */
    SF("顺丰", "shunfeng", "2", "7698089145", "SF", "", 175002L),
    /**
     * EMS
     */
    EMS("EMS", "ems", "", "1030004233095", "", "", 175056L),
    ;

    /**
     * 快递公司名称
     */
    private final String name;

    /**
     * 快递公司代码
     */
    private final String code;

    /**
     * 快递产品类型
     */
    private final String type;

    /**
     * 月结卡号，直营快递公司（顺丰 、邮政、京东、德邦等）要求必填，加盟快递传空字符串
     */
    private final String monthlyCard;

    /**
     * 品牌编码，顺丰要求必填，其他快递不传或者空字符串
     */
    private final String brandCode;

    /**
     * 网点编码，加盟型快递公司要求必填，直营快递（顺丰 、邮政、京东、德邦等）传空字符
     */
    private final String branchCode;

    /**
     * 电子面单模板id
     */
    private final Long templateId;


    XiaohongshuLogisticsCompany(String name, String code, String type, String monthlyCard, String brandCode, String branchCode, Long templateId) {
        this.name = name;
        this.code = code;
        this.type = type;
        this.monthlyCard = monthlyCard;
        this.brandCode = brandCode;
        this.branchCode = branchCode;
        this.templateId = templateId;
    }
}
