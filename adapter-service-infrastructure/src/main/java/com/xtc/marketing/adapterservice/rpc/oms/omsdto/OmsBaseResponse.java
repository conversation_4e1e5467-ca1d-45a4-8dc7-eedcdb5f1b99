package com.xtc.marketing.adapterservice.rpc.oms.omsdto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * OMS系统响应基类
 */
@Getter
@Setter
@ToString
public class OmsBaseResponse<T> {

    /**
     * 成功代码
     */
    private static final String SUCCESS_CODE = "0000001";

    /**
     * 成功标识（两种响应结构）
     */
    private Boolean success;
    /**
     * 响应码
     */
    private String code;
    /**
     * 响应描述
     */
    private String desc;
    /**
     * 响应数据
     */
    private T data;

    /**
     * 判断响应成功
     *
     * @return 执行结果
     */
    public boolean isSuccess() {
        return !isFailure();
    }

    /**
     * 判断响应失败
     *
     * @return 执行结果
     */
    public boolean isFailure() {
        if (this.code != null) {
            return !SUCCESS_CODE.equals(this.code);
        }
        if (this.success != null) {
            return !this.success;
        }
        return true;
    }

}
