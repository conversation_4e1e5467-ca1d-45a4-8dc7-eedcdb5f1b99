package com.xtc.marketing.adapterservice.rpc.wechatpay;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.ijpay.core.IJPayHttpResponse;
import com.ijpay.core.enums.RequestMethod;
import com.ijpay.core.enums.SignType;
import com.ijpay.core.kit.PayKit;
import com.ijpay.core.kit.WxPayKit;
import com.ijpay.wxpay.WxPayApi;
import com.ijpay.wxpay.enums.WxApiType;
import com.ijpay.wxpay.enums.WxDomain;
import com.ijpay.wxpay.model.v3.RefundAmount;
import com.ijpay.wxpay.model.v3.RefundModel;
import com.ijpay.wxpay.model.v3.UnifiedOrderModel;
import com.xtc.dividendcenter.dividend.dto.InnerWeChatCertificateDTO;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.wechatpay.wechatdto.WechatPayBillDTO;
import com.xtc.marketing.adapterservice.rpc.wechatpay.wechatdto.command.WechatRefundCmd;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.security.PrivateKey;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.GZIPInputStream;

/**
 * 微信支付RPC
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class WechatPayRpc {

    /**
     * 支付完成时间数据的时间格式
     */
    public static final String SUCCESS_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss+08:00";
    /**
     * 微信支付签名类型
     */
    private static final String WECHATPAY_SIGNATURE_TYPE = "WECHATPAY2-SHA256-RSA2048";

    /**
     * 申请交易账单 gzip 压缩格式
     *
     * @param mch      商户
     * @param billDate 账单日期
     * @return 账单
     */
    public WechatPayBillDTO getTradeBill(InnerWeChatCertificateDTO mch, String billDate) {
        String urlSuffix = WxApiType.TRADE_BILL.toString().concat("?bill_date=").concat(billDate)
                .concat("&tar_type=").concat("GZIP");
        IJPayHttpResponse response = this.getWxPayApiV3(urlSuffix, mch);
        return GsonUtil.jsonToBean(response.getBody(), WechatPayBillDTO.class);
    }

    /**
     * 下载账单文件
     *
     * @param mch         商户
     * @param downloadUrl 下载地址
     * @return 账单文件流
     */
    public Resource billDownload(InnerWeChatCertificateDTO mch, String downloadUrl) {
        String params = UriComponentsBuilder.fromHttpUrl(downloadUrl).build().getQuery();
        String urlSuffix = WxApiType.BILL_DOWNLOAD.toString().concat("?").concat(StringUtils.defaultString(params));

        // 构建签名请求头
        Map<String, String> headers;
        try {
            long timestamp = System.currentTimeMillis() / 1000;
            PrivateKey privateKey = PayKit.getPrivateKeyByKeyContent(mch.getKeyPem().getEncryptCertificate());
            String authorization = WxPayKit.buildAuthorization(RequestMethod.GET, urlSuffix,
                    mch.getMchId(), mch.getMerchantCert().getSerialNo(), privateKey,
                    "", WxPayKit.generateStr(), timestamp, WECHATPAY_SIGNATURE_TYPE);
            headers = WxPayApi.getHeaders(authorization, mch.getPlatFormCert().getSerialNo());
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, "微信支付调用异常 下载账单功能构建签名异常", e);
        }

        // 请求下载接口，解压缩 gzip 流，返回解压后的文件流
        try (HttpResponse httpResponse = HttpRequest.get(downloadUrl).addHeaders(headers).execute()) {
            GZIPInputStream unzipInputStream = new GZIPInputStream(httpResponse.bodyStream());
            return new InputStreamResource(unzipInputStream);
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, "微信支付调用异常 下载账单功能请求失败", e);
        }
    }

    /**
     * 微信支付 JSAPI 下单，生成预支付参数
     * <p><a href="https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_1_1.shtml">JSAPI 下单文档</a>
     *
     * @param mch               商户
     * @param unifiedOrderModel 下单参数
     * @return 预支付参数
     */
    public Map<String, Object> jsApi(InnerWeChatCertificateDTO mch, UnifiedOrderModel unifiedOrderModel) {
        IJPayHttpResponse response = this.postWxPayApiV3(WxApiType.JS_API_PAY.toString(), mch, unifiedOrderModel);
        try {
            return this.jsApiCreateSign(mch.getWxAppId(), response.getBody(), mch.getKeyPem().getEncryptCertificate());
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, "微信支付唤起支付失败", e);
        }
    }

    /**
     * 支付查询
     *
     * @param mch        商户
     * @param outTradeNo 商户订单号
     * @return 支付结果
     */
    public String payQuery(InnerWeChatCertificateDTO mch, String outTradeNo) {
        String urlSuffix = String.format(WxApiType.ORDER_QUERY_BY_NO.toString(), outTradeNo)
                .concat("?mchid=").concat(mch.getMchId());
        IJPayHttpResponse response = this.getWxPayApiV3(urlSuffix, mch);
        return response.getBody();
    }

    /**
     * 服务商 JSAPI 下单，生成预支付参数
     *
     * @param mch               商户
     * @param unifiedOrderModel 下单参数
     * @return 预支付参数
     */
    public Map<String, Object> partnerJsApi(InnerWeChatCertificateDTO mch, UnifiedOrderModel unifiedOrderModel) {
        IJPayHttpResponse response = this.postWxPayApiV3(WxApiType.PARTNER_JS_API_PAY.toString(), mch, unifiedOrderModel);
        try {
            return this.jsApiCreateSign(mch.getWxAppId(), response.getBody(), mch.getKeyPem().getEncryptCertificate());
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, "微信支付唤起支付失败", e);
        }
    }

    /**
     * 支付查询-服务商
     *
     * @param mch        商户
     * @param outTradeNo 商户订单号
     * @param subMchid   子商户号
     * @return 支付结果
     */
    public String partnerPayQuery(InnerWeChatCertificateDTO mch, String outTradeNo, String subMchid) {
        String urlSuffix = String.format(WxApiType.PARTNER_ORDER_QUERY_BY_NO.toString(), outTradeNo)
                .concat("?sp_mchid=").concat(mch.getMchId())
                .concat("&sub_mchid=").concat(subMchid);
        IJPayHttpResponse response = this.getWxPayApiV3(urlSuffix, mch);
        return response.getBody();
    }

    /**
     * 订单关闭支付
     *
     * @param mch        商户
     * @param outTradeNo 商户订单号
     */
    public void orderPayClose(InnerWeChatCertificateDTO mch, String outTradeNo) {
        String urlSuffix = String.format(WxApiType.CLOSE_ORDER_BY_NO.toString(), outTradeNo);
        UnifiedOrderModel closeOrderModel = new UnifiedOrderModel().setMchid(mch.getMchId());
        IJPayHttpResponse response = this.postWxPayApiV3(urlSuffix, mch, closeOrderModel);
        log.info("微信支付-关闭订单 {}", response.getBody());
    }

    /**
     * 退款
     *
     * @param mch 商户
     * @param cmd 微信退款参数
     * @return 退款结果
     */
    public String refund(InnerWeChatCertificateDTO mch, WechatRefundCmd cmd) {
        RefundModel refundModel = new RefundModel()
                .setTransaction_id(cmd.getTransactionId())
                .setOut_refund_no(PayKit.generateStr())
                .setNotify_url(cmd.getNotifyUrl())
                .setAmount(new RefundAmount().setRefund(cmd.getRefundAmount()).setTotal(cmd.getTotalAmount()).setCurrency("CNY"));
        IJPayHttpResponse response = this.postWxPayApiV3(WxApiType.DOMESTIC_REFUNDS.toString(), mch, refundModel);
        return response.getBody();
    }

    /**
     * 退款查询
     *
     * @param mch         商户
     * @param outRefundNo 商户退款单号
     * @return 退款结果
     */
    public String refundQuery(InnerWeChatCertificateDTO mch, String outRefundNo) {
        String urlSuffix = String.format(WxApiType.DOMESTIC_REFUNDS_QUERY.toString(), outRefundNo);
        IJPayHttpResponse response = this.getWxPayApiV3(urlSuffix, mch);
        return response.getBody();
    }

    /**
     * 异步通知解析（支付、退款）
     *
     * @param request 请求
     * @param body    异步通知密文
     * @param mch     商户
     * @return 回调通知明文
     */
    public String verifyNotify(HttpServletRequest request, String body, InnerWeChatCertificateDTO mch) {
        if (StringUtils.isBlank(body)) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, "参数不合法");
        }

        String timestamp = request.getHeader("Wechatpay-Timestamp");
        String nonce = request.getHeader("Wechatpay-Nonce");
        String serialNo = request.getHeader("Wechatpay-Serial");
        String signature = request.getHeader("Wechatpay-Signature");
        log.info("异步通知密文: {}, timestamp: {}, nonce: {}, serialNo: {}, signature: {}",
                body, timestamp, nonce, serialNo, signature);

        try {
            // 需要通过证书序列号查找对应的证书，verifyNotify 中有验证证书的序列号
            InputStream certInputStream = this.stringToInputStream(mch.getPlatFormCert().getEncryptCertificate());
            String plainText = WxPayKit.verifyNotify(serialNo, body,
                    signature, nonce, timestamp, mch.getSignKeyV3(), certInputStream);
            log.info("异步通知明文 {}", plainText);
            return plainText;
        } catch (Exception e) {
            String msg = "微信支付异步通知验证签名异常: " + e.getMessage();
            throw SysException.of(SysErrorCode.S_RPC_ERROR, e.getMessage(), msg, e);
        }
    }

    /**
     * V3 接口 GET 请求
     *
     * @param urlSuffix 通过 {@link WxApiType} 来获取，URL挂载参数需要自行拼接
     * @param mch       商户
     * @return {@link IJPayHttpResponse} 请求返回的结果
     */
    private IJPayHttpResponse getWxPayApiV3(String urlSuffix, InnerWeChatCertificateDTO mch) {
        return wxPayApiV3(RequestMethod.GET, urlSuffix, mch, null);
    }

    /**
     * V3 接口 POST 请求
     *
     * @param urlSuffix 通过 {@link WxApiType} 来获取，URL挂载参数需要自行拼接
     * @param mch       商户
     * @return {@link IJPayHttpResponse} 请求返回的结果
     */
    private IJPayHttpResponse postWxPayApiV3(String urlSuffix, InnerWeChatCertificateDTO mch, Object body) {
        return wxPayApiV3(RequestMethod.POST, urlSuffix, mch, body);
    }

    /**
     * V3 接口统一执行入口
     *
     * @param method    请求方法
     * @param urlSuffix 通过 {@link WxApiType} 来获取，URL挂载参数需要自行拼接
     * @param mch       商户
     * @param body      接口请求参数
     * @return {@link IJPayHttpResponse} 请求返回的结果
     */
    private IJPayHttpResponse wxPayApiV3(RequestMethod method, String urlSuffix,
                                         InnerWeChatCertificateDTO mch, Object body) {
        String responseStr = "";
        try {
            // 文件内容转换成私钥
            PrivateKey privateKey = PayKit.getPrivateKeyByKeyContent(mch.getKeyPem().getEncryptCertificate());

            // 发送请求
            String bodyJson = JSONUtil.toJsonStr(body);
            IJPayHttpResponse response = WxPayApi.v3(
                    method,
                    WxDomain.CHINA.toString(),
                    urlSuffix,
                    mch.getMchId(),
                    mch.getMerchantCert().getSerialNo(),
                    mch.getPlatFormCert().getSerialNo(),
                    privateKey,
                    bodyJson != null ? bodyJson : ""
            );

            // 获取响应体类型，判断是否 json 返回值
            String contentType = response.getHeader("Content-Type");
            boolean isJson = contentType.contains("application/json");
            responseStr = isJson ? response.toString() : contentType;
            log.info("微信支付-v3接口响应: {}, url: {}, body: {}", responseStr, urlSuffix, bodyJson);

            // 验证结果，正常状态 2XX 示例 200 204
            if (HttpStatus.Series.valueOf(response.getStatus()) != HttpStatus.Series.SUCCESSFUL) {
                throw new RemoteException("RPC返回异常状态码");
            }

            // 不是 json 返回值不需要验证签名，例如：下载账单接口
            if (BooleanUtils.isFalse(isJson)) {
                return response;
            }

            // 根据证书序列号查询对应的证书来验证签名结果
            boolean verifySignature = this.verifySignature(response, mch.getPlatFormCert().getEncryptCertificate());
            if (BooleanUtils.isFalse(verifySignature)) {
                throw new RemoteException("RPC返回签名校验异常");
            }
            return response;
        } catch (Exception e) {
            String msg = String.format("微信支付调用异常 response: %s", responseStr);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
        }
    }

    /**
     * JS 调起支付签名
     *
     * @param appId             应用编号
     * @param responseBody      支付接口返回值
     * @param privateKeyContent 私钥内容
     * @return 唤起支付需要的参数
     * @throws Exception 错误信息
     */
    private Map<String, Object> jsApiCreateSign(String appId, String responseBody, String privateKeyContent) throws Exception {
        // 文件内容转换成私钥
        PrivateKey privateKey = PayKit.getPrivateKeyByKeyContent(privateKeyContent);

        // 获取预支付id
        String prepayId = GsonUtil.getAsString(responseBody, "prepay_id");

        String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
        String nonceStr = String.valueOf(System.currentTimeMillis());
        String packageStr = "prepay_id=" + prepayId;
        Map<String, Object> packageParams = new HashMap<>(7);
        packageParams.put("prepayId", prepayId);
        packageParams.put("appId", appId);
        packageParams.put("timeStamp", timeStamp);
        packageParams.put("nonceStr", nonceStr);
        packageParams.put("package", packageStr);
        packageParams.put("signType", SignType.RSA.toString());
        ArrayList<String> list = new ArrayList<>();
        list.add(appId);
        list.add(timeStamp);
        list.add(nonceStr);
        list.add(packageStr);
        String packageSign = PayKit.createSign(PayKit.buildSignMessage(list), privateKey);
        packageParams.put("paySign", packageSign);

        log.info("唤起支付参数:{}", packageParams);
        return packageParams;
    }

    /**
     * 验证签名
     *
     * @param response  接口响应
     * @param publicKey 微信支付平台公钥
     * @return 签名结果
     * @throws Exception 异常信息
     */
    private boolean verifySignature(IJPayHttpResponse response, String publicKey) throws Exception {
        String timestamp = response.getHeader("Wechatpay-Timestamp");
        String nonceStr = response.getHeader("Wechatpay-Nonce");
        String signature = response.getHeader("Wechatpay-Signature");
        String body = response.getBody();
        InputStream certInputStream = this.stringToInputStream(publicKey);
        boolean verifySignature = WxPayKit.verifySignature(signature, body, nonceStr, timestamp, certInputStream);
        log.info("微信支付-验证签名: {}", verifySignature);
        return verifySignature;
    }

    /**
     * 字符串 转换为 InputStream
     *
     * @param input 字符串
     * @return InputStream
     */
    private InputStream stringToInputStream(String input) {
        byte[] bytes = input.getBytes(StandardCharsets.UTF_8);
        return new ByteArrayInputStream(bytes);
    }

}
