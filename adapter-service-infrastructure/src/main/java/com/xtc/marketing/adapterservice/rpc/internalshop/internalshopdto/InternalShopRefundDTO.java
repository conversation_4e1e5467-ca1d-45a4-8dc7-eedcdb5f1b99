package com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 内部购机退款单
 */
@Getter
@Setter
@ToString
public class InternalShopRefundDTO {

    /**
     * 退款单
     */
    @SerializedName("refund")
    private RefundDTO refund;

    @Getter
    @Setter
    @ToString
    public static class RefundDTO {

        /**
         * 退(货)款单id
         */
        @SerializedName("id")
        private Integer id;

        /**
         * 退货(款)单编号
         */
        @SerializedName("sn")
        private String sn;

        /**
         * 会员id
         */
        @SerializedName("member_id")
        private Integer memberId;

        /**
         * 会员名称
         */
        @SerializedName("member_name")
        private String memberName;

        /**
         * 卖家id
         */
        @SerializedName("seller_id")
        private Integer sellerId;

        /**
         * 卖家姓名
         */
        @SerializedName("seller_name")
        private String sellerName;

        /**
         * 订单编号
         */
        @SerializedName("order_sn")
        private String orderSn;

        /**
         * 退(货)款状态
         */
        @SerializedName("refund_status")
        private String refundStatus;

        /**
         * 创建时间
         */
        @SerializedName("create_time")
        private Long createTime;

        /**
         * 退款金额
         */
        @SerializedName("refund_price")
        private Double refundPrice;

        /**
         * 退还积分
         */
        @SerializedName("refund_point")
        private Integer refundPoint;

        /**
         * 退款方式(原路退回，在线支付，线下支付)
         */
        @SerializedName("refund_way")
        private String refundWay;

        /**
         * 退款账户类型
         */
        @SerializedName("account_type")
        private String accountType;

        /**
         * 退款账户
         */
        @SerializedName("return_account")
        private String returnAccount;

        /**
         * 客户备注
         */
        @SerializedName("customer_remark")
        private String customerRemark;

        /**
         * 客服备注
         */
        @SerializedName("seller_remark")
        private String sellerRemark;

        /**
         * 库管备注
         */
        @SerializedName("warehouse_remark")
        private String warehouseRemark;

        /**
         * 财务备注
         */
        @SerializedName("finance_remark")
        private String financeRemark;

        /**
         * 退款原因
         */
        @SerializedName("refund_reason")
        private String refundReason;

        /**
         * 退款原因
         */
        @SerializedName("refund_pics")
        private String refundPics;

        /**
         * 拒绝原因
         */
        @SerializedName("refuse_reason")
        private String refuseReason;

        /**
         * 银行名称
         */
        @SerializedName("bank_name")
        private String bankName;

        /**
         * 银行账号
         */
        @SerializedName("bank_account_number")
        private String bankAccountNumber;

        /**
         * 银行开户名
         */
        @SerializedName("bank_account_name")
        private String bankAccountName;

        /**
         * 银行开户行
         */
        @SerializedName("bank_deposit_name")
        private String bankDepositName;

        /**
         * 交易编号
         */
        @SerializedName("trade_sn")
        private String tradeSn;

        /**
         * 售后类型(取消订单,申请售后)
         */
        @SerializedName("refund_type")
        private String refundType;

        /**
         * 订单类型(在线支付,货到付款)
         */
        @SerializedName("payment_type")
        private String paymentType;

        /**
         * 退(货)款类型(退货，退款)
         */
        @SerializedName("refuse_type")
        private String refuseType;

        /**
         * 支付结果交易号
         */
        @SerializedName("pay_order_no")
        private String payOrderNo;

        @SerializedName("refund_fail_reason")
        private String refundFailReason;

        @SerializedName("refund_time")
        private Long refundTime;

        /**
         * 退款物流公司id
         */
        @SerializedName("refund_ship_id")
        private String refundShipId;

        /**
         * 退款物流单号
         */
        @SerializedName("refund_ship_sn")
        private String refundShipSn;

        @SerializedName("phone")
        private String phone;

        /**
         * 赠品信息
         */
        @SerializedName("refund_gift")
        private String refundGift;

        /**
         * 最后更新时间
         */
        @SerializedName("last_update_time")
        private Long lastUpdateTime;

        @SerializedName("refund_status_text")
        private String refundStatusText;

        @SerializedName("account_type_text")
        private String accountTypeText;

        @SerializedName("refuse_type_text")
        private String refuseTypeText;

    }

}
