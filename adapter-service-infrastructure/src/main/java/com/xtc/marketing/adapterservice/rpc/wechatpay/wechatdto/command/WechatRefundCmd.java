package com.xtc.marketing.adapterservice.rpc.wechatpay.wechatdto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatRefundCmd {

    /**
     * 微信支付订单号
     */
    private String transactionId;

    /**
     * 退款金额（单位：分）
     */
    private Integer refundAmount;

    /**
     * 原订单金额（单位：分）
     */
    private Integer totalAmount;

    /**
     * 退款结果回调url
     */
    private String notifyUrl;

}
