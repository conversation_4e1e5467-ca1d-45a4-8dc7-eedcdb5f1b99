package com.xtc.marketing.adapterservice.rpc.sf.sfdto.command;

import lombok.*;

/**
 * 顺丰物流拦截参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SfInterceptCmd {

    /**
     * 运单号，必填。
     */
    private String waybillNo;
    /**
     * 操作类型，必填。
     * <pre>
     * 1转奇
     * 2退回
     * 3优派
     * 4再派
     * 6改派送（上门派送）
     * 7更改派送时间
     * 8修改收件人信息
     * 9更改付款方式
     * 10修改代收货款
     * 12作废
     * 38部分退回（哪个单要部分退，就传哪个单，不能一次传多个）
     * </pre>
     */
    private final String serviceCode = "2";
    /**
     * 发起方：1、寄方，2、收方，必填。
     */
    private final String role = "1";
    /**
     * 付款方式：1寄付现结、2到付现结、3寄付转第三方月结、4寄付月结，必填。
     */
    private final String payMode = "4";
    /**
     * 月结卡号，符合以下任一条件时必填：1、原单付款方式是月结，2、付款方式为月结，3、修改代收货款。
     */
    private String monthlyCardNo;
    /**
     * 新目的地改联系人业务请携带姓名及手机号转奇、退回新地址必须携带，撤销转奇、退回不携带改自取必须携带。
     */
    private NewDestAddress newDestAddress;

    /**
     * 新目的地地址类，用于封装新目的地的详细信息。
     */
    @Getter
    @Setter
    @ToString
    public static class NewDestAddress {

        /**
         * 省，如广东省，必填。
         */
        private String province;
        /**
         * 市，如深圳市，必填。
         */
        private String city;
        /**
         * 区，如南山区，必填。
         */
        private String county;
        /**
         * 详细地址，不包含省市区，如粤海街道高新区1C栋二楼，必填。
         */
        private String address;
        /**
         * 联系人姓名，如黄飞鸡，必填。
         */
        private String contact;
        /**
         * 电话，如18012345678，必填。
         */
        private String phone;

    }

}
