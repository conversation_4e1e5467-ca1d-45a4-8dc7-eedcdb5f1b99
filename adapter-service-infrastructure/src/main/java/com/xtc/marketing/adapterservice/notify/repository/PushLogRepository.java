package com.xtc.marketing.adapterservice.notify.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xtc.marketing.adapterservice.config.BaseRepository;
import com.xtc.marketing.adapterservice.notify.dataobject.PushConfigDO;
import com.xtc.marketing.adapterservice.notify.dataobject.PushLogDO;
import com.xtc.marketing.adapterservice.notify.dto.query.PushLogPageQry;
import com.xtc.marketing.adapterservice.notify.repository.mapper.PushLogMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public class PushLogRepository extends BaseRepository<PushLogMapper, PushLogDO> {

    /**
     * 查询最后一次推送记录
     *
     * @param pushConfig   推送配置
     * @param receiveLogId 接收记录id
     * @return 推送记录
     */
    public Optional<PushLogDO> getLastByPushConfigAndReceiveLogId(PushConfigDO pushConfig, Long receiveLogId) {
        if (pushConfig == null || receiveLogId == null) {
            return Optional.empty();
        }
        Wrapper<PushLogDO> wrapper = Wrappers.<PushLogDO>lambdaQuery()
                .eq(PushLogDO::getBizName, pushConfig.getBizName())
                .eq(PushLogDO::getPlatformCode, pushConfig.getPlatformCode())
                .eq(PushLogDO::getModuleCode, pushConfig.getModuleCode())
                .eq(PushLogDO::getScenarioCode, pushConfig.getScenarioCode())
                .eq(PushLogDO::getReceiveLogId, receiveLogId)
                .orderByDesc(PushLogDO::getId)
                .last(LIMIT_ONE);
        PushLogDO one = this.getOne(wrapper);
        return Optional.ofNullable(one);
    }

    /**
     * 统计推送记录的数量
     *
     * @param pushConfig   推送配置
     * @param receiveLogId 接收记录id
     * @return 推送记录的数量
     */
    public int countByPushConfigAndReceiveLogId(PushConfigDO pushConfig, Long receiveLogId) {
        if (pushConfig == null || receiveLogId == null) {
            return 0;
        }
        Wrapper<PushLogDO> wrapper = Wrappers.<PushLogDO>lambdaQuery()
                .eq(PushLogDO::getBizName, pushConfig.getBizName())
                .eq(PushLogDO::getPlatformCode, pushConfig.getPlatformCode())
                .eq(PushLogDO::getModuleCode, pushConfig.getModuleCode())
                .eq(PushLogDO::getScenarioCode, pushConfig.getScenarioCode())
                .eq(PushLogDO::getReceiveLogId, receiveLogId);
        return (int) this.count(wrapper);
    }

    /**
     * 查询推送记录分页列表
     *
     * @param qry 参数
     * @return 推送记录分页列表
     */
    public IPage<PushLogDO> pageBy(PushLogPageQry qry) {
        Wrapper<PushLogDO> wrapper = Wrappers.<PushLogDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(qry.getDataId()), PushLogDO::getDataId, qry.getDataId())
                .eq(qry.getReceiveLogId() != null, PushLogDO::getReceiveLogId, qry.getReceiveLogId())
                .orderByDesc(PushLogDO::getId);
        if (wrapper.isEmptyOfWhere()) {
            return qry.buildPage();
        }
        return this.page(qry.buildPage(), wrapper);
    }

}
