package com.xtc.marketing.adapterservice.rpc.sf.sfxmldto;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamOmitField;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 顺丰仓库 xml 响应
 */
@Setter
@Getter
@ToString
@XStreamAlias("Body")
public class SfResponseXmlDTO {

    /**
     * 请求成功代码
     */
    @XStreamOmitField
    public static final String SUCCESS_CODE = "1";

    /**
     * 处理结果
     */
    @XStreamAlias("Result")
    private String result;

    /**
     * 备注，处理失败，此处为失败原因
     */
    @XStreamAlias("Note")
    private String note;

    /**
     * 判断接口响应的业务结果失败
     *
     * @return 执行结果
     */
    public boolean isFailure() {
        // 有些返回值没有 result 值，可以认为是成功的
        return result != null && !SUCCESS_CODE.equals(result);
    }

}
