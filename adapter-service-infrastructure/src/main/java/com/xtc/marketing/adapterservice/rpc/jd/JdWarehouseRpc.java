package com.xtc.marketing.adapterservice.rpc.jd;

import com.lop.open.api.sdk.DefaultDomainApiClient;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.JdlOpenPlatformSoService.querySoOrder.JdlApiResponseBase;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.JdlOpenPlatformSoService.querySoOrder.SoQueryResponse;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.JdlOpenPlatformStockService.queryWarehouseStockMergeByWarehouse.StockSummaryResult;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.JdlOpenPlatformStockService.searchShopStockFlow.JdlApiPageResponseBase;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.JdlOpenPlatformStockService.searchShopStockFlow.JdlOpenPage;
import com.lop.open.api.sdk.plugin.LopPlugin;
import com.lop.open.api.sdk.plugin.factory.OAuth2PluginFactory;
import com.lop.open.api.sdk.request.DomainAbstractRequest;
import com.lop.open.api.sdk.request.IntegratedSupplyChain.IntegratedsupplychainOrderDeliveryQueryV1LopRequest;
import com.lop.open.api.sdk.request.IntegratedSupplyChain.IntegratedsupplychainStockFlowShopstockQueryV1LopRequest;
import com.lop.open.api.sdk.request.IntegratedSupplyChain.IntegratedsupplychainStockmergeQueryV1LopRequest;
import com.lop.open.api.sdk.response.AbstractResponse;
import com.lop.open.api.sdk.response.IntegratedSupplyChain.IntegratedsupplychainOrderDeliveryQueryV1LopResponse;
import com.lop.open.api.sdk.response.IntegratedSupplyChain.IntegratedsupplychainStockFlowShopstockQueryV1LopResponse;
import com.lop.open.api.sdk.response.IntegratedSupplyChain.IntegratedsupplychainStockmergeQueryV1LopResponse;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.rmi.RemoteException;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 京东仓库RPC
 * <p><a href="https://open.jdl.com/#/open-business-document/access-guide/367/55049">文档地址</a></p>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class JdWarehouseRpc {

    /**
     * 缓存 client 对象，京东平台
     */
    private static final Map<String, DefaultDomainApiClient> CLIENT_CACHE_JD = new ConcurrentHashMap<>();
    /**
     * 服务地址
     */
    private static final String SERVER_URL = "https://api.jdl.com";

    /**
     * 分页查询出库单列表
     *
     * @param shopDO  店铺
     * @param request 参数
     * @return 出库单分页列表
     */
    public JdlOpenPage pageOutbound(ShopDO shopDO, IntegratedsupplychainStockFlowShopstockQueryV1LopRequest request) {
        IntegratedsupplychainStockFlowShopstockQueryV1LopResponse response = this.call(shopDO, request);
        return Optional.ofNullable(response.getResponse())
                .map(JdlApiPageResponseBase::getData)
                .orElse(null);
    }

    /**
     * 查询出库单详情
     *
     * @param shopDO  店铺
     * @param request 参数
     * @return 出库单详情
     */
    public SoQueryResponse getOutboundDetail(ShopDO shopDO, IntegratedsupplychainOrderDeliveryQueryV1LopRequest request) {
        IntegratedsupplychainOrderDeliveryQueryV1LopResponse response = this.call(shopDO, request);
        return Optional.ofNullable(response.getResponse())
                .map(JdlApiResponseBase::getData)
                .orElseThrow(() -> this.rpcSysException(response));
    }

    /**
     * 查询库存
     *
     * @param shopDO  店铺
     * @param request 参数
     * @return 库存
     */
    public StockSummaryResult queryStocks(ShopDO shopDO, IntegratedsupplychainStockmergeQueryV1LopRequest request) {
        IntegratedsupplychainStockmergeQueryV1LopResponse response = this.call(shopDO, request);
        return response.getResponse().getData();
    }

    /**
     * 执行远程调用
     *
     * @param shop    账号
     * @param request 参数
     * @param <T>     返回值类型
     * @return 执行结果
     */
    private <T extends AbstractResponse> T call(ShopDO shop, DomainAbstractRequest<T> request) {
        String responseStr = "";
        try {
            request.setUseJosAuth(true);
            LopPlugin lopPlugin = OAuth2PluginFactory.produceLopPlugin(shop.getAppKey(),
                    shop.getAppSecret(), shop.getAppAccessToken());
            request.addLopPlugin(lopPlugin);
            log.info("京东平台参数: {}", request.getAppJsonParams());
            DefaultDomainApiClient client = this.getJdClient(shop);
            T response = client.execute(request);

            responseStr = GsonUtil.objectToJson(response);
            log.info("京东平台返回值: {}", responseStr);

            if (!"0".equals(response.getCode())) {
                throw new RemoteException("RPC返回异常状态码");
            }
            return response;
        } catch (Exception e) {
            String msg = String.format("京东平台调用异常 response: %s", e.getMessage());
            throw SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
        }
    }

    /**
     * 获取请求客户端，京东物流平台，使用京东平台账号插件
     *
     * @param shop 店铺
     * @return 客户端
     */
    private DefaultDomainApiClient getJdClient(ShopDO shop) {
        String key = shop.getShopCode();
        if (CLIENT_CACHE_JD.get(key) == null) {
            DefaultDomainApiClient client = new DefaultDomainApiClient(SERVER_URL, 500, 3000);
            CLIENT_CACHE_JD.put(key, client);
        }
        return CLIENT_CACHE_JD.get(key);
    }

    /**
     * 远程调用异常
     *
     * @param response 响应
     * @return 异常
     */
    private SysException rpcSysException(Object response) {
        String responseStr = GsonUtil.objectToJson(response);
        String msg = String.format("京东平台调用异常 response: %s", responseStr);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg);
    }

    public static void main(String[] args) {
        JdWarehouseRpc rpc = new JdWarehouseRpc();
        ShopDO shop = ShopDO.builder()
                .shopCode("JD_XTC")
                .apiUrl("https://api.jd.com/routerjson")
                .appKey("xxxxx")
                .appSecret("xxxxx")
                .appAccessToken("xxxxx")
                .build();
    }

}
