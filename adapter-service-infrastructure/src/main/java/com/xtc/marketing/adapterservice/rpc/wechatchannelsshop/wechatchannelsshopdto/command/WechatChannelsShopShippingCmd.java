package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.command;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 微信视频号小店发货参数
 */
@Getter
@Setter
@ToString
public class WechatChannelsShopShippingCmd {

    /**
     * 订单id
     */
    @SerializedName("order_id")
    private String orderId;
    /**
     * 物流信息
     */
    @SerializedName("delivery_list")
    public List<DeliveryInfoCmd> deliverList;

    /**
     * 物流信息
     */
    @Getter
    @Setter
    @ToString
    public static class DeliveryInfoCmd {

        /**
         * 快递公司id，通过获取快递公司列表接口获得，非主流快递公司可以填OTHER
         */
        @SerializedName("delivery_id")
        private String deliveryId;
        /**
         * 快递单号
         */
        @SerializedName("waybill_id")
        private String waybillId;
        /**
         * 发货方式，1:自寄快递发货，目前仅支持1
         */
        @SerializedName("deliver_type")
        private Integer deliverType = 1;
        /**
         * 包裹中的商品信息，具体可见结构体DeliveryProductInfo
         */
        @SerializedName("product_infos")
        private List<DeliveryProductInfoCmd> products;

    }

    /**
     * 包裹中的商品信息
     */
    @Getter
    @Setter
    @ToString
    public static class DeliveryProductInfoCmd {

        /**
         * 商品id
         */
        @SerializedName("product_id")
        private String productId;
        /**
         * 商品sku
         */
        @SerializedName("sku_id")
        private String skuId;
        /**
         * 商品数量
         */
        @SerializedName("product_cnt")
        private Integer productCnt;

    }

}
