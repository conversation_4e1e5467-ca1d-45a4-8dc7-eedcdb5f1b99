package com.xtc.marketing.adapterservice.rpc.yto;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.rpc.yto.ytodto.YtoBaseDTO;
import com.xtc.marketing.adapterservice.rpc.yto.ytodto.YtoOrderDTO;
import com.xtc.marketing.adapterservice.rpc.yto.ytodto.YtoRouteDTO;
import com.xtc.marketing.adapterservice.rpc.yto.ytodto.command.YtoCreateOrderCmd;
import com.xtc.marketing.adapterservice.rpc.yto.ytodto.command.YtoInterceptCmd;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;

import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * 圆通物流RPC
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class YtoRpc {

    /**
     * 生产环境地址，替换字符串%s为对应的接口代码和账号
     */
    private static final String DOMAIN = "https://openapi.yto.net.cn:11443/open/%s/v1/1XSpYw/%s";

    /**
     * API：查询快递路由
     */
    private static final String API_SEARCH_ROUTES = "track_query_adapter";
    /**
     * API：下单，生成运单号
     */
    private static final String API_CREATE_ORDER = "privacy_create_adapter";

    /**
     * API：拦截
     */
    private static final String API_INTERCEPT = "wanted_change_adapter";

    /**
     * 下单，生成运单号
     *
     * @param account 账号
     * @param cmd     请求参数
     * @return 运单号
     */
    public YtoOrderDTO createOrder(LogisticsAccountDO account, YtoCreateOrderCmd cmd) {
        return this.call(account, API_CREATE_ORDER, cmd, YtoOrderDTO.class);
    }

    /**
     * 查询快递路由
     *
     * @param account   账号
     * @param waybillNo 运单号
     * @return 路由
     */
    public List<YtoRouteDTO> searchRoutes(LogisticsAccountDO account, String waybillNo) {
        JsonObject logisticsJson = new JsonObject();
        logisticsJson.addProperty("NUMBER", waybillNo);
        YtoBaseDTO baseDTO = this.call(account, API_SEARCH_ROUTES, logisticsJson, YtoBaseDTO.class);

        // 查询为空返回编码1001
        if ("1001".equals(baseDTO.getCode())) {
            return Collections.emptyList();
        }
        return GsonUtil.jsonToList(baseDTO.getOriginResponse(), YtoRouteDTO.class);
    }

    /**
     * 拦截
     *
     * @param account 账号
     * @param cmd     参数
     */
    public void intercept(LogisticsAccountDO account, YtoInterceptCmd cmd) {
        this.call(account, API_INTERCEPT, cmd, YtoBaseDTO.class);
    }

    /**
     * 调用圆通接口
     *
     * @param account 账号
     * @param apiCode 接口代码
     * @param request 请求参数
     * @return 执行结果
     */
    private <T extends YtoBaseDTO> T call(LogisticsAccountDO account, String apiCode,
                                          Object request, Class<T> responseClass) {
        // 构建参数
        HashMap<String, String> params = this.buildBody(account, apiCode, GsonUtil.objectToJson(request));

        String response = "";
        try {
            // 请求接口
            String url = String.format(DOMAIN, apiCode, account.getBizAccount());
            response = HttpUtil.post(url, params);
            if (StringUtils.isBlank(response)) {
                throw new RemoteException("RPC返回异常业务状态");
            }
        } catch (Exception e) {
            if (e instanceof HttpClientErrorException.BadRequest) {
                response = ((HttpClientErrorException.BadRequest) e).getResponseBodyAsString();
                return this.parseResponse(response, responseClass);
            }
            if (e instanceof HttpServerErrorException) {
                response = ((HttpServerErrorException) e).getResponseBodyAsString();
            }
            String msg = String.format("圆通物流调用异常 response: %s", response);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, response, msg, e);
        }

        // 解析响应结果
        return this.parseResponse(response, responseClass);
    }

    /**
     * 解析响应结果
     *
     * @param response      响应结果
     * @param responseClass 响应结果类型
     * @param <T>           响应结果类型
     * @return 响应结果
     */
    private <T extends YtoBaseDTO> T parseResponse(String response, Class<T> responseClass) {
        try {
            T baseDTO = responseClass.newInstance();
            baseDTO.setOriginResponse(response);
            JsonElement jsonElement = GsonUtil.jsonToBean(response, JsonElement.class);
            if (jsonElement.isJsonArray()) {
                return baseDTO;
            }
            if (jsonElement.isJsonObject()) {
                T resultDTO = GsonUtil.jsonToBean(response, responseClass);
                if (resultDTO.isFailure()) {
                    throw new RemoteException("RPC返回异常状态码");
                }
                return resultDTO;
            }
            return baseDTO;
        } catch (Exception e) {
            String msg = String.format("圆通物流响应结果解析异常 response: %s", response);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, response, msg, e);
        }
    }

    /**
     * 构建参数
     *
     * @param account 账号
     * @param apiCode 接口代码
     * @param param   请求参数
     * @return 参数
     */
    private HashMap<String, String> buildBody(LogisticsAccountDO account, String apiCode, String param) {
        try {
            // 签名
            String sign = buildSign(param, apiCode, account.getClientSecret());

            HashMap<String, String> params = new HashMap<>();
            params.put("param", param);
            params.put("timestamp", Long.toString(System.currentTimeMillis()));
            params.put("format", "JSON");
            params.put("sign", sign);
            return params;
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, "圆通物流调用异常，组装参数异常", e);
        }
    }

    /**
     * 签名
     *
     * @param param        参数
     * @param apiCode      接口代码
     * @param clientSecret 密钥
     * @return 签名
     */
    private String buildSign(String param, String apiCode, String clientSecret) throws NoSuchAlgorithmException {
        String plantText = param + apiCode + "v1" + clientSecret;
        byte[] md5 = this.md5Encode(plantText);
        return HttpUtil.base64Encode(md5);
    }

    /**
     * md5加密
     *
     * @param encryptStr 加密字符串
     * @return 密文字节
     */
    private byte[] md5Encode(String encryptStr) throws NoSuchAlgorithmException {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        md5.update(encryptStr.getBytes(StandardCharsets.UTF_8));
        return md5.digest();
    }

}
