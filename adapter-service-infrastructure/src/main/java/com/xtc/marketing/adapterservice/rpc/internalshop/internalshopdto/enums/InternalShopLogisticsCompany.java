package com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.enums;

import lombok.Getter;

/**
 * 内部购机平台快递公司
 */
@Getter
public enum InternalShopLogisticsCompany {

    /**
     * 圆通
     */
    YTO("圆通", "1"),
    /**
     * 京东
     */
    JD("京东", "2"),
    /**
     * 顺丰
     */
    SF("顺丰", "5"),
    /**
     * EMS
     */
    EMS("EMS", "6"),
    ;

    /**
     * 快递公司名称
     */
    private final String name;

    /**
     * 快递公司代码
     */
    private final String code;

    InternalShopLogisticsCompany(String name, String code) {
        this.name = name;
        this.code = code;
    }

}
