package com.xtc.marketing.adapterservice.rpc.sf.sfdto.command;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 顺丰入库单货物
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SfInboundItemCmd {

    /**
     * skuId
     */
    @SerializedName("SkuNo")
    private String skuId;

    /**
     * 数量
     */
    @SerializedName("Qty")
    private Integer quantity;

}
