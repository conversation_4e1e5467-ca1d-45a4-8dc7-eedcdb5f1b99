package com.xtc.marketing.adapterservice.rpc.jitu;

import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.JtExpressBaseRequest;
import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.JtExpressBaseResponse;
import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.request.*;
import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.response.*;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.util.*;

/**
 * 极兔快递RPC
 */
@Slf4j
@Component
public class JtExpressRpc {

    /**
     * 查询路由
     *
     * @param account   账号
     * @param waybillNo 运单号
     * @return 路由响应
     */
    public JtExpressRouteResponse routes(LogisticsAccountDO account, String waybillNo) {
        JtExpressRouteRequest request = new JtExpressRouteRequest();
        request.setBillCodes(waybillNo);
        List<JtExpressRouteResponse> response = call(account, request);
        return response.get(0);
    }

    /**
     * 创建订单
     *
     * @param account 账号
     * @param request 参数
     * @return 订单响应
     */
    public JtExpressCreateOrderResponse createOrderWithWaybill(LogisticsAccountDO account, JtExpressCreateOrderRequest request) {
        return call(account, request);
    }

    /**
     * 查询订单
     *
     * @param account 账号
     * @param orderId 订单号
     * @return 订单响应
     */
    public JtExpressQueryOrderResponse queryOrder(LogisticsAccountDO account, String orderId) {
        JtExpressQueryOrderRequest request = new JtExpressQueryOrderRequest();
        request.setCustomerCode(account.getBizAccount());
        request.setCommand(1);
        request.setSerialNumber(Collections.singletonList(orderId));
        List<JtExpressQueryOrderResponse> response = call(account, request);
        return response.get(0);
    }

    /**
     * 取消订单
     *
     * @param account      账号
     * @param orderId      订单号
     * @param cancelReason 取消原因
     * @return 取消订单响应
     */
    public JtExpressCancelOrderResponse cancelOrder(LogisticsAccountDO account, String orderId, String cancelReason) {
        JtExpressCancelOrderRequest request = new JtExpressCancelOrderRequest();
        request.setCustomerCode(account.getBizAccount());
        request.setOrderType("2");
        request.setTxlogisticId(orderId);
        request.setReason(cancelReason);
        return call(account, request);
    }

    /**
     * 拦截订单
     *
     * @param account 账号
     * @param request 参数
     * @return 拦截订单响应
     */
    public JtInterceptOrderResponse interceptOrder(LogisticsAccountDO account, JtExpressInterceptOrderRequest request) {
        return call(account, request);
    }

    /**
     * 打印面单
     *
     * @param account   账号
     * @param waybillNo 运单号
     * @return 面单 base64 内容
     */
    public String printWaybill(LogisticsAccountDO account, String waybillNo) {
        JtExpressPrintRequest request = new JtExpressPrintRequest();
        request.setBillCode(waybillNo);
        request.setCustomerCode(account.getBizAccount());
        request.setIsPrivacyFlag(true);
        JtExpressPrintResponse response = call(account, request);
        return response.getBase64EncodeContent();
    }

    /**
     * 超区查询
     *
     * @param account 账号
     * @param request 参数
     * @return 超区查询响应
     */
    public JtExpressRangeCheckResponse rangeCheck(LogisticsAccountDO account, JtExpressRangeCheckRequest request) {
        return call(account, request);
    }

    /**
     * 执行远程调用
     *
     * @param account 账号
     * @param request 请求参数
     * @param <T>     响应类型
     * @param <R>     响应结果类型（单个或者集合）
     * @return 具体响应数据
     */
    public <T, R> R call(LogisticsAccountDO account, JtExpressBaseRequest<T> request) {
        String responseStr = "";
        String url = account.getApiUrl() + request.getApiPath();
        try {
            // 生成业务参数签名
            String bizSignature = generateBizSignature(account.getBizAccount(), account.getAppSessionKey(), account.getClientSecret());
            request.setDigest(bizSignature);
            // 序列化业务参数
            String bizContentJson = GsonUtil.objectToJson(request);
            // 生成请求头签名
            String headerSignature = generateHeaderSignature(bizContentJson, account.getClientSecret());
            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("apiAccount", account.getClientCode());
            headers.put("digest", headerSignature);
            headers.put("timestamp", String.valueOf(System.currentTimeMillis()));
            // 构建请求体
            MultiValueMap<String, String> form = new LinkedMultiValueMap<>();
            form.add("bizContent", bizContentJson);
            // 发送请求
            log.info("极兔快递参数: {}", bizContentJson);
            responseStr = HttpUtil.call(url, HttpMethod.POST, headers, MediaType.APPLICATION_FORM_URLENCODED, form, String.class);
            log.info("极兔快递响应: {}", responseStr);
            // 解析响应并返回具体数据
            return parseResponse(responseStr, request.getResponseClass());
        } catch (Exception e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                responseStr = "请求超时";
            }
            throw rpcSysException(responseStr, url, e);
        }
    }

    /**
     * 解析极兔响应结果
     *
     * @param response      响应字符串
     * @param responseClass 响应类型
     * @return 响应结果
     */
    private <T, R> R parseResponse(String response, Class<T> responseClass) throws RemoteException {
        if (StringUtils.isBlank(response)) {
            throw rpcSysException("响应内容为空");
        }
        JtExpressBaseResponse baseResponse = GsonUtil.jsonToBean(response, JtExpressBaseResponse.class);
        // 检查业务状态码
        if (!"1".equals(baseResponse.getCode())) {
            throw remoteException();
        }
        // 解析数据内容
        if (baseResponse.getData() == null || baseResponse.getData().isJsonNull()) {
            return null;
        }
        String dataJson = baseResponse.getData().toString();
        if (baseResponse.getData().isJsonArray()) {
            return GsonUtil.jsonToBean(dataJson, List.class, responseClass);
        }
        return GsonUtil.jsonToBean(dataJson, responseClass);
    }

    /**
     * 生成业务参数签名
     * 算法：Base64(MD5(CustomerCode + MD5(password + "jadada236t2").toUpperCase() + privateKey))
     *
     * @param customerCode   客户代码
     * @param customerSecret 客户密码
     * @param apiPrivateKey  API密钥
     * @return 业务参数签名
     */
    private String generateBizSignature(String customerCode, String customerSecret, String apiPrivateKey) {
        // 客户密码加密 = MD5(password + "jadada236t2").toUpperCase()
        String encryptedPwd = DigestUtils.md5DigestAsHex((customerSecret + "jadada236t2").getBytes(StandardCharsets.UTF_8)).toUpperCase();
        // 签名 = Base64(MD5(customerCode + 加密后的密码 + privateKey))
        String signText = customerCode + encryptedPwd + apiPrivateKey;
        log.debug("生成签名内容: {}", signText);
        byte[] md5Digest = DigestUtils.md5Digest(signText.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(md5Digest);
    }

    /**
     * 生成请求头签名
     * 算法：base64(md5(业务参数Json + privateKey))
     *
     * @param bizContentJson 业务参数JSON字符串
     * @param apiPrivateKey  API密钥
     * @return 请求头签名
     */
    private String generateHeaderSignature(String bizContentJson, String apiPrivateKey) {
        String signText = bizContentJson + apiPrivateKey;
        log.debug("生成请求头签名内容: {}", signText);
        byte[] md5Digest = DigestUtils.md5Digest(signText.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(md5Digest);
    }

    /**
     * 远程接口异常
     *
     * @return 异常
     */
    private RemoteException remoteException() {
        return new RemoteException("RPC返回异常状态码");
    }

    /**
     * 远程调用异常
     *
     * @param response 响应
     * @return 异常
     */
    private SysException rpcSysException(Object response) {
        String responseStr = response instanceof String ? response.toString() : GsonUtil.objectToJson(response);
        String msg = String.format("极兔快递调用异常 response: %s", responseStr);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg);
    }

    /**
     * 远程调用异常
     *
     * @param responseStr 响应结果
     * @param url         url
     * @param e           异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, String url, Exception e) {
        String msg = String.format("极兔快递调用异常 response: %s, url: %s", responseStr, url);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
    }

    public static void main(String[] args) {
        JtExpressRpc rpc = new JtExpressRpc();
        LogisticsAccountDO account = LogisticsAccountDO.builder()
                .bizAccount("J0086474299")
                .clientCode("178337126125932605")
                .clientSecret("0258d71b55fc45e3ad7a7f38bf4b201a")
                .apiUrl("https://uat-openapi.jtexpress.com.cn")
                .appSessionKey("H5CD3zE6")
                .build();

        // 测试打印面单
        String pdfBase64 = rpc.printWaybill(account, "***************");
        log.info("{}", pdfBase64);
    }

}
