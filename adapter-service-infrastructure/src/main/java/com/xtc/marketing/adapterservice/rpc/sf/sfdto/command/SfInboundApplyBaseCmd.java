package com.xtc.marketing.adapterservice.rpc.sf.sfdto.command;

import com.google.gson.annotations.SerializedName;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.SfWarehouseBaseRequest;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class SfInboundApplyBaseCmd extends SfWarehouseBaseRequest {

    /**
     * 订单集合
     */
    @SerializedName("PurchaseOrders")
    private List<SfInboundApplyCmd> orders;

}
