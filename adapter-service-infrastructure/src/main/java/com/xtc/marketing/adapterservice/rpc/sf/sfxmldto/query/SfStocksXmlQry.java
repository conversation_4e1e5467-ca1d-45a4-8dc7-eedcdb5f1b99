package com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.query;

import com.google.common.collect.Lists;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import com.thoughtworks.xstream.annotations.XStreamOmitField;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.SfRequestXmlDTO;
import lombok.*;

import java.util.List;

/**
 * 顺丰仓库查询库存
 */
@Setter
@Getter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@XStreamAlias("RTInventorys")
public class SfStocksXmlQry extends SfRequestXmlDTO {

    /**
     * 仓库编码
     */
    @XStreamOmitField
    private String warehouseCode;

    /**
     * 商品编号列表
     */
    @XStreamImplicit
    private List<Sku> skus;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @XStreamAlias("RTInventory")
    public static class Sku {

        /**
         * 商品编号
         */
        @XStreamAlias("SkuNo")
        private String skuId;

    }

    @Override
    public String toRequestXml(String requestBodyXml, String companyCode) {
        return "<RTInventoryQueryRequest>"
                + "<CompanyCode>"
                + companyCode
                + "</CompanyCode>"
                + "<WarehouseCode>"
                + this.warehouseCode
                + "</WarehouseCode>"
                + "<InventoryStatus>10</InventoryStatus>"
                + requestBodyXml
                + "</RTInventoryQueryRequest>";
    }

    private static final List<String> RESPONSE_PARSE_ELEMENTS = Lists.newArrayList("RTInventoryQueryResponse");

    @Override
    public List<String> getResponseParseElements() {
        return RESPONSE_PARSE_ELEMENTS;
    }

}
