package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.command;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 微信视频号小店面单取号参数
 */
@Getter
@Setter
@ToString
public class WechatChannelsShopLogisticsCreateCmd {

    /**
     * 电子面单订单id，全局唯一id（从预取号接口获取或者自定义）,注意该字段类型用String传递，但是数据内容要求是Uint64
     * <p>从 {@code /channels/ec/logistics/ewaybill/biz/order/precreate} 接口拿到电子面单订单id</p>
     */
    @SerializedName("ewaybill_order_id")
    private String orderNo;
    /**
     * 快递公司id
     */
    @SerializedName("delivery_id")
    private String deliveryId;
    /**
     * 网点编码
     */
    @SerializedName("site_code")
    private String siteCode;
    /**
     * 电子面单账号id
     */
    @SerializedName("ewaybill_acct_id")
    private String waybillAcctId;
    /**
     * 寄件人，传明文，结构体详情请参考Address
     */
    private Address sender;
    /**
     * 收件人
     */
    private Address receiver;
    /**
     * 订单信息，结构体详情请参考EcOrderInfo
     */
    @SerializedName("ec_order_list")
    private List<EcOrderInfo> orders;
    /**
     * 备注
     */
    private String remark;
    /**
     * 店铺id（从查询开通账号信息接口获取）
     */
    @SerializedName("shop_id")
    private String shopId;
    /**
     * 模板id
     */
    @SerializedName("template_id")
    private String templateId;

    /**
     * 快递类型
     */
    @SerializedName("order_type")
    private Integer logisticsType;

    @Getter
    @Setter
    @ToString
    public static class Address {

        /**
         * 人名
         */
        private String name;
        /**
         * 联系电话
         */
        private String mobile;
        /**
         * 省
         */
        private String province;
        /**
         * 市
         */
        private String city;
        /**
         * 区
         */
        private String county;
        /**
         * 街道(收件人非必填)
         */
        private String street;
        /**
         * 详细地址
         */
        private String address;

    }

    @Getter
    @Setter
    @ToString
    public static class EcOrderInfo {

        /**
         * 订单id
         */
        @SerializedName("ec_order_id")
        private long orderNo;
        /**
         * 订单商品信息
         */
        @SerializedName("goods_list")
        private List<GoodsInfo> goods;
        /**
         * 代发的订单密文
         */
        @SerializedName("ewaybill_order_code")
        private String waybillOrderCode;
        /**
         * 代发的订单所属店铺appid
         */
        @SerializedName("ewaybill_order_appid")
        private String waybillOrderAppid;

    }

    @Getter
    @Setter
    @ToString
    public static class GoodsInfo {

        /**
         * 商品名
         */
        @SerializedName("good_name")
        private String goodName;
        /**
         * 商品个数
         */
        @SerializedName("good_count")
        private Integer goodCount;
        /**
         * 商品product id
         */
        @SerializedName("product_id")
        private Long productId;
        /**
         * 商品sku id
         */
        @SerializedName("sku_id")
        private Long skuId;
        /**
         * 商家自定义spu id
         */
        @SerializedName("out_product_id")
        private String outProductId;
        /**
         * 商家自定义sku id
         */
        @SerializedName("out_sku_id")
        private String outSkuId;
        /**
         * 商家自定义商品详情（如果不传平台的商品id的话，会展示该字段在面单商品区域）
         */
        @SerializedName("out_goods_info")
        private String outGoodsInfo;

    }

}