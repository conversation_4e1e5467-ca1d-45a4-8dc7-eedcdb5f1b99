package com.xtc.marketing.adapterservice.rpc.sf.sfxmldto;

import com.xtc.marketing.adapterservice.rpc.sf.util.XStreamUtil;

import java.util.List;

/**
 * 顺丰仓库 xml 请求
 */
public abstract class SfRequestXmlDTO {

    /**
     * 转换请求 xml
     *
     * @param requestBodyXml 请求体 xml
     * @param companyCode    货主编码
     * @return 请求 xml
     */
    public abstract String toRequestXml(String requestBodyXml, String companyCode);

    /**
     * 获取请求响应结果的 xml 元素解析层次
     *
     * @return xml 元素解析层次
     */
    public abstract List<String> getResponseParseElements();

    /**
     * 判断使用其他格式的 xml
     *
     * @return 执行结果
     */
    public boolean otherXml() {
        return false;
    }

    /**
     * 转换请求 xml
     *
     * @param companyCode 货主编码
     * @return 请求 xml
     */
    public final String toRequestXml(String companyCode) {
        String requestBodyXml = XStreamUtil.toXml(this);
        return toRequestXml(requestBodyXml, companyCode);
    }

}
