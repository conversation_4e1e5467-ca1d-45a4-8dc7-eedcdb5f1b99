package com.xtc.marketing.adapterservice.rpc.ems.emsdto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmsAddressCmd {

    /**
     * 姓名
     */
    private String name;

    /**
     * 用户移动电话
     */
    private String mobile;

    /**
     * 省名称，使用国标全称
     */
    private String prov;

    /**
     * 市名称，使用国标全称
     */
    private String city;

    /**
     * 用户所在县（区），使用国标全称
     */
    private String county;

    /**
     * 详细地址
     */
    private String address;

}
