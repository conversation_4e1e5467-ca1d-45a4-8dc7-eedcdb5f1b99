package com.xtc.marketing.adapterservice.rpc.ems.emsdto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmsInterceptCmd {

    /**
     * 1: 退回到寄件人 2：修改用户的收货地址，但无法更改电话和姓名
     */
    private String identification;

    /**
     * 运单号
     */
    private String waybillNo;

}
