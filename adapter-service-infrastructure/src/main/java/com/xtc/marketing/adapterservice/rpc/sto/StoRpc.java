package com.xtc.marketing.adapterservice.rpc.sto;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.JsonElement;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.rpc.sto.stodto.StoBaseDTO;
import com.xtc.marketing.adapterservice.rpc.sto.stodto.StoCreateOrderDTO;
import com.xtc.marketing.adapterservice.rpc.sto.stodto.StoInterceptDTO;
import com.xtc.marketing.adapterservice.rpc.sto.stodto.StoRouteDTO;
import com.xtc.marketing.adapterservice.rpc.sto.stodto.command.StoCancelOrderCmd;
import com.xtc.marketing.adapterservice.rpc.sto.stodto.command.StoCreateOrderCmd;
import com.xtc.marketing.adapterservice.rpc.sto.stodto.command.StoInterceptCmd;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpServerErrorException;

import java.rmi.RemoteException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 申通快递RPC
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class StoRpc {

    /**
     * API：订单创建接口
     */
    private static final ImmutableMap<String, String> API_OMS_EXPRESS_ORDER_CREATE = ImmutableMap.of("OMS_EXPRESS_ORDER_CREATE", "sto_oms");
    /**
     * API：取消订单
     */
    private static final ImmutableMap<String, String> API_OMS_EXPRESS_ORDER_CANCEL = ImmutableMap.of("EDI_MODIFY_ORDER_CANCEL", "edi_modify_order");
    /**
     * API：物流轨迹查询
     */
    private static final ImmutableMap<String, String> API_STO_TRACE_QUERY_COMMON = ImmutableMap.of("STO_TRACE_QUERY_COMMON", "sto_trace_query");
    /**
     * API：拦截指令下发
     */
    private static final ImmutableMap<String, String> API_INTERCEPT_CREATE_STANDARD = ImmutableMap.of("INTERCEPT_CREATE_STANDARD", "reverse-center");

    /**
     * 下单，生成运单号
     *
     * @param account 账号
     * @param cmd     参数
     * @return 运单号
     */
    public StoCreateOrderDTO createOrder(LogisticsAccountDO account, StoCreateOrderCmd cmd) {
        return this.call(account, API_OMS_EXPRESS_ORDER_CREATE, cmd, StoCreateOrderDTO.class);
    }

    /**
     * 取消订单
     *
     * @param account 账号
     * @param cmd     参数
     */
    public boolean cancelOrder(LogisticsAccountDO account, StoCancelOrderCmd cmd) {
        StoBaseDTO call = this.call(account, API_OMS_EXPRESS_ORDER_CANCEL, cmd, StoBaseDTO.class);
        return call.getSuccess();
    }

    /**
     * 查询快递路由
     *
     * @param account   账号
     * @param waybillNo 运单号
     * @return 路由
     */
    public List<StoRouteDTO> searchRoutes(LogisticsAccountDO account, String waybillNo) {
        Map<String, Object> body = Maps.newHashMapWithExpectedSize(2);
        body.put("waybillNoList", Lists.newArrayList(waybillNo));
        StoBaseDTO stoBaseDTO = this.call(account, API_STO_TRACE_QUERY_COMMON, body, StoBaseDTO.class);
        // 路由数据解析
        String routes = Optional.ofNullable(stoBaseDTO.getData())
                .map(JsonElement::getAsJsonObject)
                .map(jsonObject -> jsonObject.get(waybillNo))
                .map(JsonElement::toString)
                .orElse(null);
        return GsonUtil.jsonToList(routes, StoRouteDTO.class);
    }

    /**
     * 拦截快递
     *
     * @param account 账号
     * @param cmd     参数
     */
    public void intercept(LogisticsAccountDO account, StoInterceptCmd cmd) {
        StoInterceptDTO intercept = this.call(account, API_INTERCEPT_CREATE_STANDARD, cmd, StoInterceptDTO.class);
        if (BooleanUtils.isNotTrue(intercept.getIsAddSuccess())) {
            String msg = Optional.ofNullable(intercept.getData())
                    .filter(jsonElement -> !jsonElement.isJsonNull())
                    .map(JsonElement::toString)
                    .orElse("申通拦截异常，原因未知需要人工确认");
            throw SysException.of(SysErrorCode.S_RPC_ERROR.getErrCode(), "申通快递调用异常 response: " + msg);
        }
    }

    /**
     * 远程调用
     *
     * @param account       账号
     * @param api           接口
     * @param request       请求参数
     * @param responseClass 响应结果类型
     * @return 返回结果
     */
    private <T extends StoBaseDTO> T call(LogisticsAccountDO account, ImmutableMap<String, String> api,
                                          Object request, Class<T> responseClass) {
        MultiValueMap<String, Object> requestBody = this.buildMultiValueMap(account, api, request);
        String response = "";
        try {
            response = HttpUtil.postForForm(account.getApiUrl(), requestBody);
            if (StringUtils.isBlank(response)) {
                throw new RemoteException("RPC返回异常业务状态");
            }
        } catch (Exception e) {
            if (e instanceof HttpServerErrorException) {
                response = ((HttpServerErrorException) e).getResponseBodyAsString();
            }
            String msg = String.format("申通快递调用异常 response: %s", response);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, response, msg, e);
        }
        return this.parseResponse(response, responseClass);
    }

    /**
     * 解析响应结果
     *
     * @param response      响应结果
     * @param responseClass 响应结果类型
     * @param <T>           响应结果类型
     * @return 响应结果
     */
    private <T extends StoBaseDTO> T parseResponse(String response, Class<T> responseClass) {
        try {
            StoBaseDTO baseDTO = GsonUtil.jsonToBean(response, StoBaseDTO.class);
            if (baseDTO.isFailure()) {
                throw new RemoteException("RPC返回异常状态码");
            }
            String data = baseDTO.getData().toString();
            boolean jsonToBean = responseClass != StoBaseDTO.class && baseDTO.getData().isJsonObject();
            T resultDTO = jsonToBean ? GsonUtil.jsonToBean(data, responseClass) : responseClass.newInstance();
            BeanUtils.copyProperties(baseDTO, resultDTO);
            return resultDTO;
        } catch (Exception e) {
            String msg = String.format("申通快递响应结果解析异常 response: %s", response);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, response, msg, e);
        }
    }

    /**
     * 构建请求参数
     *
     * @param account 账号
     * @param api     接口
     * @param request 接口参数
     * @return 请求参数
     */
    private MultiValueMap<String, Object> buildMultiValueMap(LogisticsAccountDO account, ImmutableMap<String, String> api, Object request) {
        try {
            MultiValueMap<String, Object> multiValueMap = new LinkedMultiValueMap<>();
            multiValueMap.add("from_appkey", account.getClientCode());
            multiValueMap.add("from_code", account.getClientCode());

            api.entrySet().stream().findFirst().ifPresent(entry -> {
                multiValueMap.add("to_appkey", entry.getValue());
                multiValueMap.add("to_code", entry.getValue());
                multiValueMap.add("api_name", entry.getKey());
            });

            String requestJson = GsonUtil.objectToJson(request);
            multiValueMap.add("content", requestJson);

            String signature = this.buildSignature(requestJson, account.getClientSecret());
            multiValueMap.add("data_digest", signature);
            return multiValueMap;
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, "申通快递调用异常，请求参数构建失败", e.getMessage(), e);
        }
    }

    /**
     * 构建请求的签名
     *
     * @param content   请求参数
     * @param secretKey 密钥
     * @return 签名
     */
    private String buildSignature(String content, String secretKey) {
        String text = content + secretKey;
        byte[] md5 = DigestUtils.md5(text);
        return Base64.encodeBase64String(md5);
    }

    public static void main(String[] args) {
        LogisticsAccountDO account = LogisticsAccountDO.builder()
                .apiUrl("https://cloudinter-linkgateway.sto.cn/gateway/link.do")
                .clientCode("xxxxxx")
                .clientSecret("xxxxxx")
                .build();

        StoRpc rpc = new StoRpc();
        System.out.println(rpc.searchRoutes(account, "xxxxxx"));
    }

}
