package com.xtc.marketing.adapterservice.rpc.jitu.jitudto.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 极兔超区查询响应
 */
@Getter
@Setter
@ToString
public class JtExpressRangeCheckResponse {

    /**
     * 是否可达： 1.可达 2.不可达
     */
    private Integer rangeCheckResult;
    /**
     * 异常原因
     */
    private String abnormalReason;
    /**
     * 异常类型
     */
    private String abnormalType;

}
