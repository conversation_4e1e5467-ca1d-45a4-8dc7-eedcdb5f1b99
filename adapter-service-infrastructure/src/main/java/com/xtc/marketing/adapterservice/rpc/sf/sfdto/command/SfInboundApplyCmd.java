package com.xtc.marketing.adapterservice.rpc.sf.sfdto.command;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 顺丰仓库，入库申请单
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SfInboundApplyCmd {

    /**
     * 供应商代码
     */
    @SerializedName("VendorCode")
    private String vendorCode;

    /**
     * 仓库编码
     */
    @SerializedName("WarehouseCode")
    private String warehouseCode;

    /**
     * 入库单号（业务方单号）
     */
    @SerializedName("ErpOrder")
    private String orderId;

    /**
     * 客户订单类型，默认：10
     */
    @SerializedName("ErpOrderType")
    private final String erpOrderType = "10";

    /**
     * 顺丰订单类型，默认：采购入库
     * 10：采购入库
     * 20：退货入库
     * 30：调拨入库
     * 40：赠品入库
     * 50：换货入库
     * 60：其它入库
     * 如果超出以上6种类型，请单独联系业务部门。
     */
    @SerializedName("SFOrderType")
    private final String sfOrderType = "采购入库";

    /**
     * 预计收货时间（格式：YYYY-MM-DD HH24:MI:SS）
     */
    @SerializedName("ScheduledReceiptDate")
    private LocalDateTime scheduledReceiptDate;

    /**
     * 商品列表
     */
    @SerializedName("Items")
    private List<SfInboundItemCmd> items;

}
