package com.xtc.marketing.adapterservice.rpc.jitu.jitudto.request;

import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.JtExpressBaseRequest;
import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.response.JtExpressCancelOrderResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 极兔取消订单参数DTO
 */
@Getter
@Setter
@ToString
public class JtExpressCancelOrderRequest extends JtExpressBaseRequest<JtExpressCancelOrderResponse> {

    /**
     * 客户代码/商家编码
     */
    private String customerCode;
    /**
     * 订单类型 1（散客），2（协议客户）
     */
    private String orderType;
    /**
     * 客户订单号
     */
    private String txlogisticId;
    /**
     * 取消原因
     */
    private String reason;

    @Override
    public Class<JtExpressCancelOrderResponse> getResponseClass() {
        return JtExpressCancelOrderResponse.class;
    }

    @Override
    public String getApiPath() {
        return "/webopenplatformapi/api/order/cancelOrder";
    }

}