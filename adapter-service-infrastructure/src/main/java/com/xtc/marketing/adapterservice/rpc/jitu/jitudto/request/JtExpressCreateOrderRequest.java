package com.xtc.marketing.adapterservice.rpc.jitu.jitudto.request;

import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.JtExpressBaseRequest;
import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.response.JtExpressCreateOrderResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 极兔创建订单请求
 */
@Getter
@Setter
@ToString
public class JtExpressCreateOrderRequest extends JtExpressBaseRequest<JtExpressCreateOrderResponse> {

    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 网络类型
     */
    private String network;
    /**
     * 客户订单号
     */
    private String txlogisticId;
    /**
     * 运单号
     */
    private String billCode;
    /**
     * 快件类型
     */
    private String expressType;
    /**
     * 订单类型
     */
    private String orderType;
    /**
     * 服务类型
     */
    private String serviceType;
    /**
     * 派送类型
     */
    private String deliveryType;
    /**
     * 支付类型
     */
    private String payType;
    /**
     * 寄件人信息
     */
    private JtExpressSender sender;
    /**
     * 收件人信息
     */
    private JtExpressReceiver receiver;
    /**
     * 上门取货开始时间
     */
    private String sendStartTime;
    /**
     * 上门取货结束时间
     */
    private String sendEndTime;
    /**
     * 物品类型
     */
    private String goodsType;
    /**
     * 是否实名制
     */
    private String isRealName;
    /**
     * 长(cm)
     */
    private String length;
    /**
     * 宽(cm)
     */
    private String width;
    /**
     * 高(cm)
     */
    private String height;
    /**
     * 重量(kg)
     */
    private String weight;
    /**
     * 包裹总件数
     */
    private Integer totalQuantity;
    /**
     * 货物价值
     */
    private String itemsValue;
    /**
     * 代收货款币别
     */
    private String priceCurrency;
    /**
     * 保价金额
     */
    private String offerFee;
    /**
     * 备注
     */
    private String remark;
    /**
     * 商品信息列表
     */
    private List<JtExpressItem> items;
    /**
     * 海关信息
     */
    private JtExpressCustomsInfo customsInfo;
    /**
     * 收寄网点编码
     */
    private String postSiteCode;

    @Override
    public Class<JtExpressCreateOrderResponse> getResponseClass() {
        return JtExpressCreateOrderResponse.class;
    }

    @Override
    public String getApiPath() {
        return "/webopenplatformapi/api/order/addOrder";
    }

    /**
     * 寄件人信息
     */
    @Getter
    @Setter
    @ToString
    public static class JtExpressSender {

        /**
         * 寄件人姓名
         */
        private String name;
        /**
         * 寄件公司
         */
        private String company;
        /**
         * 寄件邮编
         */
        private String postCode;
        /**
         * 寄件邮箱
         */
        private String mailBox;
        /**
         * 寄件手机（手机和电话二选一必填）
         */
        private String mobile;
        /**
         * 寄件电话（手机和电话二选一必填）
         */
        private String phone;
        /**
         * 寄件国家三字码（如：中国=CHN、印尼=IDN）
         */
        private String countryCode;
        /**
         * 寄件省份
         */
        private String prov;
        /**
         * 寄件城市
         */
        private String city;
        /**
         * 寄件区域
         */
        private String area;
        /**
         * 寄件乡镇
         */
        private String town;
        /**
         * 寄件街道
         */
        private String street;
        /**
         * 寄件详细地址
         */
        private String address;

    }

    /**
     * 收件人信息
     */
    @Getter
    @Setter
    @ToString
    public static class JtExpressReceiver {

        /**
         * 收件人姓名
         */
        private String name;
        /**
         * 收件公司
         */
        private String company;
        /**
         * 收件邮编
         */
        private String postCode;
        /**
         * 收件邮箱
         */
        private String mailBox;
        /**
         * 收件手机（手机和电话二选一必填）
         */
        private String mobile;
        /**
         * 收件电话（手机和电话二选一必填）
         */
        private String phone;
        /**
         * 收件国家三字码（如：中国=CHN、印尼=IDN）
         */
        private String countryCode;
        /**
         * 收件省份
         */
        private String prov;
        /**
         * 收件城市
         */
        private String city;
        /**
         * 收件区域
         */
        private String area;
        /**
         * 收件乡镇
         */
        private String town;
        /**
         * 收件街道
         */
        private String street;
        /**
         * 收件详细地址
         */
        private String address;

    }

    /**
     * 商品信息
     */
    @Getter
    @Setter
    @ToString
    public static class JtExpressItem {

        /**
         * 物品类型: bm000001 文件 bm000002 数码产品 ...
         */
        private String itemType;
        /**
         * 物品名称
         */
        private String itemName;
        /**
         * 物品中文名称
         */
        private String chineseName;
        /**
         * 物品英文名称
         */
        private String englishName;
        /**
         * 件数，≤1
         */
        private Integer number;
        /**
         * 申报价值(数值型)
         */
        private String itemValue;
        /**
         * 申报货款币别（默认本国币别，如：RMB）
         */
        private String priceCurrency;
        /**
         * 物品描述
         */
        private String desc;
        /**
         * 商品URL
         */
        private String itemUrl;

    }

    /**
     * 海关信息
     */
    @Getter
    @Setter
    @ToString
    public static class JtExpressCustomsInfo {

        /**
         * 海关编码
         */
        private String customsCode;
        /**
         * 海关名称
         */
        private String customsName;
        /**
         * 海关申报价值
         */
        private String customsValue;
        /**
         * 海关币别
         */
        private String customsCurrency;

    }

}
