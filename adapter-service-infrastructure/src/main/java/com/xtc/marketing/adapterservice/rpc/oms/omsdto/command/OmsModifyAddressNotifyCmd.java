package com.xtc.marketing.adapterservice.rpc.oms.omsdto.command;

import com.google.gson.annotations.Expose;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.OmsBaseRequest;
import lombok.*;
import org.springframework.http.HttpMethod;

/**
 * 修改地址通知参数
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OmsModifyAddressNotifyCmd extends OmsBaseRequest<Void> {

    /**
     * 订单号
     */
    private String tradeId;
    /**
     * 收件人密文数据
     */
    private String receiverOaid;
    /**
     * 收件人姓名
     */
    private String receiverName;
    /**
     * 收件人手机号
     */
    private String receiverMobile;
    /**
     * 收件人省份
     */
    private String receiverProvince;
    /**
     * 收件人城市
     */
    private String receiverCity;
    /**
     * 收件人区县
     */
    private String receiverDistrict;
    /**
     * 收件人乡镇
     */
    private String receiverTown;
    /**
     * 收件人详细地址
     */
    private String receiverAddress;
    /**
     * 事件（仅在请求url做标识，并且 Gson 序列化与反序列化都不处理）
     */
    @Expose
    private String event;

    @Override
    public String getApiPath() {
        String eventParam = event == null ? "" : "?event=" + event;
        return "/api/trade/notify-receive/modify-address" + eventParam;
    }

    @Override
    public HttpMethod getRequestMethod() {
        return HttpMethod.POST;
    }

    @Override
    public Class<Void> getResponseClass() {
        return Void.class;
    }

}
