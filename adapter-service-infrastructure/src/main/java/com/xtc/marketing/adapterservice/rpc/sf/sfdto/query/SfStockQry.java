package com.xtc.marketing.adapterservice.rpc.sf.sfdto.query;

import com.google.gson.annotations.SerializedName;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.SfWarehouseBaseRequest;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class SfStockQry extends SfWarehouseBaseRequest {

    /**
     * 仓库编码
     */
    @SerializedName("WarehouseCode")
    private String warehouseCode;

    /**
     * sku列表
     */
    @SerializedName("RTInventorys")
    private List<SfSkuQry> skus;

}
