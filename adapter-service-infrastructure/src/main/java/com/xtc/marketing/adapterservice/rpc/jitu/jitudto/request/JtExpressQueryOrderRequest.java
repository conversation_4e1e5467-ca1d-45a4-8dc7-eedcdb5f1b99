package com.xtc.marketing.adapterservice.rpc.jitu.jitudto.request;

import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.JtExpressBaseRequest;
import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.response.JtExpressQueryOrderResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 极兔查询订单请求
 */
@Getter
@Setter
@ToString
public class JtExpressQueryOrderRequest extends JtExpressBaseRequest<JtExpressQueryOrderResponse> {

    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 查询命令
     * 1.按客户订单编号查询
     * 2.按运单编号查询（用运单号查订单状态）
     * 3.按下单时间段查询（查订单）（按时间段查询返回不限制条数）
     * 4.订单编号
     */
    private Integer command;
    /**
     * 客户订单编号查询/运单编号/订单号（公司内部）
     * command=1 客户订单编号查询
     * command=2 运单编号查询
     * command=4 订单号（公司内部）
     * 支持200个
     */
    private List<String> serialNumber;
    /**
     * 开始时间(24小时制: yyyy-MM-dd HH:mm:ss)
     * command=3时必填
     */
    private String startDate;
    /**
     * 结束时间(24小时制: yyyy-MM-dd HH:mm:ss)
     * command=3时必填，时间范围最大7天
     */
    private String endDate;
    /**
     * 状态（订单状态筛选数据使用，默认全部）
     * 已取消 104
     * 已取件 103
     * 已调派业务员 102
     * 已调派网点 101
     * 未调派 100
     */
    private Integer status;
    /**
     * 当前页
     * command=3时必填
     */
    private Integer current;
    /**
     * 每页大小
     * command=3时必填
     */
    private Integer size;

    @Override
    public Class<JtExpressQueryOrderResponse> getResponseClass() {
        return JtExpressQueryOrderResponse.class;
    }

    @Override
    public String getApiPath() {
        return "/webopenplatformapi/api/order/getOrders";
    }

}
