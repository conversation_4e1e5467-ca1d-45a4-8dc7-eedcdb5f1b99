package com.xtc.marketing.adapterservice.rpc.jd.jddto.enums;

import com.google.common.collect.ImmutableMap;
import lombok.Getter;

import java.util.Map;

/**
 * 京东平台快递公司
 */
@Getter
public enum JdLogisticsCompany {
    /**
     * 圆通
     */
    YTO("463", "YTO",
            ImmutableMap.of("JD_XTC", "754096", "JD_BBK", "754096", "JD_BBK_DIANDUJI", "754096"),
            "https://template-content.jd.com/template-oss?tempCode=ytokd76x130"),
    /**
     * 京东
     */
    JD("2087", "JD",
            ImmutableMap.of("JD_XTC", "020K2415972", "JD_BBK", "020K2415952", "JD_BBK_DIANDUJI", "020K4182542"),
            "https://template-content.jd.com/template-oss?tempCode=jdkd76x130"),
    /**
     * 顺丰
     */
    SF("467", "SF",
            ImmutableMap.of("JD_XTC", "7698089145", "JD_BBK", "7698089145", "JD_BBK_DIANDUJI", "7698089145"),
            "https://template-content.jd.com/template-oss?tempCode=sfkd76x130"),
    /**
     * EMS
     */
    EMS("3668", "EMSBZ",
            ImmutableMap.of("JD_XTC", "1030004233095", "JD_BBK", "1030004233095", "JD_BBK_DIANDUJI", "1030004233095"),
            "https://template-content.jd.com/template-oss?tempCode=emsbzkd76x130"),
    ;

    /**
     * 承运商ID（电子面单、订单发货，获取商家物流公司接口：FceAlphaGetVenderCarrierRequest）
     */
    private final String providerId;
    /**
     * 承运商编码（电子面单）
     */
    private final String providerCode;
    /**
     * 商家编码，或者网点编码，或者结算编码（电子面单）
     */
    private final Map<String, String> customerCode;
    /**
     * 电子面单模板地址（电子面单）
     */
    private final String printTemplateUrl;

    JdLogisticsCompany(String providerId, String providerCode,
                       Map<String, String> customerCode, String printTemplateUrl) {
        this.providerId = providerId;
        this.providerCode = providerCode;
        this.customerCode = customerCode;
        this.printTemplateUrl = printTemplateUrl;
    }

    /**
     * 获取商家编码
     *
     * @param shopCode 店铺代码
     * @return 商家编码
     */
    public String getCustomerCode(String shopCode) {
        return customerCode.get(shopCode);
    }
}
