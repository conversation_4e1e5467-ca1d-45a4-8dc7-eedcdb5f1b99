package com.xtc.marketing.adapterservice.rpc.ems;

import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.rpc.ems.emsdto.EmsBaseDTO;
import com.xtc.marketing.adapterservice.rpc.ems.emsdto.EmsRouteDTO;
import com.xtc.marketing.adapterservice.rpc.ems.emsdto.command.EmsCreateOrderCmd;
import com.xtc.marketing.adapterservice.rpc.ems.emsdto.command.EmsInterceptCmd;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpServerErrorException;

import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.util.List;
import java.util.UUID;

/**
 * EMS物流RPC
 * <p><a href="https://api.ems.com.cn/#/gnapijj">开放平台</a></p>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class EmsRpc {

    /**
     * 生产环境地址
     */
    private static final String DOMAIN = "http://211.156.195.180/eis-itf-webext/interface";

    /**
     * 特快专递 1, 标准快递 6, 国内标快 11
     */
    public static final String BIZ_PRODUCT_NORMAL = "1";
    /**
     * 内件性质(1：文件 3、物品)
     */
    public static final String CONTENTS_ATTRIBUTE = "3";
    /**
     * 电商客户标识(ecommerceuserid这个字段可以填一个<50 位随机数)
     */
    public static final String ECOMMERCE_USER_ID = "XTC";

    /**
     * API：查询快递路由
     */
    private static final String API_SEARCH_ROUTES = "qps_querytrace";
    /**
     * API：下单，生成运单号
     */
    private static final String API_CREATE_ORDER = "oms_ordercreate_waybillno";
    /**
     * API：拦截（与查询路由使用同一个API）
     */
    private static final String API_INTERCEPT = "qps_querytrace";

    /**
     * 下单，生成运单号
     *
     * @param account 账号
     * @param cmd     请求参数
     * @return 运单号
     */
    public EmsBaseDTO createOrder(LogisticsAccountDO account, EmsCreateOrderCmd cmd) {
        return this.call(account, API_CREATE_ORDER, cmd, EmsBaseDTO.class);
    }

    /**
     * 拦截快递
     *
     * @param account 账号
     * @param cmd     请求参数
     * @return 请求结果
     */
    public EmsBaseDTO intercept(LogisticsAccountDO account, EmsInterceptCmd cmd) {
        return this.call(account, API_INTERCEPT, cmd, EmsBaseDTO.class);
    }

    /**
     * 查询快递路由
     * <p><a href="https://api.ems.com.cn/#/csgj">操作码数据字典</a></p>
     *
     * @param account   账号
     * @param wayBillNo 运单号
     * @return 路由
     */
    public List<EmsRouteDTO> searchRoutes(LogisticsAccountDO account, String wayBillNo) {
        JsonObject logisticsJson = new JsonObject();
        logisticsJson.addProperty("waybill_no", wayBillNo);
        EmsBaseDTO baseDTO = this.call(account, API_SEARCH_ROUTES, logisticsJson, EmsBaseDTO.class);
        return GsonUtil.jsonToList(baseDTO.getBody().toString(), EmsRouteDTO.class);
    }

    /**
     * 远程调用
     *
     * @param account 账号
     * @param apiCode 接口代码
     * @param request 请求参数
     * @return 返回结果
     */
    private <T extends EmsBaseDTO> T call(LogisticsAccountDO account, String apiCode,
                                          Object request, Class<T> responseClass) {
        // 构建参数
        MultiValueMap<String, Object> params = this.buildBody(account, apiCode, GsonUtil.objectToJson(request));

        String response = "";
        try {
            // 请求接口
            response = HttpUtil.postForForm(DOMAIN, params);
            if (StringUtils.isBlank(response)) {
                throw new RemoteException("RPC返回异常业务状态");
            }
        } catch (Exception e) {
            if (e instanceof HttpServerErrorException) {
                response = ((HttpServerErrorException) e).getResponseBodyAsString();
            }
            String msg = String.format("EMS物流调用异常 response: %s", response);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, response, msg, e);
        }

        return this.parseResponse(response, responseClass);
    }

    /**
     * 解析响应结果
     *
     * @param response      响应结果
     * @param responseClass 响应结果类型
     * @param <T>           响应结果类型
     * @return 响应结果
     */
    private <T extends EmsBaseDTO> T parseResponse(String response, Class<T> responseClass) {
        try {
            // 解析返回值
            EmsBaseDTO baseDTO = GsonUtil.jsonToBean(response, EmsBaseDTO.class);
            if (baseDTO.isFailure()) {
                throw new RemoteException("RPC返回异常状态码");
            }
            String data = baseDTO.getBody().toString();
            boolean jsonToBean = responseClass != EmsBaseDTO.class && baseDTO.getBody().isJsonObject();
            T resultDTO = jsonToBean ? GsonUtil.jsonToBean(data, responseClass) : responseClass.newInstance();
            BeanUtils.copyProperties(baseDTO, resultDTO);
            return resultDTO;
        } catch (Exception e) {
            String msg = String.format("EMS物流响应结果解析异常 response: %s", response);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, response, msg, e);
        }
    }

    /**
     * 构建参数
     *
     * @param account 账号
     * @param apiCode 接口代码
     * @param param   请求参数
     * @return 参数
     */
    private MultiValueMap<String, Object> buildBody(LogisticsAccountDO account, String apiCode, String param) {
        try {
            // 签名
            String signature = this.sign(param, account.getClientSecret());

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("logistics_interface", param);
            body.add("apiCode", apiCode);
            body.add("senderNo", account.getBizAccount());
            body.add("serialNo", UUID.randomUUID().toString());
            body.add("msgType", 0);
            body.add("signature", signature);
            return body;
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, "EMS物流调用异常，组装参数异常", e);
        }
    }

    /**
     * 签名
     *
     * @param param        请求参数
     * @param clientSecret 秘钥
     * @return 签名
     */
    private String sign(String param, String clientSecret) {
        String shaEncodeStr = DigestUtils.sha256Hex(param + clientSecret);
        return new String(Base64Utils.encode(shaEncodeStr.getBytes(StandardCharsets.UTF_8)));
    }

}
