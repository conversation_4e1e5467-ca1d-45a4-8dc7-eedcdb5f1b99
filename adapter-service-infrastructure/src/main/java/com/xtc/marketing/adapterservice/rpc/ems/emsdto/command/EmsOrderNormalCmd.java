package com.xtc.marketing.adapterservice.rpc.ems.emsdto.command;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmsOrderNormalCmd {

    /**
     * 电商客户标识(ecommerceuserid这个字段可以填一个<50 位随机数)
     */
    @SerializedName("ecommerce_user_id")
    private String ecommerceUserId;

    /**
     * 订单号
     */
    @SerializedName("logistics_order_no")
    private String logisticsOrderNo;

    /**
     * 帐号
     */
    @SerializedName("sender_no")
    private String senderNo;

    @SerializedName("base_product_no")
    private String baseProductNo;

    /**
     * 业务产品分类
     * 1：特快专递
     * 2：快递包裹
     * 3：特快到付
     * 9：国内即日
     * 10：电商标快
     * 11：国内标快
     */
    @SerializedName("biz_product_no")
    private String bizProductNo;

    /**
     * 寄件人
     */
    private EmsAddressCmd sender;

    /**
     * 收件人
     */
    private EmsAddressCmd receiver;

    /**
     * 货物列表
     */
    private List<EmsCargoCmd> cargos;

}
