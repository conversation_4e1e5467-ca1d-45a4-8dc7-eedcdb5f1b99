package com.xtc.marketing.adapterservice.rpc.jd.jdjcqdto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 京东消息订阅响应对象
 */
@Getter
@Setter
@ToString
public class JcqResponseDTO {

    /**
     * 请求ID
     */
    private String requestId;
    /**
     * 拉取结果
     */
    private JcqPullResultDTO result;
    /**
     * 错误信息
     */
    private Error error;

    /**
     * 错误信息
     */
    @Data
    public static class Error {

        private String code;
        private String message;
        private String status;

    }

}
