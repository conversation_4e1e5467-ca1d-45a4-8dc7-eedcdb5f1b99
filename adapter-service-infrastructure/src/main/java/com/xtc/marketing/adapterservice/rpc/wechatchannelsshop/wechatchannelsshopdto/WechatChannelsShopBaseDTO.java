package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public abstract class WechatChannelsShopBaseDTO {

    /**
     * 成功代码
     */
    private static final int SUCCESS_CODE = 0;

    /**
     * 错误码
     */
    @SerializedName(value = "errCode", alternate = "errcode")
    private Integer errCode;
    /**
     * 错误信息
     */
    @SerializedName(value = "errMsg", alternate = "errmsg")
    private String errMsg;
    /**
     * 原始数据
     */
    private String originData;

    /**
     * 获取数据对应的 Key 减少返回值的层级
     * <p>{@code {"errCode":0,"errMsg":"ok","order":{}}} 配置 {@code "order"}</p>
     * <p>{@code {"errCode":0,"errMsg":"ok","page_orders":{},"has_next":true,"next_key":"xxxxxx"}} 配置空字符串 {@code ""}</p>
     *
     * @return 数据对应的 Key
     */
    public abstract String getResponseDataKey();

    /**
     * 判断调用失败
     *
     * @return 执行结果
     */
    public boolean isFailure() {
        return errCode != null && errCode != SUCCESS_CODE;
    }

    /**
     * 判断调用成功
     *
     * @return 执行结果
     */
    public boolean isSucceed() {
        return !this.isFailure();
    }

}
