package com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 内部购机发票
 */
@Getter
@Setter
@ToString
public class InternalShopReceiptDTO {

    /**
     * 发票历史id
     */
    @SerializedName("history_id")
    private Integer historyId;

    /**
     * 订单号
     */
    @SerializedName("order_sn")
    private String orderSn;

    /**
     * 卖家id
     */
    @SerializedName("seller_id")
    private Integer sellerId;

    /**
     * 会员id
     */
    @SerializedName("member_id")
    private Integer memberId;

    /**
     * 会员名称
     */
    @SerializedName("member_name")
    private String memberName;

    /**
     * 发票类型
     */
    @SerializedName("receipt_type")
    private String receiptType;

    /**
     * 发票抬头
     */
    @SerializedName("receipt_title")
    private String receiptTitle;

    /**
     * 发票金额
     */
    @SerializedName("receipt_amount")
    private BigDecimal receiptAmount;

    /**
     * 发票内容
     */
    @SerializedName("receipt_content")
    private String receiptContent;

    /**
     * 税号
     */
    @SerializedName("tax_no")
    private String taxNo;

    /**
     * 注册地址
     */
    @SerializedName("reg_addr")
    private String regAddr;

    /**
     * 注册电话
     */
    @SerializedName("reg_tel")
    private String regTel;

    /**
     * 开户银行
     */
    @SerializedName("bank_name")
    private String bankName;

    /**
     * 银行账户
     */
    @SerializedName("bank_account")
    private String bankAccount;

    /**
     * 开票时间（秒时间戳）
     */
    @SerializedName("add_time")
    private Long addTime;

    /**
     * 发票下载地址
     */
    @SerializedName("receipt_link")
    private String receiptLink;

}
