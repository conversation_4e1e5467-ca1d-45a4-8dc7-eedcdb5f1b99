package com.xtc.marketing.adapterservice.rpc.jd;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.jd.jdjcqdto.JcqPullResultDTO;
import com.xtc.marketing.adapterservice.rpc.jd.jdjcqdto.JcqResponseDTO;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

/**
 * 京东消息订阅客户端
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class JcqClient {

    /**
     * 连接地址
     */
    public static final String CONNECT_URL = "https://jcq-shared-004.cn-north-1.jdcloud.com";
    /**
     * 用户accessKey
     */
    private static final String ACCESS_KEY = "JDC_B4739AAC938DA0765C61ADF4AC9C";
    /**
     * 用户secretKey
     */
    private static final String SECRET_KEY = "B0546A667B90813150464A0C3C79E9D2";
    /**
     * 初始延迟（毫秒）
     */
    private static final long INITIAL_DELAY = 200L;
    /**
     * 拉取间隔（毫秒）
     */
    private static final long PULL_INTERVAL = 2000L;
    /**
     * 一次最多拉取消息条数
     */
    private static final int PULL_SIZE = 10;
    /**
     * HMAC-SHA1算法名称
     */
    private static final String HMAC_SHA1_ALGORITHM = "HmacSHA1";
    /**
     * 请求头参数名称：accessKey
     */
    private static final String HEADER_ACCESS_KEY = "accessKey";
    /**
     * 请求头参数名称：signature
     */
    private static final String HEADER_SIGNATURE = "signature";
    /**
     * 请求头参数名称：dateTime
     */
    private static final String HEADER_DATE = "dateTime";
    /**
     * API版本
     */
    private static final String API_VERSION = "v2";

    /**
     * 消费组id
     */
    @Setter
    private String groupId;
    /**
     * 主题名称
     */
    @Setter
    private String topic;
    /**
     * 消息消费者
     */
    @Setter
    private Consumer<JcqPullResultDTO> messageConsumer;
    /**
     * 运行状态标识
     */
    private final AtomicBoolean running = new AtomicBoolean(false);
    /**
     * 拉取服务线程池
     */
    private final ScheduledExecutorService pullService =
            Executors.newSingleThreadScheduledExecutor(new JdPullThreadFactory("jd-pull-service"));
    /**
     * 拉取任务句柄
     */
    private ScheduledFuture<?> pullTaskFuture;

    /**
     * 生成京东消息订阅客户端
     *
     * @param groupId 消费组id
     * @param topic   主题名称
     */
    public JcqClient(String groupId, String topic) {
        this.groupId = groupId;
        this.topic = topic;
    }

    @PreDestroy
    public void destroy() {
        // 停止消息拉取任务
        try {
            this.stopPull();
        } catch (Exception ignore) {
        }
        // 关闭拉取服务线程池
        pullService.shutdown();
        try {
            if (!pullService.awaitTermination(5, TimeUnit.SECONDS)) {
                pullService.shutdownNow();
            }
        } catch (InterruptedException e) {
            pullService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 启动消息拉取
     */
    public void startPull() throws Exception {
        // 检查运行状态标识，如果不是 false 则说明拉取器已经在运行中
        if (!running.compareAndSet(false, true)) {
            log.info("京东消息拉取器已在运行中 - groupId: {}, topic: {}", groupId, topic);
            return;
        }
        try {
            // 启动定时拉取任务
            this.pullTaskFuture = pullService.scheduleAtFixedRate(
                    () -> {
                        try {
                            // 拉取器未启动，或者消息消费者未设置，则直接返回
                            if (!running.get() || messageConsumer == null) {
                                return;
                            }
                            // 拉取消息
                            this.pullMessage().ifPresent(pullResult -> messageConsumer.accept(pullResult));
                        } catch (Exception e) {
                            log.error("京东消息拉取任务执行异常", e);
                        }
                    },
                    INITIAL_DELAY,
                    PULL_INTERVAL,
                    TimeUnit.MILLISECONDS
            );
            log.info("京东消息拉取器启动成功 - groupId: {}, topic: {}, 拉取间隔: {}ms", groupId, topic, PULL_INTERVAL);
        } catch (Exception e) {
            log.error("京东消息拉取器启动失败 - groupId: {}, topic: {}, message: {}", groupId, topic, e.getMessage(), e);
            // 启动失败时重置运行状态标识
            running.set(false);
            throw e;
        }
    }

    /**
     * 停止消息拉取
     */
    public void stopPull() throws Exception {
        // 检查运行状态标识，如果不是 true 则说明拉取器已经停止或未启动
        if (!running.compareAndSet(true, false)) {
            log.info("京东消息拉取器已停止 - groupId: {}, topic: {}", groupId, topic);
            return;
        }
        try {
            if (this.pullTaskFuture != null && !this.pullTaskFuture.isDone()) {
                // 取消任务，false表示允许当前正在执行的任务完成
                this.pullTaskFuture.cancel(false);
                // 清理任务引用
                this.pullTaskFuture = null;
            }
            log.info("京东消息拉取器停止成功 - groupId: {}, topic: {}", groupId, topic);
        } catch (Exception e) {
            log.error("京东消息拉取器停止异常 - groupId: {}, topic: {}, message: {}", groupId, topic, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 检查京东消息订阅客户端在运行中
     *
     * @return 执行结果
     */
    public boolean isRunning() {
        return running.get() &&
                pullTaskFuture != null &&
                !pullTaskFuture.isDone() &&
                !pullTaskFuture.isCancelled();
    }

    /**
     * 拉取消息
     *
     * @return 消息拉取结果，无消息或失败时返回null
     */
    private Optional<JcqPullResultDTO> pullMessage() {
        // 生成用于签名的、排序后的参数字符串
        TreeMap<String, String> urlParams = Maps.newTreeMap();
        urlParams.put("topic", this.topic);
        urlParams.put("consumerGroupId", this.groupId);
        urlParams.put("ack", "true");
        urlParams.put("size", String.valueOf(PULL_SIZE));
        // 生成签名
        TreeMap<String, String> signParams = Maps.newTreeMap(urlParams);
        String nowUtc = Instant.now().atOffset(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
        signParams.put("accessKey", ACCESS_KEY);
        signParams.put("dateTime", nowUtc);
        String signature = this.sign(signParams);
        // 构建URL
        String urlParamsStr = Joiner.on("&").withKeyValueSeparator("=").join(urlParams);
        String url = String.format("%s/%s/messages?%s", CONNECT_URL, API_VERSION, urlParamsStr);
        // 构建请求头
        Map<String, String> headers = Maps.newHashMap();
        headers.put(HEADER_ACCESS_KEY, ACCESS_KEY);
        headers.put(HEADER_SIGNATURE, signature);
        headers.put(HEADER_DATE, nowUtc);
        // 发送请求
        String responseJson = HttpUtil.get(url, headers);
        if (StringUtils.isBlank(responseJson)) {
            return Optional.empty();
        }
        // 解析响应
        JcqResponseDTO response = GsonUtil.jsonToBean(responseJson, JcqResponseDTO.class);
        return Optional.ofNullable(response)
                .map(JcqResponseDTO::getResult)
                .filter(result -> CollectionUtils.isNotEmpty(result.getMessages()));
    }

    /**
     * 计算签名
     *
     * @param signParams 待签名数据
     * @return 签名字符串
     */
    private String sign(TreeMap<String, String> signParams) {
        String signText = Joiner.on("&").withKeyValueSeparator("=").join(signParams);
        try {
            SecretKeySpec signingKey = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), HMAC_SHA1_ALGORITHM);
            Mac mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(signText.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(rawHmac);
        } catch (Exception e) {
            log.error("计算签名失败 - signText: {}", signText, e);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, "京东消息计算签名失败", e);
        }
    }

    /**
     * 京东拉取线程工厂，用于设置守护线程
     */
    private static class JdPullThreadFactory implements ThreadFactory {

        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        JdPullThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix;
        }

        @Override
        public Thread newThread(@NotNull Runnable runnable) {
            Thread thread = new Thread(runnable, namePrefix + "-" + threadNumber.getAndIncrement());
            // 设置为守护线程
            thread.setDaemon(true);
            if (thread.getPriority() != Thread.NORM_PRIORITY) {
                thread.setPriority(Thread.NORM_PRIORITY);
            }
            return thread;
        }

    }

    public static void main(String[] args) throws Exception {
        JcqClient jcqClient = new JcqClient(
                "open_message_650723933947",
                "568091687201$Default$open_message_AFS_StepResult_JOS_A44020750E1C3532733F30F5BE74DD23"
        );

        // 设置消息消费者
        jcqClient.setMessageConsumer(pullResult -> {
            log.info("拉取到京东消息: {}", GsonUtil.objectToJson(pullResult));
            pullResult.getMessages().forEach(message -> {
                log.info("处理消息 - ID: {}, 内容: {}", message.getMessageId(), message.getMessageBody());
            });
        });

        // 启动消息拉取
        jcqClient.startPull();

        // 添加JVM关闭钩子，确保优雅关闭
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("正在关闭京东消息拉取器...");
            jcqClient.destroy();
        }));

        log.info("京东消息拉取器已启动");

        // 使用CountDownLatch替代无限循环，支持优雅关闭
        CountDownLatch latch = new CountDownLatch(1);
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.warn("主线程被中断，正在停止服务...");
            Thread.currentThread().interrupt();
        } finally {
            jcqClient.destroy();
        }
    }

}
