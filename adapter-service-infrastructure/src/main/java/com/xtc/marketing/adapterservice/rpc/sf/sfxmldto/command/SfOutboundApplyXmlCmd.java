package com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.command;

import com.beust.jcommander.internal.Lists;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.SfRequestXmlDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 顺丰仓库出库
 */
@Setter
@Getter
@ToString
@XStreamAlias("SaleOrder")
public class SfOutboundApplyXmlCmd extends SfRequestXmlDTO {

    /**
     * 仓库编码
     */
    @XStreamAlias("WarehouseCode")
    private String warehouseCode;
    /**
     * 店铺名称
     */
    @XStreamAlias("ShopName")
    private String shopName;
    /**
     * 出库单号（业务方单号）
     */
    @XStreamAlias("ErpOrder")
    private String orderId;
    /**
     * 交易平台（TM天猫，TB淘宝，JD京东，PDD拼多多，DY抖音）
     */
    @XStreamAlias("TradePlatform")
    private String tradePlatform;
    /**
     * 平台应用id
     */
    @XStreamAlias("EwaybillOrderAppid")
    private String ewaybillOrderAppid;
    /**
     * 收件人密文数据 微信视频专用
     */
    @XStreamAlias("EwaybillOrderCode")
    private String ewaybillOrderCode;
    /**
     * 平台交易号，比如淘宝交易号等等
     */
    @XStreamAlias("TradeOrder")
    private String tradeOrder;
    /**
     * 顺丰订单类型
     * 10  销售订单
     * 20  返厂订单
     * 30  换货订单
     * 40  调拨订单
     * 50  退仓订单
     * 90  NPR订单
     */
    @XStreamAlias("SfOrderType")
    private String sfOrderType = "销售订单";
    /**
     * 运单打印寄件方信息来源（当值为 10 时，寄件方信息必填 ）
     * 10  订单自带寄件方信息
     * 20  货主配置寄件人信息
     */
    @XStreamAlias("FromFlag")
    private String fromFlag;
    /**
     * 承运商
     */
    @XStreamAlias("OrderCarrier")
    private Carrier carrier;
    /**
     * 收件方
     */
    @XStreamAlias("OrderReceiverInfo")
    private Receiver receiver;
    /**
     * 寄件方
     */
    @XStreamAlias("OrderSenderInfo")
    private Sender sender;
    /**
     * 商品列表
     */
    @XStreamAlias("OrderItems")
    private List<Item> items;
    /**
     * 扩展数据
     */
    @XStreamAlias("OrderExtendAttribute")
    private OrderExtendAttribute extendData;

    @Setter
    @Getter
    @ToString
    public static class Carrier {

        /**
         * 承运商
         */
        @XStreamAlias("Carrier")
        private String carrierName = "顺丰速运";
        /**
         * 承运商产品
         */
        @XStreamAlias("CarrierProduct")
        private String carrierProduct;
        /**
         * 国补订单微派编码
         */
        @XStreamAlias("CarrierAddedServices")
        private List<CarrierAddedServices> carrierAddedServices;

    }

    @Setter
    @Getter
    @ToString
    public static class Receiver {

        /**
         * 收件方公司
         */
        @XStreamAlias("ReceiverCompany")
        private String receiverCompany;
        /**
         * 收件方名称
         */
        @XStreamAlias("ReceiverName")
        private String receiverName;
        /**
         * 收件方邮编
         */
        @XStreamAlias("ReceiverZipCode")
        private String receiverZipCode;
        /**
         * 收件方手机号
         */
        @XStreamAlias("ReceiverMobile")
        private String receiverMobile;
        /**
         * 收件方电话
         */
        @XStreamAlias("ReceiverPhone")
        private String receiverPhone;
        /**
         * 收件方国家
         */
        @XStreamAlias("ReceiverCountry")
        private String receiverCountry = "中国";
        /**
         * 收件方省份
         */
        @XStreamAlias("ReceiverProvince")
        private String receiverProvince;
        /**
         * 收件方城市
         */
        @XStreamAlias("ReceiverCity")
        private String receiverCity;
        /**
         * 收件方区县
         */
        @XStreamAlias("ReceiverArea")
        private String receiverDistrict;
        /**
         * 收件方地址
         */
        @XStreamAlias("ReceiverAddress")
        private String receiverAddress;

    }

    @Setter
    @Getter
    @ToString
    public static class Sender {

        /**
         * 寄件方公司
         */
        @XStreamAlias("SenderCompany")
        private String senderCompany;
        /**
         * 寄件方名称
         */
        @XStreamAlias("SenderName")
        private String senderName;
        /**
         * 寄件方邮编
         */
        @XStreamAlias("SenderZipCode")
        private String senderZipCode;
        /**
         * 寄件方手机
         */
        @XStreamAlias("SenderMobile")
        private String senderMobile;
        /**
         * 寄件方电话
         */
        @XStreamAlias("SenderPhone")
        private String senderPhone;
        /**
         * 寄件方国家
         */
        @XStreamAlias("SenderCountry")
        private String senderCountry = "中国";
        /**
         * 寄件方省份
         */
        @XStreamAlias("SenderProvince")
        private String senderProvince;
        /**
         * 寄件方城市
         */
        @XStreamAlias("SenderCity")
        private String senderCity;
        /**
         * 寄件方区县
         */
        @XStreamAlias("SenderArea")
        private String senderDistrict;
        /**
         * 寄件方地址
         */
        @XStreamAlias("SenderAddress")
        private String senderAddress;

    }

    @Setter
    @Getter
    @ToString
    @XStreamAlias("OrderItem")
    public static class Item {

        /**
         * skuId
         */
        @XStreamAlias("SkuNo")
        private String skuId;
        /**
         * 数量
         */
        @XStreamAlias("ItemQuantity")
        private String quantity;
        /**
         * 库存类型
         */
        @XStreamAlias("InventoryStatus")
        private String inventoryStatus;

    }

    @Setter
    @Getter
    @ToString
    @XStreamAlias("CarrierAddedService")
    public static class CarrierAddedServices {

        /**
         * 服务编码
         */
        @XStreamAlias("ServiceCode")
        private String serviceCode = "WEIPAI";
        /**
         * 微派编码
         */
        @XStreamAlias("Attr02")
        private String attr02;

    }

    @Setter
    @Getter
    @ToString
    public static class OrderExtendAttribute {

        /**
         * 扩展字段30
         */
        @XStreamAlias("UserDef30")
        private String userDef30;

    }

    @Override
    public String toRequestXml(String requestBodyXml, String companyCode) {
        return "<SaleOrderRequest>"
                + "<CompanyCode>"
                + companyCode
                + "</CompanyCode>"
                + "<SaleOrders>"
                + requestBodyXml
                + "</SaleOrders>"
                + "</SaleOrderRequest>";
    }

    private static final List<String> RESPONSE_PARSE_ELEMENTS =
            Lists.newArrayList("SaleOrderResponse", "SaleOrders", "SaleOrder");

    @Override
    public List<String> getResponseParseElements() {
        return RESPONSE_PARSE_ELEMENTS;
    }

}
