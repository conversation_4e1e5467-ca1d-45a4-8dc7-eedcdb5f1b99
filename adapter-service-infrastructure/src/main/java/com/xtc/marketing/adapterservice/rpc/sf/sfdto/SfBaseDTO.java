package com.xtc.marketing.adapterservice.rpc.sf.sfdto;

import com.google.gson.JsonElement;
import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 顺丰物流响应基类
 */
@Getter
@Setter
@ToString
public class SfBaseDTO {

    /**
     * 请求ID
     */
    private String requestId;
    /**
     * 成功标识
     */
    private Boolean success;
    /**
     * 错误码
     */
    private String errorCode;
    /**
     * 错误信息
     */
    @SerializedName(value = "errorMsg", alternate = "errorMessage")
    private String errorMsg;
    /**
     * 响应数据
     */
    @SerializedName(value = "msgData", alternate = "obj")
    private JsonElement msgData;

    /**
     * 判断请求失败
     *
     * @return 执行结果
     */
    public boolean isFailure() {
        return success == null || !success;
    }

}
