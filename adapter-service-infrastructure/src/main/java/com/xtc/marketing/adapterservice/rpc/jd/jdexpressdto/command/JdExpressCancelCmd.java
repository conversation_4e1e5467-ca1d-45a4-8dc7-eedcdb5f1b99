package com.xtc.marketing.adapterservice.rpc.jd.jdexpressdto.command;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 京东快递取消
 */
@Getter
@Setter
@ToString
public class JdExpressCancelCmd {

    /**
     * 快递场景
     * <p> 0-c2c 1-b2c 2-c2b 4-kyb2c 5- kyc2c <p/>
     * <p>详细说明：<a href="https://cloud.jdl.com/#/open-business-document/access-guide/267/54152">下单来源说明</a><p/>
     */
    private Integer waybillScene;
    /**
     * 取消原因编码 1-用户发起取消；2-超时未支付
     */
    private String cancelReasonCode;
    /**
     * 取消类型 0：仅取消；1：优先取消，取消失败后拦截；2：仅发起拦截
     */
    private Integer cancelType;

}
