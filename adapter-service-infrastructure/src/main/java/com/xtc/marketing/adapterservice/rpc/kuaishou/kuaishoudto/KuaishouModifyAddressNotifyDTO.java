package com.xtc.marketing.adapterservice.rpc.kuaishou.kuaishoudto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 快手修改地址通知消息体
 */
@Getter
@Setter
@ToString
public class KuaishouModifyAddressNotifyDTO {

    /**
     * 订单ID
     */
    private Long oid;
    /**
     * 买家openId
     */
    private String buyerOpenId;
    /**
     * 提交变更时间
     */
    private Long createTime;
    /**
     * 审核状态，10-审批中、20-成功、30-拒绝
     */
    private Integer auditStatus;
    /**
     * 审核时间
     */
    private Long auditTime;

    public String getOrderId() {
        return oid != null ? oid.toString() : null;
    }

}
