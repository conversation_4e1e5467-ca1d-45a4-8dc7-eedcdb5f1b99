package com.xtc.marketing.adapterservice.rpc.sf.sfdto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SfCreateOrderCmd {

    /**
     * 响应报文的语言
     */
    private final String language = "zh-CN";
    /**
     * 是否返回路由标签，默认1（1：返回路由标签，0：不返回，除部分特殊用户外，其余用户都默认返回）
     */
    private final Integer isReturnRoutelabel = 1;
    /**
     * 要求上门取件开始时间， 格式： YYYY-MM-DD HH24:MM:SS， 示例： 2012-7-30 09:30:00 （预约单传预约截止时间，不赋值默认按当前时间下发，1小时内取件）
     */
    private String sendStartTm;
    /**
     * 是否通过手持终端通知顺丰收派员上门收件，支持以下值，1：要求 0：不要求 (默认0) *电商退货固定传1
     */
    private Integer isDocall;
    /**
     * 客户订单号，重复使用订单号时返回第一次下单成功时的运单信息
     */
    private String orderId;
    /**
     * 快件产品类别（1：顺丰特快，2：顺丰标快）
     * <p><a href="https://open.sf-express.com/developSupport/734349?activeIndex=324604">文档</a>
     */
    private Integer expressTypeId;
    /**
     * 顺丰月结卡号
     */
    private String monthlyCard;
    /**
     * 收寄双方信息
     */
    private List<SfContactCmd> contactInfoList;
    /**
     * 托寄物
     */
    private List<SfCargoCmd> cargoDetails;
    /**
     * 扩展属性
     */
    private Object extraInfoList;

    /**
     * 校验参数非法
     *
     * @return 执行结果
     */
    public boolean checkIllegal() {
        if (ObjectUtils.anyNull(orderId, contactInfoList, cargoDetails)) {
            return true;
        }

        // 校验收寄双方信息是否都已提交
        if (contactInfoList.size() != 2) {
            return true;
        }
        boolean hasSender = contactInfoList.stream().anyMatch(contact -> contact.getContactType() == 1);
        boolean hasReceiver = contactInfoList.stream().anyMatch(contact -> contact.getContactType() == 2);
        if (!hasSender || !hasReceiver) {
            return true;
        }

        return cargoDetails.isEmpty();
    }

}
