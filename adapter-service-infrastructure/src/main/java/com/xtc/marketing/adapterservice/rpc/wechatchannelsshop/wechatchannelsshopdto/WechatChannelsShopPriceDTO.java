package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 微信视频号小店价格
 */
@Getter
@Setter
@ToString
public class WechatChannelsShopPriceDTO {

    /**
     * 订单金额，单位为分，order_price=original_order_price-discounted_price-deduction_price-change_down_price
     */
    @SerializedName("order_price")
    private Integer payment;
    /**
     * 运费，单位为分
     */
    @SerializedName("freight")
    private Integer shippingPayment;
    /**
     * 优惠券优惠金额，单位为分
     */
    @SerializedName("discounted_price")
    private Integer discount;
    /**
     * 订单原始价格，单位为分，original_order_price=product_price+freight
     */
    @SerializedName("original_order_price")
    private Integer priceTotal;

}
