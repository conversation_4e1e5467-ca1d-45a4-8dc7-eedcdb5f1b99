package com.xtc.marketing.adapterservice.config;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 所有的 Repository 都继承此类，提供公共基础方法增强 ServiceImpl
 * <p>例：public class UserRepository extends BaseRepository<UserMapper, UserDO></p>
 *
 * @param <M> BaseMapper 对象
 * @param <T> 实体对象
 */
public class BaseRepository<M extends BaseMapper<T>, T extends BaseDO> extends ServiceImpl<M, T> {

    /**
     * 限制一条数据
     */
    public static final String LIMIT_ONE = " limit 1 ";

    /**
     * 列表数据默认限制数量
     */
    public static final String DEFAULT_LIST_SIZE_LIMIT = " limit 200 ";

    /**
     * 查询单条数据
     *
     * @param column 列
     * @param val    值
     * @return 单条数据
     */
    public Optional<T> getBy(SFunction<T, Object> column, Object val) {
        if (val == null) {
            return Optional.empty();
        }
        Wrapper<T> wrapper = Wrappers.<T>lambdaQuery()
                .eq(column, val)
                .orderByDesc(T::getId)
                .last(LIMIT_ONE);
        T one = this.getOne(wrapper);
        return Optional.ofNullable(one);
    }

    /**
     * 查询数据列表
     *
     * @param column 列
     * @param val    值
     * @return 数据列表
     */
    public List<T> listBy(SFunction<T, Object> column, Object val) {
        if (val == null) {
            return Collections.emptyList();
        }
        Wrapper<T> wrapper = Wrappers.<T>lambdaQuery()
                .eq(column, val)
                .orderByDesc(T::getId)
                .last(DEFAULT_LIST_SIZE_LIMIT);
        return this.list(wrapper);
    }

}
