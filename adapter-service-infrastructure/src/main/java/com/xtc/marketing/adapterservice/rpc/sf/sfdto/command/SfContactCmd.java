package com.xtc.marketing.adapterservice.rpc.sf.sfdto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SfContactCmd {

    /**
     * 国家或地区代码（例如：内地件CN）
     */
    private final String country = "CN";

    /**
     * 地址类型（1，寄件方 2，到件方）
     */
    private Integer contactType;

    /**
     * 公司名称
     */
    private String company;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 联系电话
     */
    private String tel;

    /**
     * 所在省级行政区名称，必须是标准的省级行政区名称如：北京、广东省、广西壮族自治区等。
     * 此字段影响原寄地代码识别，建议尽可能传该字段的值。
     */
    private String province;

    /**
     * 所在地级行政区名称，必须是标准的城市称谓，如：北京市、 深圳市、大理白族自治州等。
     * 此字段影响原寄地代码识别，建议尽可能传该字段的值。
     */
    private String city;

    /**
     * 所在县/区级行政区名称，必须是标准的县/区称谓，如：福田区，南涧彝族自治县、准格尔旗等。
     */
    private String county;

    /**
     * 详细地址
     */
    private String address;

}
