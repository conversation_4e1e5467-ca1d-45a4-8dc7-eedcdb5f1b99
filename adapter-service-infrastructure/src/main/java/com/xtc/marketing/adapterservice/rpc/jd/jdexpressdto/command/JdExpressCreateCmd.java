package com.xtc.marketing.adapterservice.rpc.jd.jdexpressdto.command;

import com.google.gson.annotations.SerializedName;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCreateOrderV1.CommonProductInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 京东快递下单
 */
@Getter
@Setter
@ToString
public class JdExpressCreateCmd {

    /**
     * 快递场景
     * <p> 0-c2c 1-b2c 2-c2b 4-kyb2c 5- kyc2c <p/>
     * <p>详细说明：<a href="https://cloud.jdl.com/#/open-business-document/access-guide/267/54152">下单来源说明</a><p/>
     */
    private Integer waybillScene;
    /**
     * 付款方式
     * <p>1-寄付 2-到付 3-月结 5-多方收费<p/>
     */
    private Integer paymentType;
    /**
     * 详细付款方式-C2B场景必填
     * <p>枚举值：1-寄付现结 ；3-月结<p/>
     */
    private Integer paymentDetailType;
    /**
     * 增值服务
     */
    @SerializedName("addedProducts")
    private List<CommonProductInfo> extraProducts;
    /**
     * 取件开始时间
     */
    private LocalDateTime pickupStartTime;
    /**
     * 取件结束时间
     */
    private LocalDateTime pickupEndTime;

}
