package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class WechatChannelsShopOrderItemDetailDTO {

    /**
     * 商品信息
     */
    @SerializedName("product_infos")
    private List<WechatChannelsShopProductDTO> products;
    /**
     * 价格信息
     */
    @SerializedName("price_info")
    private WechatChannelsShopPriceDTO price;
    /**
     * 支付信息
     */
    @SerializedName("pay_info")
    private PayDTO pay;
    /**
     * 配送信息
     */
    @SerializedName("delivery_info")
    private ShippingDTO shipping;
    /**
     * 额外信息
     */
    @SerializedName("ext_info")
    private ExtDTO ext;
    /**
     * 授权账号信息
     */
    @SerializedName("agent_info")
    private AgentDTO agent;
    /**
     * 授权账号信息
     */
    @SerializedName("source_infos")
    private List<SourceInfoDTO> sourceInfoDTO;

    /**
     * 授权账号
     */
    @Getter
    @Setter
    @ToString
    public static class AgentDTO {

        /**
         * 授权视频号id
         */
        @SerializedName("agent_finder_id")
        private String sellerId;
        /**
         * 授权视频号昵称
         */
        @SerializedName("agent_finder_nickname")
        private String sellerName;

    }

    /**
     * 额外信息
     */
    @Getter
    @Setter
    @ToString
    public static class ExtDTO {

        /**
         * 用户备注
         */
        @SerializedName("customer_notes")
        private String userMemo;
        /**
         * 商家备注
         */
        @SerializedName("merchant_notes")
        private String sellerMemo;

    }

    /**
     * 支付信息
     */
    @Getter
    @Setter
    @ToString
    public static class PayDTO {

        /**
         * 支付时间，秒级时间戳, 先用后付订单(payment_method=2)本字段的值为用户确认先用后
         */
        @SerializedName("pay_time")
        private Long payTime;

    }

    /**
     * 发货信息
     */
    @Getter
    @Setter
    @ToString
    public static class ShippingDTO {

        /**
         * 地址信息，具体结构请参考AddressInfo结构体
         */
        @SerializedName("address_info")
        private WechatChannelsShopAddressDTO address;
        /**
         * 发货物流信息，具体结构请参考DeliveryProductInfo结构体
         */
        @SerializedName("delivery_product_info")
        private List<LogisticsDTO> logistics;
        /**
         * 密文
         */
        @SerializedName("ewaybill_order_code")
        private String waybillOrderCode;

    }

    /**
     * 物流信息
     */
    @Getter
    @Setter
    @ToString
    public static class LogisticsDTO {

        /**
         * 快递单号
         */
        @SerializedName("waybill_id")
        private String waybillId;
        /**
         * 快递公司编码
         */
        @SerializedName("delivery_id")
        private String deliveryId;
        /**
         * 配送方式
         */
        @SerializedName("deliver_type")
        private String deliverType;
        /**
         * 快递公司名称
         */
        @SerializedName("delivery_name")
        private String deliveryName;
        /**
         * 商品信息
         */
        @SerializedName("product_infos")
        private List<DeliveryProductInfoDTO> productInfos;
        /**
         * 发货时间
         */
        @SerializedName("delivery_time")
        private Long shippingTime;

    }

    /**
     * 包裹中的商品信息
     */
    @Getter
    @Setter
    @ToString
    public static class DeliveryProductInfoDTO {

        /**
         * 商品id
         */
        @SerializedName("product_id")
        private String productId;
        /**
         * 商品sku
         */
        @SerializedName("sku_id")
        private String skuId;
        /**
         * 商品数量
         */
        @SerializedName("product_cnt")
        private Integer productNum;

    }

    /**
     * 订单成交来源信息
     */
    @Getter
    @Setter
    @ToString
    public static class SourceInfoDTO {

        /**
         * 商品id
         */
        @SerializedName("sku_id")
        private String skuId;
        /**
         * 账号关联类型
         */
        @SerializedName("sale_channel")
        private Integer saleChannel;
        /**
         * 带货账号类型
         */
        @SerializedName("account_type")
        private Integer accountType;
        /**
         * 带货账号id
         */
        @SerializedName("account_id")
        private String accountId;
        /**
         * 带货账号昵称
         */
        @SerializedName("account_nickname")
        private String accountNickname;
        /**
         * 带货内容类型
         */
        @SerializedName("content_type")
        private Integer contentType;
        /**
         * 带货内容id
         */
        @SerializedName("content_id")
        private String contentId;
        /**
         * 自营推客推广的带货机构id
         */
        @SerializedName("promoter_head_supplier_id")
        private String promoterHeadSupplierId;

    }

}
