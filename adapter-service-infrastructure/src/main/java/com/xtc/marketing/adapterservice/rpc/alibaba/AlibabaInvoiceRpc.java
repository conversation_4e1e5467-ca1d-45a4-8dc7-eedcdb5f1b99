package com.xtc.marketing.adapterservice.rpc.alibaba;

import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.TaobaoRequest;
import com.taobao.api.TaobaoResponse;
import com.taobao.api.request.AlibabaEinvoiceCreateResultGetRequest;
import com.taobao.api.request.AlibabaEinvoiceCreatereqRequest;
import com.taobao.api.request.AlibabaEinvoiceRedCreatereqRequest;
import com.taobao.api.request.AlibabaEinvoiceSerialnoGenerateRequest;
import com.taobao.api.response.AlibabaEinvoiceCreateResultGetResponse;
import com.taobao.api.response.AlibabaEinvoiceCreatereqResponse;
import com.taobao.api.response.AlibabaEinvoiceRedCreatereqResponse;
import com.taobao.api.response.AlibabaEinvoiceSerialnoGenerateResponse;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.rmi.RemoteException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 阿里巴巴发票RPC
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class AlibabaInvoiceRpc {

    /**
     * 缓存 client 对象
     */
    private static final Map<String, TaobaoClient> CLIENT_CACHE = new ConcurrentHashMap<>();

    /**
     * 开票
     *
     * @param shop    店铺
     * @param request 参数
     * @return 执行结果
     */
    public boolean createBlueInvoice(ShopDO shop, AlibabaEinvoiceCreatereqRequest request) {
        AlibabaEinvoiceCreatereqResponse response = this.call(shop, request);
        return response.getIsSuccess();
    }

    /**
     * 冲红
     *
     * @param shop    店铺
     * @param request 请求参数
     * @return 执行结果
     */
    public String createRedInvoice(ShopDO shop, AlibabaEinvoiceRedCreatereqRequest request) {
        AlibabaEinvoiceRedCreatereqResponse response = this.call(shop, request);
        return response.getIsSuccess();
    }

    /**
     * 查询开票结果
     *
     * @param shop    店铺
     * @param request 请求参数
     * @return 开票结果
     */
    public List<AlibabaEinvoiceCreateResultGetResponse.InvoiceResult> listInvoiceResult(ShopDO shop, AlibabaEinvoiceCreateResultGetRequest request) {
        AlibabaEinvoiceCreateResultGetResponse response = this.call(shop, request);
        return response.getInvoiceResultList();
    }

    /**
     * 生成开票流水号
     *
     * @param shop 店铺
     * @return 开票流水号
     */
    public String createSerialNo(ShopDO shop) {
        AlibabaEinvoiceSerialnoGenerateRequest request = new AlibabaEinvoiceSerialnoGenerateRequest();
        AlibabaEinvoiceSerialnoGenerateResponse response = this.call(shop, request);
        return response.getSerialNo();
    }

    /**
     * 执行远程调用
     *
     * @param shop    店铺
     * @param request 请求参数
     * @param <T>     返回值类型
     * @return 返回值
     */
    private <T extends TaobaoResponse> T call(ShopDO shop, TaobaoRequest<T> request) {
        log.info("阿里巴巴发票参数: {}", GsonUtil.objectToJson(request));
        String responseStr = "";
        try {
            TaobaoClient client = this.getClient(shop);
            T response = client.execute(request, shop.getAppSessionKey());

            responseStr = GsonUtil.objectToJson(response);
            log.info("阿里巴巴发票返回值: {}", responseStr);

            if (!response.isSuccess()) {
                throw new RemoteException("RPC返回异常状态码");
            }
            return response;
        } catch (Exception e) {
            String msg = String.format("阿里巴巴发票调用异常 response: %s", responseStr);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
        }
    }

    /**
     * 获取请求客户端
     *
     * @param shop 店铺
     * @return 客户端
     */
    private TaobaoClient getClient(ShopDO shop) {
        String key = shop.getShopCode();
        if (CLIENT_CACHE.get(key) == null) {
            TaobaoClient client = new DefaultTaobaoClient(shop.getApiUrl(), shop.getAppKey(), shop.getAppSecret());
            CLIENT_CACHE.put(key, client);
        }
        return CLIENT_CACHE.get(key);
    }

}
