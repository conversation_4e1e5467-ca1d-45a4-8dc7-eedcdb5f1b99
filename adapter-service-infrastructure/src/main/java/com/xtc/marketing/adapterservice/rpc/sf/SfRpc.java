package com.xtc.marketing.adapterservice.rpc.sf;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.*;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.command.SfCreateOrderCmd;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.command.SfInterceptCmd;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpServerErrorException;

import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * 顺丰物流RPC
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class SfRpc {

    /**
     * 生产环境地址
     */
    private static final String DOMAIN = "https://bspgw.sf-express.com/std/service";

    /**
     * 面单云打印模板代码
     */
    private static final String CLOUD_PRINT_TEMPLATE_CODE = "fm_76130_standard_YOGIPBEI";
    /**
     * 快件产品类别（1：顺丰特快，2：顺丰标快）
     * <p><a href="https://open.sf-express.com/developSupport/734349?activeIndex=324604">文档</a>
     */
    public static final String EXPRESS_TYPE_ID_STANDARD = "2";

    /**
     * API：查询快递路由
     */
    private static final String API_SEARCH_ROUTES = "EXP_RECE_SEARCH_ROUTES";
    /**
     * API：查询订单详情
     */
    private static final String API_SEARCH_ORDER = "EXP_RECE_SEARCH_ORDER_RESP";
    /**
     * API：下单，生成运单号
     */
    private static final String API_CREATE_ORDER = "EXP_RECE_CREATE_ORDER";
    /**
     * API：修改订单（确认/取消）
     */
    private static final String API_UPDATE_ORDER = "EXP_RECE_UPDATE_ORDER";
    /**
     * API：物流拦截
     */
    private static final String API_INTERCEPT = "EXP_RECE_WANTED_INTERCEPT";
    /**
     * API：面单云打印 - 菜鸟控件
     */
    private static final String API_CLOUD_PRINT_CAINIAO = "COM_RECE_CLOUD_PRINT_CAINIAO";
    /**
     * API：推送国补通知
     */
    private static final String API_PUSH_COUNTRY_SUBSIDY = "EXP_PUSH_COUNTRY_SUBSIDY_INFO";

    /**
     * 查询快递路由
     * <p><a href="https://open.sf-express.com/Api/ApiDetails?level3=397&interName=%E8%B7%AF%E7%94%B1%E6%9F%A5%E8%AF%A2%E6%8E%A5%E5%8F%A3-EXP_RECE_SEARCH_ROUTES">路由查询接口</a></p>
     * <p><a href="https://open.sf-express.com/developSupport/734349?activeIndex=589678">路由操作码</a></p>
     *
     * @param account   物流账号
     * @param waybillNo 运单号
     * @return 快递路由
     */
    public List<SfRouteDTO> searchRoutes(LogisticsAccountDO account, String waybillNo) {
        if (waybillNo == null) {
            throw illegalArgumentException();
        }

        Map<String, Object> qry = Maps.newHashMapWithExpectedSize(2);
        qry.put("trackingType", 1);
        qry.put("trackingNumber", Lists.newArrayList(waybillNo));
        SfBaseDTO sfBaseDTO = this.call(account, API_SEARCH_ROUTES, qry, SfBaseDTO.class);

        String routesJson = sfBaseDTO.getMsgData().getAsJsonObject()
                .get("routeResps").getAsJsonArray()
                .get(0).getAsJsonObject()
                .get("routes").toString();
        return GsonUtil.jsonToList(routesJson, SfRouteDTO.class);
    }

    /**
     * 查询运单号
     *
     * @param account 物流账号
     * @param orderId 订单号
     * @return 运单号
     */
    public String getWaybillNoByOrderId(LogisticsAccountDO account, String orderId) {
        SfBaseDTO sfBaseDTO = this.searchOrder(account, orderId);
        return sfBaseDTO.getMsgData().getAsJsonObject()
                .get("waybillNoInfoList").getAsJsonArray()
                .get(0).getAsJsonObject()
                .get("waybillNo").getAsString();
    }

    /**
     * 查询订单详情
     *
     * @param account 物流账号
     * @param orderId 订单号
     * @return 订单列表
     */
    public SfBaseDTO searchOrder(LogisticsAccountDO account, String orderId) {
        if (orderId == null) {
            throw illegalArgumentException();
        }
        Map<String, Object> qry = Maps.newHashMapWithExpectedSize(1);
        qry.put("orderId", orderId);
        return this.call(account, API_SEARCH_ORDER, qry, SfBaseDTO.class);
    }

    /**
     * 下单，生成运单号
     *
     * @param account 物流账号
     * @param cmd     参数
     * @return 运单号
     */
    public SfOrderDTO createOrder(LogisticsAccountDO account, SfCreateOrderCmd cmd) {
        if (cmd.checkIllegal()) {
            throw illegalArgumentException();
        }
        return this.call(account, API_CREATE_ORDER, cmd, SfOrderDTO.class);
    }

    /**
     * 取消订单
     *
     * @param account 物流账号
     * @param orderId 订单号
     */
    public void cancelOrder(LogisticsAccountDO account, String orderId) {
        if (orderId == null) {
            throw illegalArgumentException();
        }

        Map<String, Object> cmd = Maps.newHashMapWithExpectedSize(2);
        cmd.put("orderId", orderId);
        cmd.put("dealType", 2);
        SfBaseDTO response = this.call(account, API_UPDATE_ORDER, cmd, SfBaseDTO.class);

        // 判断执行结果
        int status = response.getMsgData().getAsJsonObject().get("resStatus").getAsInt();
        if (status != 2) {
            String msg = String.format("取消订单失败，客户订单号与顺丰运单不匹配 response: %s", GsonUtil.objectToJson(response));
            throw SysException.of(SysErrorCode.S_RPC_ERROR, msg);
        }
    }

    /**
     * 物流拦截
     *
     * @param account 物流账号
     * @param cmd     参数
     */
    public void intercept(LogisticsAccountDO account, SfInterceptCmd cmd) {
        SfBaseDTO response = this.call(account, API_INTERCEPT, cmd, SfBaseDTO.class);
        boolean success = response.getMsgData().getAsJsonObject().get("success").getAsBoolean();
        if (BooleanUtils.isFalse(success)) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, response.getMsgData().toString());
        }
    }

    /**
     * 面单云打印
     *
     * @param account   物流账号
     * @param wayBillNo 运单号
     * @return 模板地址
     */
    public String cloudPrint(LogisticsAccountDO account, String wayBillNo) {
        if (wayBillNo == null) {
            throw illegalArgumentException();
        }

        Map<String, Object> requestBody = Maps.newHashMapWithExpectedSize(4);
        requestBody.put("version", "2.0");
        requestBody.put("sync", true);
        requestBody.put("templateCode", CLOUD_PRINT_TEMPLATE_CODE);
        requestBody.put("documents", Collections.singletonList(Collections.singletonMap("masterWaybillNo", wayBillNo)));
        SfCloudPrintDTO response = this.call(account, API_CLOUD_PRINT_CAINIAO, requestBody, SfCloudPrintDTO.class);

        return Optional.ofNullable(response)
                .map(SfCloudPrintDTO::getFiles)
                .filter(CollectionUtils::isNotEmpty)
                .map(files -> files.get(0))
                .map(SfCloudPrintDTO.PrintFile::getContents)
                .filter(CollectionUtils::isNotEmpty)
                .map(contents -> contents.get(0))
                .map(SfCloudPrintDTO.PrintTemplate::getTemplateURL)
                .orElse(null);
    }

    /**
     * 验证国补通知
     *
     * @param account    物流账号
     * @param notifyData 通知数据
     * @return 验证结果
     * @throws Exception 异常
     */
    public boolean verifyCountrySubsidyNotify(LogisticsAccountDO account, JsonObject notifyData) throws Exception {
        return this.verifyNotify(account, API_PUSH_COUNTRY_SUBSIDY, notifyData);
    }

    /**
     * 验证通知
     *
     * @param account     物流账号
     * @param serviceCode 接口代码
     * @param notifyData  通知数据
     * @return 验证结果
     */
    private boolean verifyNotify(LogisticsAccountDO account, String serviceCode, JsonObject notifyData) throws Exception {
        // 解析请求体
        String bodyServiceCode = GsonUtil.getAsString(notifyData, "serviceCode");
        if (!serviceCode.equals(bodyServiceCode)) {
            return false;
        }
        String timestamp = GsonUtil.getAsString(notifyData, "timestamp");
        String msgDigest = GsonUtil.getAsString(notifyData, "msgDigest");
        String msgData = GsonUtil.getAsString(notifyData, "msgData");
        // 验证签名
        String verifyMsgDigest = this.buildMsgDigest(msgData, Long.parseLong(timestamp), account.getClientSecret());
        return verifyMsgDigest.equals(msgDigest);
    }

    /**
     * 接口调用
     *
     * @param account       物流账号
     * @param serviceCode   接口代码
     * @param request       请求参数
     * @param responseClass 响应类型
     * @param <T>           继承 SfBaseDTO 的类型
     * @return 返回值
     */
    private <T extends SfBaseDTO> T call(LogisticsAccountDO account, String serviceCode, Object request, Class<T> responseClass) {
        // 构建请求参数封装
        MultiValueMap<String, Object> body = this.buildBody(account, serviceCode, request);

        // 发送请求
        String response = "";
        SfResponseBaseDTO responseBaseDTO;
        try {
            response = HttpUtil.postForForm(account.getApiUrl(), body);
            responseBaseDTO = GsonUtil.jsonToBean(response, SfResponseBaseDTO.class);
            if (responseBaseDTO.isFailure()) {
                throw new RemoteException("RPC返回异常状态码");
            }
        } catch (Exception e) {
            if (e instanceof HttpServerErrorException) {
                response = ((HttpServerErrorException) e).getResponseBodyAsString();
            }
            String msg = String.format("顺丰物流调用异常 response: %s", response);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, response, msg, e);
        }

        // 解析响应结果
        return this.parseResponse(responseBaseDTO.getApiResultData(), responseClass);
    }

    /**
     * 解析响应结果
     *
     * @param apiResultData 响应结果
     * @param responseClass 响应类型
     * @param <T>           继承 SfBaseDTO 的类型
     * @return 返回值
     */
    private <T extends SfBaseDTO> T parseResponse(String apiResultData, Class<T> responseClass) {
        try {
            // 解析业务返回值
            SfBaseDTO baseDTO = GsonUtil.jsonToBean(apiResultData, SfBaseDTO.class);
            if (baseDTO.isFailure()) {
                throw new RemoteException("RPC返回异常业务状态");
            }
            String data = baseDTO.getMsgData().toString();
            boolean jsonToBean = responseClass != SfBaseDTO.class && baseDTO.getMsgData().isJsonObject();
            T resultDTO = jsonToBean ? GsonUtil.jsonToBean(data, responseClass) : responseClass.newInstance();
            BeanUtils.copyProperties(baseDTO, resultDTO);
            return resultDTO;
        } catch (Exception e) {
            String msg = String.format("顺丰物流响应结果解析异常 apiResultData: %s", apiResultData);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, apiResultData, msg, e);
        }
    }

    /**
     * 构建请求参数封装
     *
     * @param account     物流账号
     * @param serviceCode 接口代码
     * @param request     请求参数
     * @return 请求参数封装
     */
    private MultiValueMap<String, Object> buildBody(LogisticsAccountDO account, String serviceCode, Object request) {
        try {
            long timestamp = System.currentTimeMillis();
            String msgData = GsonUtil.objectToJson(request);
            String msgDigest = this.buildMsgDigest(msgData, timestamp, account.getClientSecret());

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("partnerID", account.getClientCode());
            body.add("requestID", UUID.randomUUID().toString().replace("-", ""));
            body.add("serviceCode", serviceCode);
            body.add("timestamp", timestamp);
            body.add("msgDigest", msgDigest);
            body.add("msgData", msgData);
            return body;
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, "顺丰物流调用异常，组装参数异常", e);
        }
    }

    /**
     * 生成签名
     *
     * @param msgData      业务数据
     * @param timestamp    时间戳
     * @param clientSecret 客户密钥
     * @return 签名
     */
    private String buildMsgDigest(String msgData, long timestamp, String clientSecret)
            throws NoSuchAlgorithmException {
        String plantText = msgData + timestamp + clientSecret;
        String urlEncode = HttpUtil.urlEncode(plantText);
        byte[] md5 = this.md5Encode(urlEncode);
        return HttpUtil.base64Encode(md5);
    }

    /**
     * md5加密
     *
     * @param encryptStr 加密字符串
     * @return 密文字节
     */
    private byte[] md5Encode(String encryptStr) throws NoSuchAlgorithmException {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        md5.update(encryptStr.getBytes(StandardCharsets.UTF_8));
        return md5.digest();
    }

    /**
     * 参数不合法异常
     *
     * @return 异常
     */
    private SysException illegalArgumentException() {
        return SysException.of(SysErrorCode.S_RPC_ERROR, "参数不合法");
    }

    public static void main(String[] args) {
        SfRpc rpc = new SfRpc();
        LogisticsAccountDO account = LogisticsAccountDO.builder()
                .bizAccount("xxxxx")
                .clientCode("xxxxx")
                .clientSecret("xxxxx")
                .build();
        // rpc.searchRoutes(account, "xxxxx");
    }

}
