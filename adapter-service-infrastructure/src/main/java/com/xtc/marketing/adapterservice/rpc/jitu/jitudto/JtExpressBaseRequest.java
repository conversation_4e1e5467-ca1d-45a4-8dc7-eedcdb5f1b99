package com.xtc.marketing.adapterservice.rpc.jitu.jitudto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 极兔请求基类
 *
 * @param <T> 响应数据类型
 */
@Getter
@Setter
@ToString
public abstract class JtExpressBaseRequest<T> {

    /**
     * 签名
     */
    private String digest;
    /**
     * 获取响应数据类型
     *
     * @return 响应数据类型
     */
    public abstract Class<T> getResponseClass();
    /**
     * 获取API路径
     *
     * @return API路径
     */
    public abstract String getApiPath();

}
