package com.xtc.marketing.adapterservice.rpc.tenserpay.tenserpaydto.command;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TenserPayOrderCmd {

    /**
     * 币种：CNY
     */
    private final String currency = "CNY";

    /**
     * 服务类型
     * 微信支付：WECHAT_JSAPI_PAY
     * 支付宝支付：ALI_JSAPI_PAY
     * 银联支付：UNION_JSAPI_PAY
     */
    private ServiceType serviceType;

    /**
     * 收款方ID
     */
    private String payeeId;

    /**
     * 商户订单号
     * 32字符以内，可包含字母、数字、下划线；需保证在商户端不重复
     */
    private String outOrderNo;

    /**
     * 商户订单时间
     * 格式：yyyyMMddHHmmss
     */
    private String outOrderTime;

    /**
     * 用户ID
     * 支付宝：userId
     * 微信：openId
     * 银联：userId
     */
    private String userId;

    /**
     * 订单有效时间
     * 可选范围1m-24h
     * m代表分钟 1m代表订单时间1分钟有效
     * h代表小时 1h代表订单时间1小时有效
     * 订单时间最长24小时有效
     */
    private String validTime;

    /**
     * 订单金额
     * 单位：元，最多两位小数，金额不能为负数最长13位
     */
    private String totalAmount;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 订单结果异步通知地址
     * 订单成功后，通知订单结果
     */
    private String notifyUrl;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 服务类型
     */
    public enum ServiceType {
        /**
         * 微信支付
         */
        WECHAT_JSAPI_PAY,
        /**
         * 支付宝支付
         */
        ALI_JSAPI_PAY,
        /**
         * 银联支付
         */
        UNION_JSAPI_PAY,
        /**
         * 微信小程序
         */
        WECHAT_MINI_PROGRAM_PAY,
        /**
         * 支付宝小程序
         */
        ALI_MINI_PROGRAM_PAY
    }

}
