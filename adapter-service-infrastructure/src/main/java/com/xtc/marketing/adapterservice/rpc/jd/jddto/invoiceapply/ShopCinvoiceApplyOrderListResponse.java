
package com.xtc.marketing.adapterservice.rpc.jd.jddto.invoiceapply;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.jd.open.api.sdk.response.AbstractResponse;

public class ShopCinvoiceApplyOrderListResponse extends AbstractResponse {

    private ResultMO response;

    public ShopCinvoiceApplyOrderListResponse() {
    }

    @JsonProperty("response")
    public void setResponse(ResultMO response) {
        this.response = response;
    }

    @JsonProperty("response")
    public ResultMO getResponse() {
        return this.response;
    }

}
