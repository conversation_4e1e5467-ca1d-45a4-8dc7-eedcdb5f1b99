package com.xtc.marketing.adapterservice.rpc.jitu.jitudto.request;

import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.JtExpressBaseRequest;
import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.response.JtExpressRangeCheckResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 极兔超区查询请求
 */
@Getter
@Setter
@ToString
public class JtExpressRangeCheckRequest extends JtExpressBaseRequest<JtExpressRangeCheckResponse> {

    /**
     * 寄件人省份
     */
    private String senderProvName;
    /**
     * 寄件人城市
     */
    private String senderCityName;
    /**
     * 寄件人区县
     */
    private String senderAreaName;
    /**
     * 寄件人详细地址
     */
    private String senderAddress;
    /**
     * 收件人省份
     */
    private String receiverProvName;
    /**
     * 收件人城市
     */
    private String receiverCityName;
    /**
     * 收件人区县
     */
    private String receiverAreaName;
    /**
     * 收件人详细地址
     */
    private String receiverAddress;

    @Override
    public Class<JtExpressRangeCheckResponse> getResponseClass() {
        return JtExpressRangeCheckResponse.class;
    }

    @Override
    public String getApiPath() {
        return "/webopenplatformapi/api/route/rangeCheck";
    }

}
