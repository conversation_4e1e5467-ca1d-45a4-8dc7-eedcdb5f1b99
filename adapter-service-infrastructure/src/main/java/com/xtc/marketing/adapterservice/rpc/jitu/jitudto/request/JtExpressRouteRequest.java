package com.xtc.marketing.adapterservice.rpc.jitu.jitudto.request;

import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.JtExpressBaseRequest;
import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.response.JtExpressRouteResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 极兔快递轨迹查询请求
 */
@Getter
@Setter
@ToString
public class JtExpressRouteRequest extends JtExpressBaseRequest<JtExpressRouteResponse> {

    /**
     * 运单号，多个运单号以英文逗号隔开
     * 目前先支持一次性最多查询30票运单
     */
    private String billCodes;

    @Override
    public Class<JtExpressRouteResponse> getResponseClass() {
        return JtExpressRouteResponse.class;
    }

    @Override
    public String getApiPath() {
        return "/webopenplatformapi/api/logistics/trace";
    }

}
