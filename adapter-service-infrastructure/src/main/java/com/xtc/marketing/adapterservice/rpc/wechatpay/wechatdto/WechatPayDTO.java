package com.xtc.marketing.adapterservice.rpc.wechatpay.wechatdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
@NoArgsConstructor
public class WechatPayDTO {

    /**
     * 订单支付成功状态
     */
    public static final String TRADE_STATE_SUCCESS = "SUCCESS";

    @SerializedName("appid")
    private String appid;

    /**
     * 商户号
     */
    @SerializedName("mchid")
    private String mchid;

    /**
     * 微信支付订单号
     */
    @SerializedName("transaction_id")
    private String transactionId;

    /**
     * 商户订单号
     */
    @SerializedName("out_trade_no")
    private String outTradeNo;

    /**
     * 交易状态
     */
    @SerializedName("trade_state")
    private String tradeState;

    /**
     * 交易状态描述
     */
    @SerializedName("trade_state_desc")
    private String tradeStateDesc;

    /**
     * 支付完成时间
     */
    @SerializedName("success_time")
    private String successTime;

    /**
     * 支付者
     */
    @SerializedName("payer")
    private Payer payer;

    /**
     * 订单金额
     */
    @SerializedName("amount")
    private Amount amount;

    /**
     * 优惠详情
     */
    @SerializedName("promotion_detail")
    private List<Promotion> promotionDetails;

    /**
     * 支付者
     */
    @Getter
    @Setter
    @ToString
    public static class Payer {

        /**
         * 用户标识
         */
        @SerializedName("openid")
        private String openid;

    }

    /**
     * 金额信息
     */
    @Getter
    @Setter
    @ToString
    public static class Amount {

        /**
         * 订单总金额
         */
        @SerializedName("total")
        private Integer total;

        /**
         * 用户支付金额
         */
        @SerializedName("payer_total")
        private Integer payerTotal;

    }

    /**
     * 优惠详情
     */
    @Getter
    @Setter
    @ToString
    public static class Promotion {

        /**
         * 优惠券ID
         */
        @SerializedName("coupon_id")
        private String couponId;

        /**
         * 优惠名称
         */
        @SerializedName("name")
        private String name;

        /**
         * 优惠券面额
         */
        @SerializedName("amount")
        private Integer amount;

        /**
         * 微信出资金额
         */
        @SerializedName("wechatpay_contribute")
        private Integer wechatpayContribute;

    }

    /**
     * 支付成功标识
     *
     * @return 支付成功标识
     */
    public boolean success() {
        return TRADE_STATE_SUCCESS.equals(this.tradeState);
    }

    /**
     * 支付失败标识
     *
     * @return 支付失败标识
     */
    public boolean failure() {
        return !success();
    }

}
