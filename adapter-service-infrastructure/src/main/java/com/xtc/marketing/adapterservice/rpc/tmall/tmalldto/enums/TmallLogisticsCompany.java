package com.xtc.marketing.adapterservice.rpc.tmall.tmalldto.enums;

import lombok.Getter;

/**
 * 天猫平台快递公司
 */
@Getter
public enum TmallLogisticsCompany {
    /**
     * 顺丰
     */
    SF("顺丰", "SF", "SF", "2", "https://cloudprint.cainiao.com/template/standard/474941/33"),
    /**
     * 圆通
     */
    YTO("圆通", "YTO", "", "", "https://cloudprint.cainiao.com/template/standard/290659/89"),
    /**
     * EMS
     */
    EMS("EMS", "EMS", "", "", "https://cloudprint.cainiao.com/template/standard/345208/37"),
    ;

    /**
     * 公司名称
     */
    private final String name;
    /**
     * 公司代码
     */
    private final String code;
    /**
     * 品牌编码，顺丰要求必填，其他快递不传或者空字符串
     */
    private final String brandCode;
    /**
     * 快递产品类型
     */
    private final String productCode;
    /**
     * 电子面单，模板url
     */
    private final String templateUrl;

    TmallLogisticsCompany(String name, String code, String brandCode, String productCode, String templateUrl) {
        this.name = name;
        this.code = code;
        this.brandCode = brandCode;
        this.productCode = productCode;
        this.templateUrl = templateUrl;
    }
}
