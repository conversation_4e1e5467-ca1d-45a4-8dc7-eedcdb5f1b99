package com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.command;

import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.enums.InternalShopLogisticsCompany;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class InternalShopShippingCmd {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 物流单号
     */
    private String waybillNo;

    /**
     * 快递公司
     */
    private InternalShopLogisticsCompany logisticsCompany;

    /**
     * 校验参数非法
     *
     * @return 执行结果
     */
    public boolean checkIllegal() {
        return orderNo == null || waybillNo == null || logisticsCompany == null;
    }

}
