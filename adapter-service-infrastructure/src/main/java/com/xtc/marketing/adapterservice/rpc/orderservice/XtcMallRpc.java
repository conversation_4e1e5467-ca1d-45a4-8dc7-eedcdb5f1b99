package com.xtc.marketing.adapterservice.rpc.orderservice;

import com.xtc.marketing.adapterservice.constant.SystemConstant;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.orderservice.xtcmalldto.XtcMallDecryptDTO;
import com.xtc.marketing.adapterservice.rpc.orderservice.xtcmalldto.query.XtcMallEncryptQry;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpServerErrorException;

import java.rmi.RemoteException;

/**
 * 会员商城RPC
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class XtcMallRpc {

    /**
     * 激活的配置
     */
    @Value("${spring.profiles.active}")
    private String profileActive;

    /**
     * 会员商城地址（正式）
     */
    private static final String DOMAIN_XTC_MALL = "https://xtc-mall.okii.com/shopAdmin";
    /**
     * 会员商城地址（测试）
     */
    private static final String DOMAIN_XTC_MALL_TEST = "https://xtc-mall-test.okii.com/shopAdmin";

    /**
     * 解密
     *
     * @param qry 参数
     * @return 解密数据
     */
    public XtcMallDecryptDTO xtcMallOrderDecrypt(XtcMallEncryptQry qry) {
        String url = this.getDomain() + "/i/order/decryptSensitiveData";
        String responseStr = "";
        try {
            responseStr = HttpUtil.post(url, qry);

            SingleResponse<XtcMallDecryptDTO> response = GsonUtil.jsonToBean(responseStr,
                    SingleResponse.class, XtcMallDecryptDTO.class);
            if (response.isFailure()) {
                throw new RemoteException("RPC返回异常状态码");
            }
            return response.getData();
        } catch (Exception e) {
            if (e instanceof HttpServerErrorException) {
                responseStr = ((HttpServerErrorException) e).getResponseBodyAsString();
            }
            String msg = String.format("会员商城调用异常 response: %s, url: %s", responseStr, url);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
        }
    }

    /**
     * 获取域名
     *
     * @return 域名
     */
    private String getDomain() {
        return SystemConstant.isTestProfile(profileActive) ? DOMAIN_XTC_MALL_TEST : DOMAIN_XTC_MALL;
    }

}
