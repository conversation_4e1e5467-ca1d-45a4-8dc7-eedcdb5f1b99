package com.xtc.marketing.adapterservice.rpc.jd.jddto.invoiceapply;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.jd.open.api.sdk.domain.jinsuanpan.FinInvoiceApplyOrderProvider.response.list.ApplyOrderJosVo;

import java.io.Serializable;
import java.util.List;

public class PageMO implements Serializable {

    private int pageIndex;
    private int pageSize;
    private Long totalCount;
    private List<ApplyOrderJosVo> list;

    public PageMO() {
    }

    @JsonProperty("pageIndex")
    public void setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
    }

    @JsonProperty("pageIndex")
    public int getPageIndex() {
        return this.pageIndex;
    }

    @JsonProperty("pageSize")
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    @JsonProperty("pageSize")
    public int getPageSize() {
        return this.pageSize;
    }

    @JsonProperty("totalCount")
    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    @JsonProperty("totalCount")
    public Long getTotalCount() {
        return this.totalCount;
    }

    @JsonProperty("list")
    public void setList(List<ApplyOrderJosVo> list) {
        this.list = list;
    }

    @JsonProperty("list")
    public List<ApplyOrderJosVo> getList() {
        return this.list;
    }

}
