package com.xtc.marketing.adapterservice.rpc.jd;

import com.google.common.collect.ImmutableMap;
import com.jd.open.api.sdk.DefaultJdClient;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.domain.jinsuanpan.FinInvoiceApplyOrderProvider.response.order.ApplyOrderVO;
import com.jd.open.api.sdk.domain.jinsuanpan.FinInvoiceOwnProvider.response.amount.InvoiceOwnQueryAmountResult;
import com.jd.open.api.sdk.domain.jinsuanpan.FinInvoiceOwnProvider.response.amount.OrderShouldInvoiceAmount;
import com.jd.open.api.sdk.domain.order.IOrderService.response.getmobilelist.OrderPrivacyModel;
import com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.get.OrderSearchInfo;
import com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.search.OrderListResult;
import com.jd.open.api.sdk.domain.order.OrderShipmentService.response.shipment.OperatorResult;
import com.jd.open.api.sdk.domain.refundapply.RefundApplySoaService.response.queryPageList.QueryResult;
import com.jd.open.api.sdk.domain.shangjiashouhou.ServiceReceiveProvider.response.register.Result;
import com.jd.open.api.sdk.domain.wujiemiandan.WaybillReceiveApi.response.receive.WaybillResponseDTO;
import com.jd.open.api.sdk.domain.wujiemiandan.WaybillReceiveApi.response.receive.WaybillResultDTO;
import com.jd.open.api.sdk.request.JdRequest;
import com.jd.open.api.sdk.request.etms.LdopWaybillReceiveRequest;
import com.jd.open.api.sdk.request.evaluation.PopPopCommentJsfServiceGetVenderCommentsForJosRequest;
import com.jd.open.api.sdk.request.jinsuanpan.PopCinvoiceApplyOrderRequest;
import com.jd.open.api.sdk.request.jinsuanpan.PopInvoiceSelfAmountRequest;
import com.jd.open.api.sdk.request.jinsuanpan.PopInvoiceSelfApplyRequest;
import com.jd.open.api.sdk.request.mall.DigitalSubsidyUploadSnInsertRequest;
import com.jd.open.api.sdk.request.order.*;
import com.jd.open.api.sdk.request.refundapply.PopAfsSoaRefundapplyQueryByIdRequest;
import com.jd.open.api.sdk.request.refundapply.PopAfsSoaRefundapplyQueryPageListRequest;
import com.jd.open.api.sdk.request.shangjiashouhou.AscQueryViewRequest;
import com.jd.open.api.sdk.request.shangjiashouhou.AscReceiveRegisterRequest;
import com.jd.open.api.sdk.response.AbstractResponse;
import com.jd.open.api.sdk.response.etms.LdopWaybillReceiveResponse;
import com.jd.open.api.sdk.response.evaluation.PopPopCommentJsfServiceGetVenderCommentsForJosResponse;
import com.jd.open.api.sdk.response.jinsuanpan.PopCinvoiceApplyOrderResponse;
import com.jd.open.api.sdk.response.jinsuanpan.PopInvoiceSelfAmountResponse;
import com.jd.open.api.sdk.response.jinsuanpan.PopInvoiceSelfApplyResponse;
import com.jd.open.api.sdk.response.mall.DigitalSubsidyUploadSnInsertResponse;
import com.jd.open.api.sdk.response.order.*;
import com.jd.open.api.sdk.response.refundapply.PopAfsSoaRefundapplyQueryByIdResponse;
import com.jd.open.api.sdk.response.refundapply.PopAfsSoaRefundapplyQueryPageListResponse;
import com.jd.open.api.sdk.response.shangjiashouhou.AscQueryViewResponse;
import com.jd.open.api.sdk.response.shangjiashouhou.AscReceiveRegisterResponse;
import com.jd.security.tdeclient.SecretJdClient;
import com.jd.security.tdeclient.TDEClient;
import com.lop.open.api.sdk.DefaultDomainApiClient;
import com.lop.open.api.sdk.domain.jdcloudprint.PullDataService.pullData.PrePrintDataInfo;
import com.lop.open.api.sdk.domain.jdcloudprint.PullDataService.pullData.PullDataReqDTO;
import com.lop.open.api.sdk.domain.jdcloudprint.PullDataService.pullData.PullDataRespDTO;
import com.lop.open.api.sdk.domain.jdcloudprint.PullDataService.pullData.WayBillInfo;
import com.lop.open.api.sdk.plugin.LopPlugin;
import com.lop.open.api.sdk.plugin.factory.OAuth2PluginFactory;
import com.lop.open.api.sdk.request.DomainAbstractRequest;
import com.lop.open.api.sdk.request.jdcloudprint.PullDataServicePullDataLopRequest;
import com.lop.open.api.sdk.response.jdcloudprint.PullDataServicePullDataLopResponse;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.jd.jddto.JdRefreshTokenDTO;
import com.xtc.marketing.adapterservice.rpc.jd.jddto.PrintDataEncryptDTO;
import com.xtc.marketing.adapterservice.rpc.jd.jddto.enums.JdLogisticsCompany;
import com.xtc.marketing.adapterservice.rpc.jd.jddto.invoiceapply.PageMO;
import com.xtc.marketing.adapterservice.rpc.jd.jddto.invoiceapply.ShopCinvoiceApplyOrderListRequest;
import com.xtc.marketing.adapterservice.rpc.jd.jddto.invoiceapply.ShopCinvoiceApplyOrderListResponse;
import com.xtc.marketing.adapterservice.rpc.jd.jddto.request.LdopAlphaWaybillReceiveRequest;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.OrderDecryptDTO;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;

import java.net.SocketTimeoutException;
import java.rmi.RemoteException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 京东商城RPC
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class JdRpc {

    /**
     * 缓存 client 对象，京东平台
     */
    private static final Map<String, JdClient> CLIENT_CACHE_JD = new ConcurrentHashMap<>();
    /**
     * 缓存 client 对象，京东物流平台
     */
    private static final Map<String, DefaultDomainApiClient> CLIENT_CACHE_LOP = new ConcurrentHashMap<>();
    /**
     * 缓存 client 对象，京东物流平台，使用京东平台账号插件
     */
    private static final Map<String, LopPlugin> CLIENT_CACHE_LOP_PLUGIN = new ConcurrentHashMap<>();
    /**
     * 京东物流平台地址
     */
    private static final String LOP_DOMAIN = "https://api.jdl.com";
    /**
     * 中转服务器地址
     */
    private static final String PROXY_DOMAIN = "http://116.196.125.220:8080";
    /**
     * 解密电话号码使用的参数 appName
     */
    private static final String DECRYPT_APP_NAME = "小天才智慧通发货系统";
    /**
     * 查询订单字段
     */
    private static final String ORDER_FIELDS = "orderId,venderId,orderType,payType,orderTotalPrice,orderSellerPrice," +
            "orderPayment,freightPrice,sellerDiscount,orderState,orderStateRemark,deliveryType,invoiceEasyInfo," +
            "invoiceInfo,invoiceCode,orderRemark,orderStartTime,orderEndTime,consigneeInfo,itemInfoList,couponDetailList," +
            "venderRemark,balanceUsed,pin,returnOrder,paymentConfirmTime,waybill,logisticsId,vatInfo,modified," +
            "directParentOrderId,parentOrderId,customs,customsModel,orderSource,storeOrder,idSopShipmenttype,scDT," +
            "serviceFee,pauseBizInfo,taxFee,tuiHuoWuYou,storeId,orderExt,orderMarkDesc,realPin,popSignMap,promisePickDate," +
            "opPickDate,opDeliveredDate,open_id_buyer,xid_buyer,sendpayMap";
    /**
     * 查询订单状态
     */
    private static final String ORDER_STATE = "WAIT_SELLER_STOCK_OUT,WAIT_GOODS_RECEIVE_CONFIRM," +
            "WAIT_SELLER_DELIVERY,PAUSE,FINISHED_L,TRADE_CANCELED,LOCKED,POP_ORDER_PAUSE";

    /**
     * API：获取 AccessToken
     */
    private static final String API_ACCESS_TOKEN = "https://open-oauth.jd.com/oauth2/access_token" +
            "?app_key=%s&app_secret=%s&code=%s&grant_type=authorization_code";
    /**
     * API：刷新 AccessToken
     */
    private static final String API_REFRESH_TOKEN = "https://open-oauth.jd.com/oauth2/refresh_token" +
            "?app_key=%s&app_secret=%s&refresh_token=%s&grant_type=refresh_token";
    /**
     * API：查询订单分页列表
     */
    private static final String API_ORDER_PAGE = "/api/jd/order/page";
    /**
     * API：查询订单详情
     */
    private static final String API_ORDER_DETAIL = "/api/jd/order/detail";
    /**
     * API：查询订单的应开发票金额
     */
    private static final String API_ORDER_INVOICE_AMOUNT = "/api/jd/order/invoice-amount";
    /**
     * API：查询退款单分页列表
     */
    private static final String API_REFUND_PAGE = "/api/jd/refund/page";
    /**
     * API：查询退款单明细
     */
    private static final String API_REFUND_DETAIL_RAW = "/api/jd/refund/detail-raw";
    /**
     * API：查询发票申请分页列表
     */
    private static final String API_INVOICE_APPLY_PAGE = "/api/jd/invoice-apply/page";
    /**
     * API：查询评价分页列表
     */
    private static final String API_ORDER_COMMENT_PAGE = "/api/jd/order-comment/page";

    /**
     * 刷新 AccessToken
     *
     * @param shop 店铺
     * @return AccessToken
     */
    public JdRefreshTokenDTO refreshToken(ShopDO shop) {
        return this.getAccessToken(shop, null);
    }

    /**
     * 获取 AccessToken
     *
     * @param shop 店铺
     * @param code 授权码
     * @return AccessToken
     */
    public JdRefreshTokenDTO getAccessToken(ShopDO shop, String code) {
        String url;
        if (StringUtils.isNotBlank(code)) {
            // 使用 code 获取新访问令牌
            url = String.format(API_ACCESS_TOKEN, shop.getAppKey(), shop.getAppSecret(), code);
        } else {
            // 默认刷新令牌
            url = String.format(API_REFRESH_TOKEN, shop.getAppKey(), shop.getAppSecret(), shop.getAppRefreshToken());
        }
        String responseStr = "";
        try {
            responseStr = HttpUtil.get(url);
            if (StringUtils.isBlank(responseStr)) {
                throw remoteException();
            }
            return GsonUtil.jsonToBean(responseStr, JdRefreshTokenDTO.class);
        } catch (Exception e) {
            throw this.rpcSysException(responseStr, url, e);
        }
    }

    /**
     * 查询订单分页列表
     *
     * @param shop    店铺
     * @param request 参数
     * @return 订单分页列表
     */
    public OrderListResult pageOrders(ShopDO shop, PopOrderSearchRequest request) {
        request.setOptionalFields(ORDER_FIELDS);
        request.setOrderState(ORDER_STATE);
        // 按修改时间查询
        request.setDateType(0);
        // 倒序排序
        request.setSortType(1);
        PopOrderSearchResponse response = this.call(shop, request, API_ORDER_PAGE);
        return response.getSearchorderinfoResult();
    }

    /**
     * 查询订单详情
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 订单详情
     */
    public OrderSearchInfo getOrder(ShopDO shop, String orderNo) {
        PopOrderGetRequest request = new PopOrderGetRequest();
        request.setOptionalFields(ORDER_FIELDS);
        request.setOrderId(Long.parseLong(orderNo));
        PopOrderGetResponse response = this.call(shop, request, API_ORDER_DETAIL);
        return response.getOrderDetailInfo().getOrderInfo();
    }

    /**
     * 查询发票申请
     *
     * @param shop    店铺
     * @param request 请求参数
     * @return 发票申请
     */
    public PageMO pageInvoiceApply(ShopDO shop, ShopCinvoiceApplyOrderListRequest request) {
        ShopCinvoiceApplyOrderListResponse response = this.call(shop, request, API_INVOICE_APPLY_PAGE);
        return response.getResponse().getData();
    }

    /**
     * 查询发票申请
     *
     * @param shop    店铺
     * @param request 请求参数
     * @return 发票申请详情
     */
    public ApplyOrderVO getInvoiceApply(ShopDO shop, PopCinvoiceApplyOrderRequest request) {
        PopCinvoiceApplyOrderResponse response = this.call(shop, request);
        return response.getResponse().getData();
    }

    /**
     * 退款单列表查询
     *
     * @param shop    店铺
     * @param request 参数
     * @return 退款单列表
     */
    public QueryResult pageRefunds(ShopDO shop, PopAfsSoaRefundapplyQueryPageListRequest request) {
        PopAfsSoaRefundapplyQueryPageListResponse response = this.call(shop, request, API_REFUND_PAGE);
        return response.getQueryResult();
    }

    /**
     * 退款单详情查询
     *
     * @param shop     店铺
     * @param refundId 退款单
     * @return 退款单
     */
    public com.jd.open.api.sdk.domain.refundapply.RefundApplySoaService.response.queryById.QueryResult getRefund(ShopDO shop, String refundId) {
        PopAfsSoaRefundapplyQueryByIdRequest request = new PopAfsSoaRefundapplyQueryByIdRequest();
        request.setId(Long.parseLong(refundId));
        PopAfsSoaRefundapplyQueryByIdResponse response = this.call(shop, request);
        return response.getQueryResult();
    }

    /**
     * 退款单详情
     *
     * @param shop     店铺
     * @param orderNo  订单
     * @param refundId 退款单
     * @param shopId   店铺id
     * @return 退款单
     */
    public AscQueryViewResponse getRefund(ShopDO shop, String orderNo, String refundId, String shopId) {
        AscQueryViewRequest request = new AscQueryViewRequest();
        request.setOperateNick("adapter-service");
        request.setOperatePin("adapter-service");
        request.setOrderId(Long.parseLong(orderNo));
        request.setServiceId(Long.parseLong(refundId));
        request.setBuId(shopId);
        return this.call(shop, request, API_REFUND_DETAIL_RAW);
    }

    /**
     * 生成电子面单
     *
     * @param shop    店铺
     * @param request 参数
     * @return 电子面单
     */
    public String createLogisticsOrder(ShopDO shop, LdopWaybillReceiveRequest request) {
        LdopWaybillReceiveResponse response = this.call(shop, request);
        // 判断业务结果异常
        if (response.getReceiveorderinfoResult() == null
                || response.getReceiveorderinfoResult().getResultCode() == null
                || response.getReceiveorderinfoResult().getResultCode() != 100) {
            throw this.rpcSysException(response);
        }
        return response.getReceiveorderinfoResult().getDeliveryId();
    }

    /**
     * 生成电子面单
     *
     * @param shop    店铺
     * @param request 参数
     * @return 运单号
     */
    public String createLogisticsOrderLodp(ShopDO shop, LdopAlphaWaybillReceiveRequest request) {
        com.jd.open.api.sdk.request.wujiemiandan.LdopAlphaWaybillReceiveRequest ldopAlphaWaybillReceiveRequest =
                new com.jd.open.api.sdk.request.wujiemiandan.LdopAlphaWaybillReceiveRequest();
        ldopAlphaWaybillReceiveRequest.setContent(GsonUtil.objectToJson(request));
        WaybillResponseDTO response = this.callToString(shop, ldopAlphaWaybillReceiveRequest, WaybillResponseDTO.class);
        if (response.getStatusCode() == null || response.getStatusCode() != 0) {
            throw this.rpcSysException(response);
        }
        List<String> waybillNoList = Optional.ofNullable(response.getData())
                .map(WaybillResultDTO::getWaybillCodeList)
                .orElse(Collections.emptyList());
        return waybillNoList.stream().findFirst().orElse(null);
    }

    /**
     * 电子面单数据加密
     * <br><a href="https://open.jdl.com/#/open-business-document/api-doc/157/191">官方文档</a>
     *
     * @param shop             店铺
     * @param orderNO          订单号
     * @param logisticsOrderId 物流订单号
     * @param company          物流公司
     * @param waybillNo        运单号
     * @return 电子面单数据加密
     */
    public PrintDataEncryptDTO printDataEncrypt(ShopDO shop, String orderNO, String logisticsOrderId,
                                                JdLogisticsCompany company, String waybillNo) {
        PullDataReqDTO pullDataReqDTO = new PullDataReqDTO();
        // 物流订单号
        pullDataReqDTO.setObjectId(logisticsOrderId);
        // 承运商编码。承运商编码列表：https://cloud.jdl.com/#/open-business-document/access-guide/157/54133
        pullDataReqDTO.setCpCode(company.getProviderCode());
        // cpCode为JD\JDKY\JDDJ\ZY时，key的值是ewCustomerCode，value传商家编码（京东快递传商家编码，京东快运、京东大件传事业部编码）
        // cpCode是非京东物流的其他物流服务时，key的值是eCustomerCode，value传下运单时无界电子面单店铺的vendorid或vendorcode
        if (company == JdLogisticsCompany.JD) {
            pullDataReqDTO.setParameters(ImmutableMap.of("ewCustomerCode", company.getCustomerCode(shop.getShopCode())));
        } else {
            pullDataReqDTO.setParameters(ImmutableMap.of("eCustomerCode", shop.getShopId().split("\\|")[0]));
        }

        // 运单信息列表。最多支持10条
        WayBillInfo wayBillInfo = new WayBillInfo();
        wayBillInfo.setOrderNo(orderNO);
        // 是否京东商城订单（pop订单）。枚举值：1—京东商城订单；0—其他平台订单。不填或者填写0则认为非京东商城订单
        wayBillInfo.setPopFlag(1);
        // 京东物流运单号，cpCode为JD（京东快递）、JDKY（京东快运）、JDDJ（京东大件）、ZY（众邮快递）时必填
        wayBillInfo.setJdWayBillCode(waybillNo);
        // 三方物流服务商运单号。cpCode是顺丰、德邦、跨越、三通一达等其他物流服务商（非京东物流）时必填。长度2-50
        wayBillInfo.setWayBillCode(waybillNo);
        pullDataReqDTO.setWayBillInfos(Collections.singletonList(wayBillInfo));

        PullDataServicePullDataLopRequest request = new PullDataServicePullDataLopRequest();
        request.setPullDataReqDTO(pullDataReqDTO);
        PullDataServicePullDataLopResponse response = this.callLop(shop, request);
        List<PrePrintDataInfo> prePrintDatas = Optional.ofNullable(response.getResult()).map(PullDataRespDTO::getPrePrintDatas)
                .orElseThrow(() -> this.rpcSysException(response));

        PrintDataEncryptDTO printDataEncryptDTO = new PrintDataEncryptDTO();
        printDataEncryptDTO.setTemplateUrl(company.getPrintTemplateUrl());
        String prePrintDataInfo = prePrintDatas.stream().findFirst().map(PrePrintDataInfo::getPerPrintData).orElse(null);
        printDataEncryptDTO.setPerPrintData(prePrintDataInfo);
        return printDataEncryptDTO;
    }

    /**
     * 评价列表查询
     *
     * @param shop    店铺
     * @param request 参数
     * @return 评价列表
     */
    public PopPopCommentJsfServiceGetVenderCommentsForJosResponse pageComments(
            ShopDO shop, PopPopCommentJsfServiceGetVenderCommentsForJosRequest request) {
        return this.call(shop, request, API_ORDER_COMMENT_PAGE);
    }

    /**
     * 物流发货
     *
     * @param shop    店铺
     * @param request 参数
     * @return 执行结果
     */
    public OperatorResult orderShipping(ShopDO shop, PopOrderShipmentRequest request) {
        return this.call(shop, request).getSopjosshipmentResult();
    }

    /**
     * 无需物流发货
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 执行结果
     */
    public boolean orderDummyShipping(ShopDO shop, String orderNo) {
        PopOrderShipmentRequest request = new PopOrderShipmentRequest();
        request.setOrderId(Long.parseLong(orderNo));
        // 厂家自送：1274
        request.setLogiCoprId("1274");
        PopOrderShipmentResponse response = this.call(shop, request);
        return Optional.of(response)
                .map(PopOrderShipmentResponse::getSopjosshipmentResult)
                .map(OperatorResult::getSuccess)
                .orElse(false);
    }

    /**
     * 发票上传
     *
     * @param shop    店铺
     * @param request 请求参数
     */
    public boolean uploadInvoiceBase64(ShopDO shop, PopInvoiceSelfApplyRequest request) {
        PopInvoiceSelfApplyResponse response = this.call(shop, request);
        if (BooleanUtils.isFalse(response.getApplyinvoiceforownResult().getSuccess())) {
            throw this.rpcSysException(response);
        }
        return response.getApplyinvoiceforownResult().getSuccess();
    }

    /**
     * 备注
     *
     * @param shop    店铺
     * @param request 参数
     * @return 执行结果
     */
    public boolean orderRemark(ShopDO shop, PopOrderModifyVenderRemarkRequest request) {
        PopOrderModifyVenderRemarkResponse response = this.call(shop, request);
        return response.getModifyvenderremarkResult().getSuccess();
    }

    /**
     * 解密文本
     *
     * @param shop   店铺
     * @param cipher 密文
     * @return 明文
     */
    public Optional<String> decryptText(ShopDO shop, String cipher) {
        try {
            TDEClient tdeClient = SecretJdClient.getInstance(shop.getApiUrl(), shop.getAppAccessToken(), shop.getAppKey(), shop.getAppSecret());
            return Optional.ofNullable(tdeClient.decryptString(cipher));
        } catch (Exception e) {
            log.error("京东解密失败 cipher: {}", cipher, e);
            return Optional.empty();
        }
    }

    /**
     * 解密电话号码
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 电话号码
     */
    public OrderDecryptDTO decryptMobile(ShopDO shop, String orderNo) {
        PopOrderGetmobilelistRequest request = new PopOrderGetmobilelistRequest();
        request.setOrderId(orderNo);
        request.setAppName(DECRYPT_APP_NAME);
        request.setExpiration(30);
        PopOrderGetmobilelistResponse response = this.call(shop, request);

        OrderPrivacyModel decrypt = response.getResult().getData().get(orderNo);
        if (decrypt == null) {
            return OrderDecryptDTO.builder().build();
        }

        OrderDecryptDTO decryptDTO = OrderDecryptDTO.builder()
                .phone(decrypt.getCustomerPhone())
                .mobile(decrypt.getConsMobilePhone())
                .build();
        Optional.ofNullable(decrypt.getExpiration()).map(DateUtil::toLocalDateTime).ifPresent(decryptDTO::setMobileExpireTime);
        return decryptDTO;
    }

    /**
     * 查询订单应开票金额
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 订单应开票金额
     */
    public OrderShouldInvoiceAmount getInvoiceAmount(ShopDO shop, String orderNo) {
        PopInvoiceSelfAmountRequest request = new PopInvoiceSelfAmountRequest();
        request.setOrderId(orderNo);
        PopInvoiceSelfAmountResponse response = this.call(shop, request, API_ORDER_INVOICE_AMOUNT);
        return Optional.of(response)
                .map(PopInvoiceSelfAmountResponse::getQueryamountforownResult)
                .map(InvoiceOwnQueryAmountResult::getData)
                .orElse(null);
    }

    /**
     * 退货确认入仓
     *
     * @param shop    店铺
     * @param request 平台退货确认入仓参数
     */
    public void refundGoodsToWarehouse(ShopDO shop, AscReceiveRegisterRequest request) {
        AscReceiveRegisterResponse response = this.call(shop, request);
        Optional.of(response)
                .map(AscReceiveRegisterResponse::getResult)
                .map(Result::getSuccess)
                .filter(BooleanUtils::isTrue)
                .orElseThrow(() -> this.rpcSysException(response));
    }

    /**
     * 批量上传条码
     *
     * @param shop    店铺
     * @param request 条码上传参数
     */
    public void barcodeUpload(ShopDO shop, DigitalSubsidyUploadSnInsertRequest request) {
        DigitalSubsidyUploadSnInsertResponse response = this.call(shop, request);
        // 返回值不为空，则抛出异常
        if (response.getReturnType() == null || !"200".equals(response.getReturnType().getCode())) {
            throw this.rpcSysException(response);
        }
    }

    /**
     * 执行远程调用
     *
     * @param shop    店铺
     * @param request 参数
     * @param <T>     返回值类型
     * @return 执行结果
     */
    private <T extends AbstractResponse> T call(ShopDO shop, JdRequest<T> request) {
        String responseStr = "";
        try {
            // 发起请求
            log.info("京东平台参数: {}", request.getAppJsonParams());
            JdClient client = this.getJdClient(shop);
            T response = client.execute(request);
            // 解析响应结果
            responseStr = GsonUtil.objectToJson(response);
            log.info("京东平台返回值: {}", responseStr);
            if (!"0".contains(response.getCode())) {
                throw remoteException();
            }
            return response;
        } catch (Exception e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                responseStr = "请求超时";
            }
            throw this.rpcSysException(responseStr, e);
        }
    }

    /**
     * 执行远程调用
     *
     * @param shop    店铺
     * @param request 请求
     * @param api     中转接口
     * @param <T>     返回值类型
     * @return 执行结果
     */
    private <T extends AbstractResponse> T call(ShopDO shop, JdRequest<T> request, String api) {
        String url = PROXY_DOMAIN + api;
        String responseStr = "";
        try {
            // 生成请求参数
            Map<String, Object> body = ImmutableMap.of("shop", shop, "requestParam", GsonUtil.objectToJson(request));
            // 执行请求
            responseStr = HttpUtil.post(url, body);
            // 解析响应结果
            if (StringUtils.isBlank(responseStr)) {
                throw remoteException();
            }
            ResponseEntity<String> responseEntity = GsonUtil.jsonToBean(responseStr, ResponseEntity.class);
            return GsonUtil.jsonToBean(responseEntity.getBody(), request.getResponseClass());
        } catch (Exception e) {
            if (e instanceof HttpStatusCodeException) {
                responseStr = ((HttpStatusCodeException) e).getResponseBodyAsString();
            }
            if (e.getCause() instanceof SocketTimeoutException) {
                responseStr = "请求超时";
            }
            throw this.rpcSysException(responseStr, url, e);
        }
    }

    /**
     * 执行远程调用
     *
     * @param shop    店铺
     * @param request 参数
     * @param <T>     返回值类型
     * @return 执行结果
     */
    private <T extends AbstractResponse, R> R callToString(ShopDO shop, JdRequest<T> request, Class<R> clazz) {
        String responseStr = "";
        try {
            // 发起请求
            log.info("京东平台参数: {}", request.getAppJsonParams());
            JdClient client = this.getJdClient(shop);
            responseStr = client.executeToString(request);
            log.info("京东平台返回值: {}", responseStr);
            // 解析响应结果
            R response = GsonUtil.jsonToBean(responseStr, clazz);
            if (response == null) {
                throw remoteException();
            }
            return GsonUtil.jsonToBean(responseStr, clazz);
        } catch (Exception e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                responseStr = "请求超时";
            }
            throw this.rpcSysException(responseStr, e);
        }
    }

    /**
     * 执行远程调用
     *
     * @param shop    店铺
     * @param request 参数
     * @param <T>     返回值类型
     * @return 执行结果
     */
    private <T extends com.lop.open.api.sdk.response.AbstractResponse> T callLop(ShopDO shop, DomainAbstractRequest<T> request) {
        String responseStr = "";
        try {
            // 使用京东宙斯平台鉴权
            request.setUseJosAuth(true);
            request.addLopPlugin(this.getLopPlugin(shop));
            // 发起请求
            log.info("京东物流平台参数: {}", request.getAppJsonParams());
            DefaultDomainApiClient client = this.getLopClient(shop);
            T response = client.execute(request);
            // 解析响应结果
            responseStr = GsonUtil.objectToJson(response);
            log.info("京东物流平台返回值: {}", responseStr);
            if (!"0".contains(response.getCode())) {
                throw remoteException();
            }
            return response;
        } catch (Exception e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                responseStr = "请求超时";
            }
            throw this.rpcSysException(responseStr, e);
        }
    }

    /**
     * 获取请求客户端，京东平台
     *
     * @param shop 店铺
     * @return 客户端
     */
    private JdClient getJdClient(ShopDO shop) {
        String key = shop.getShopCode();
        if (CLIENT_CACHE_JD.get(key) == null) {
            JdClient client = new DefaultJdClient(shop.getApiUrl(), shop.getAppAccessToken(),
                    shop.getAppKey(), shop.getAppSecret(), 500, 3000);
            CLIENT_CACHE_JD.put(key, client);
        }
        return CLIENT_CACHE_JD.get(key);
    }

    /**
     * 获取请求客户端，京东物流平台
     *
     * @param shop 店铺
     * @return 客户端
     */
    private DefaultDomainApiClient getLopClient(ShopDO shop) {
        String key = shop.getShopCode();
        if (CLIENT_CACHE_LOP.get(key) == null) {
            DefaultDomainApiClient client = new DefaultDomainApiClient(LOP_DOMAIN, 500, 3000);
            CLIENT_CACHE_LOP.put(key, client);
        }
        return CLIENT_CACHE_LOP.get(key);
    }

    /**
     * 获取请求客户端，京东物流平台，使用京东平台账号插件
     *
     * @param shop 店铺
     * @return 客户端
     */
    private LopPlugin getLopPlugin(ShopDO shop) {
        String key = shop.getShopCode();
        if (CLIENT_CACHE_LOP_PLUGIN.get(key) == null) {
            LopPlugin lopPlugin = OAuth2PluginFactory.produceLopPlugin(shop.getAppKey(), shop.getAppSecret(), shop.getAppAccessToken());
            CLIENT_CACHE_LOP_PLUGIN.put(key, lopPlugin);
        }
        return CLIENT_CACHE_LOP_PLUGIN.get(key);
    }

    /**
     * 远程接口异常
     *
     * @return 异常
     */
    private RemoteException remoteException() {
        return new RemoteException("RPC返回异常状态码");
    }

    /**
     * 远程调用异常
     *
     * @param response 响应
     * @return 异常
     */
    private SysException rpcSysException(Object response) {
        String responseStr = GsonUtil.objectToJson(response);
        String msg = String.format("京东平台调用异常 response: %s", responseStr);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg);
    }

    /**
     * 远程调用异常
     *
     * @param responseStr 响应结果
     * @param e           异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, Exception e) {
        String msg = String.format("京东平台调用异常 response: %s", responseStr);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
    }

    /**
     * 远程调用异常
     *
     * @param responseStr 响应结果
     * @param url         url
     * @param e           异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, String url, Exception e) {
        String msg = String.format("京东平台调用异常 response: %s, url: %s", responseStr, url);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
    }

    public static void main(String[] args) {
        JdRpc rpc = new JdRpc();
        ShopDO shop = ShopDO.builder()
                .shopCode("JD_XTC")
                .apiUrl("https://api.jd.com/routerjson")
                .appKey("xxxxx")
                .appSecret("xxxxx")
                .appAccessToken("xxxxx")
                .build();

        OrderSearchInfo response = rpc.getOrder(shop, "297142098197");
        System.out.println(GsonUtil.objectToJson(response));
    }

}
