package com.xtc.marketing.adapterservice.notify.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xtc.marketing.adapterservice.config.BaseDO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
@TableName("t_receive_log")
public class ReceiveLogDO extends BaseDO {

    /**
     * 模块代码
     */
    private String moduleCode;
    /**
     * 平台代码
     */
    private String platformCode;
    /**
     * 场景代码
     */
    private String scenarioCode;
    /**
     * 数据id
     */
    private String dataId;
    /**
     * 数据
     */
    private String rawData;
    /**
     * 通知响应结果
     */
    private String responseStr;

}
