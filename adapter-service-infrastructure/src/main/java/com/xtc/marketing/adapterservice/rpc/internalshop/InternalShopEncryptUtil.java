package com.xtc.marketing.adapterservice.rpc.internalshop;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

/**
 * 内部购机平台加密工具（AES加密）
 **/
public class InternalShopEncryptUtil {

    private static final String KEYS = "ce5f6b38b319e45a89eab289d0c5f68f";

    private InternalShopEncryptUtil() {
    }

    /**
     * 二进制byte[]转十六进制string
     */
    public static String byteToHexString(byte[] bytes) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < bytes.length; i++) {
            String strHex = Integer.toHexString(bytes[i]);
            if (strHex.length() > 3) {
                sb.append(strHex.substring(6));
            } else {
                if (strHex.length() < 2) {
                    sb.append("0" + strHex);
                } else {
                    sb.append(strHex);
                }
            }
        }
        return sb.toString();
    }

    /**
     * 十六进制string转二进制byte[]
     */
    public static byte[] hexStringToByte(String s) {
        byte[] baKeyword = new byte[s.length() / 2];
        for (int i = 0; i < baKeyword.length; i++) {
            String strNum = s.substring(i * 2, i * 2 + 2);
            try {
                baKeyword[i] = (byte) (0xff & Integer.parseInt(strNum, 16));
            } catch (Exception e) {
                System.out.println("十六进制转byte发生错误！！！");
                e.printStackTrace();
            }
        }
        return baKeyword;
    }

    /**
     * 使用对称密钥进行加密
     */
    public static String encrypt(String mingwen) {
        String res = null;
        byte[] keyb = hexStringToByte(KEYS);
        SecretKeySpec sKeySpec = new SecretKeySpec(keyb, "AES");
        Cipher cipher;
        try {
            cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.ENCRYPT_MODE, sKeySpec);
            byte[] afterEncrypt = cipher.doFinal(mingwen.getBytes());
            res = byteToHexString(afterEncrypt);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;

    }

    /**
     * 使用对称密钥进行解密
     */
    public static String decrypt(String sjiami) {
        String res = null;
        byte[] keyb = hexStringToByte(KEYS);
        byte[] miwen = hexStringToByte(sjiami);
        SecretKeySpec sKeySpec = new SecretKeySpec(keyb, "AES");
        Cipher cipher;
        try {
            cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, sKeySpec);
            byte[] afterDecrypt = cipher.doFinal(miwen);
            res = new String(afterDecrypt, StandardCharsets.UTF_8);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

}
