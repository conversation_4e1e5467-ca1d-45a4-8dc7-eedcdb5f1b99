package com.xtc.marketing.adapterservice.logistics.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xtc.marketing.adapterservice.config.BaseDO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
@TableName("t_logistics_account")
public class LogisticsAccountDO extends BaseDO {

    /**
     * 公司代码
     */
    private String companyCode;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 业务账号
     */
    private String bizAccount;
    /**
     * 客户代码
     */
    private String clientCode;
    /**
     * 客户密钥
     */
    private String clientSecret;
    /**
     * 启用
     */
    private Boolean enabled;
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
    /**
     * api地址
     */
    private String apiUrl;
    /**
     * 应用session_key
     */
    private String appSessionKey;
    /**
     * 应用access_token
     */
    private String appAccessToken;
    /**
     * 应用refresh_token
     */
    private String appRefreshToken;

}
