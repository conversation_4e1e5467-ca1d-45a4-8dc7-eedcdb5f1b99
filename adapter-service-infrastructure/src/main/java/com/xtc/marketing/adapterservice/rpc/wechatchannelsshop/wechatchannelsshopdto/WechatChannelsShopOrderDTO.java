package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 微信视频号小店订单
 */
@Getter
@Setter
@ToString
public class WechatChannelsShopOrderDTO extends WechatChannelsShopBaseDTO {

    /**
     * 订单列表
     */
    @SerializedName("order_id_list")
    private List<String> orders;
    /**
     * 是否还有下一页，true:有下一页；false:已经结束，没有下一页。
     */
    @SerializedName("has_more")
    private Boolean hasMore;
    /**
     * 分页参数
     */
    @SerializedName("next_key")
    private String nextKey;

    @Override
    public String getResponseDataKey() {
        return "";
    }

}
