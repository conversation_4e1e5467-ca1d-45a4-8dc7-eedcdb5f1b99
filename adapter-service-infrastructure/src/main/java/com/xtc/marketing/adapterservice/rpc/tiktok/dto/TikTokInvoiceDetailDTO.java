package com.xtc.marketing.adapterservice.rpc.tiktok.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 抖音发票明细
 */
@Getter
@Setter
@ToString
public class TikTokInvoiceDetailDTO {

    /**
     * 产品ID
     */
    @SerializedName("ProductId")
    private String productId;
    /**
     * 产品数量
     */
    @SerializedName("ProductCount")
    private String productCount;
    /**
     * 产品名称
     */
    @SerializedName("ProductName")
    private String productName;
    /**
     * 产品价格（单位：分）
     */
    @SerializedName("ProductPrice")
    private String productPrice;
    /**
     * SKU规格列表
     */
    @SerializedName("SkuSpecs")
    private List<SkuSpecDTO> skuSpecs;
    /**
     * SKU编码
     */
    @SerializedName("SkuCode")
    private String skuCode;
    /**
     * 赠品类型
     */
    @SerializedName("GivenProductType")
    private String givenProductType;
    /**
     * 赠品价格
     */
    @SerializedName("GivenProductPrice")
    private String givenProductPrice;
    /**
     * 发票金额（单位：分）
     */
    @SerializedName("InvoiceAmount")
    private String invoiceAmount;
    /**
     * 发票金额明细
     */
    @SerializedName("InvoiceAmountDetail")
    private InvoiceAmountDetailDTO invoiceAmountDetail;
    /**
     * 货物ID
     */
    @SerializedName("CargoId")
    private String cargoId;
    /**
     * 政府补贴类别名称
     */
    @SerializedName("GovSubsidyCategoryName")
    private String govSubsidyCategoryName;
    /**
     * 政府补贴类别代码
     */
    @SerializedName("GovSubsidyCategoryCode")
    private String govSubsidyCategoryCode;
    /**
     * 政府补贴产品型号
     */
    @SerializedName("GovSubsidyProductModel")
    private String govSubsidyProductModel;
    /**
     * 政府补贴产品功率等级
     */
    @SerializedName("GovSubsidyProductPowerLevel")
    private String govSubsidyProductPowerLevel;
    /**
     * 政府补贴序列号列表
     */
    @SerializedName("GovSubsidySerialNosList")
    private List<GovSubsidySerialNoDTO> govSubsidySerialNosList;
    /**
     * 政府补贴多信息
     */
    @SerializedName("GovSubsidyMultis")
    private GovSubsidyMultisDTO govSubsidyMultis;
    /**
     * 政府补贴主要类别
     */
    @SerializedName("GovSubsidyMajorCate")
    private String govSubsidyMajorCate;
    /**
     * 是否政府补贴后门订单
     */
    @SerializedName("GovSubsidyBackDoorOrder")
    private String govSubsidyBackDoorOrder;

    /**
     * SKU规格
     */
    @Getter
    @Setter
    @ToString
    public static class SkuSpecDTO {

        /**
         * 规格名称
         */
        @SerializedName("Name")
        private String name;
        /**
         * 规格值
         */
        @SerializedName("Value")
        private String value;

    }

    /**
     * 发票金额明细
     */
    @Getter
    @Setter
    @ToString
    public static class InvoiceAmountDetailDTO {

        /**
         * 促销折扣金额（单位：分）
         */
        @SerializedName("PromotionDiscountAmount")
        private String promotionDiscountAmount;
        /**
         * 原始商品总金额（单位：分）
         */
        @SerializedName("OriginItemSumAmount")
        private String originItemSumAmount;
        /**
         * 实际退款金额（单位：分）
         */
        @SerializedName("RealRefundAmount")
        private String realRefundAmount;
        /**
         * 政府二次补贴金额（单位：分）
         */
        @SerializedName("GovernmentResaleSubsidyAmount")
        private String governmentResaleSubsidyAmount;
        /**
         * 政府二次补贴退款金额（单位：分）
         */
        @SerializedName("GovernmentResaleSubsidyRefundAmount")
        private String governmentResaleSubsidyRefundAmount;

    }

    /**
     * 政府补贴序列号数据
     */
    @Getter
    @Setter
    @ToString
    public static class GovSubsidySerialNoDTO {

        /**
         * 序列号键（如：69, SN, IMEI）
         */
        @SerializedName("Key")
        private String key;
        /**
         * 序列号值列表
         */
        @SerializedName("Value")
        private List<String> value;

    }

    /**
     * 政府补贴
     */
    @Getter
    @Setter
    @ToString
    public static class GovSubsidyMultisDTO {

        /**
         * 供应商店铺ID
         */
        @SerializedName("SupplierShopId")
        private String supplierShopId;
        /**
         * 供应商店铺名称
         */
        @SerializedName("SupplierShopName")
        private String supplierShopName;

    }

    /**
     * 校验含有key
     *
     * @param serialNo 序列号
     * @return 结果
     */
    public static boolean hasKey(TikTokInvoiceDetailDTO.GovSubsidySerialNoDTO serialNo) {
        return "69".equals(serialNo.getKey()) || "SN".equals(serialNo.getKey()) || "IMEI".equals(serialNo.getKey());
    }

}
