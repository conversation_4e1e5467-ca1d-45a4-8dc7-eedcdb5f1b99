package com.xtc.marketing.adapterservice.rpc.orderservice.enums;

import lombok.Getter;

/**
 * 订单服务快递公司
 */
@Getter
public enum OrderServiceLogisticsCompany {

    /**
     * 圆通
     */
    YTO("圆通"),
    /**
     * 京东
     */
    JD("京东"),
    /**
     * 顺丰
     */
    SF("顺丰"),
    /**
     * EMS
     */
    EMS("EMS"),
    /**
     * 申通
     */
    STO("申通");

    /**
     * 快递公司名称
     */
    private final String name;

    OrderServiceLogisticsCompany(String name) {
        this.name = name;
    }
}
