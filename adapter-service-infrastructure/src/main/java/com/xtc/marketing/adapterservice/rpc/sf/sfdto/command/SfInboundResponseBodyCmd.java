package com.xtc.marketing.adapterservice.rpc.sf.sfdto.command;

import com.google.gson.annotations.SerializedName;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.SfWarehouseBaseRequest;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class SfInboundResponseBodyCmd extends SfWarehouseBaseRequest {

    /**
     * 唯一编码
     */
    @SerializedName("TransactionId")
    private String transactionId;

    /**
     * 货主编码
     */
    @SerializedName("CompanyCode")
    private String companyCode;

    /**
     * 订单列表
     */
    @SerializedName("PurchaseOrders")
    private List<SfInboundApplyCmd> orders;

}
