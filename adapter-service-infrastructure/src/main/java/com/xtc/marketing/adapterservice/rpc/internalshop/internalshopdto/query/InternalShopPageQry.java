package com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.query;

import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;

import java.time.Duration;

/**
 * 内部购机分页查询
 */
@Data
@Builder
public class InternalShopPageQry {

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 分页数据量
     */
    private Integer pageSize;

    /**
     * 更新时间开始（秒时间戳）
     */
    private Long updateTimeStart;

    /**
     * 更新时间结束（秒时间戳，时间范围限制 24 小时）
     */
    private Long updateTimeEnd;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 校验参数非法
     *
     * @return 执行结果
     */
    public boolean checkIllegal() {
        if (ObjectUtils.anyNull(pageNo, pageSize, updateTimeStart, updateTimeEnd)) {
            return true;
        }
        if (pageNo < 1 || pageSize > 200 || updateTimeStart < 1 || updateTimeEnd < 1) {
            return true;
        }
        long oneDaySeconds = Duration.ofDays(1).getSeconds();
        long duration = updateTimeEnd - updateTimeStart;
        return duration < 1 || duration > oneDaySeconds;
    }

}
