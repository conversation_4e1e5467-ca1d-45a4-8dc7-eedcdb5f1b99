package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.notify;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * AES密码工具类
 */
public class AesUtil {

    private static final Charset CHARSET = StandardCharsets.UTF_8;
    private static final String AES = "AES";
    /**
     * 微信视频号小店使用的加密算法：AES/CBC/PKCS7Padding，由于 Java 不支持 PKCS7Padding，所以这里使用 NoPadding，并自行处理补位
     */
    private static final String CIPHER_ALGORITHM = "AES/CBC/NoPadding";

    private AesUtil() {
    }

    /**
     * 加密
     *
     * @param secret         密钥字节
     * @param plaintextBytes 明文字节
     * @return 密文字节
     */
    public static byte[] encrypt(byte[] secret, byte[] plaintextBytes) {
        try {
            Cipher cipher = generateCipher(Cipher.ENCRYPT_MODE, secret);
            return cipher.doFinal(plaintextBytes);
        } catch (Exception e) {
            throw new RuntimeException("AesUtil encrypt error", e);
        }
    }

    /**
     * 解密
     *
     * @param secret       密钥字节
     * @param encryptBytes 密文字节
     * @return 明文字节
     */
    public static byte[] decrypt(byte[] secret, byte[] encryptBytes) {
        try {
            Cipher cipher = generateCipher(Cipher.DECRYPT_MODE, secret);
            return cipher.doFinal(encryptBytes);
        } catch (Exception e) {
            throw new RuntimeException("AesUtil decrypt error", e);
        }
    }

    /**
     * 生成密码器
     *
     * @param mode   密码器工作模式
     * @param secret 密钥字节数组
     * @return 密码器
     */
    private static Cipher generateCipher(int mode, byte[] secret) {
        try {
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret, AES);
            // iv 使用密钥 secret 的前 16 个字节
            byte[] ivBytes = Arrays.copyOfRange(secret, 0, 16);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(mode, secretKeySpec, ivParameterSpec);
            return cipher;
        } catch (Exception e) {
            throw new RuntimeException("AesUtil generateCipher error", e);
        }
    }

    /**
     * PKCS7 补位
     *
     * @param count 需要进行填充补位操作的明文字节个数
     * @return 补齐用的字节数组
     */
    public static byte[] pkcs7Encode(int count) {
        return PKCS7Encoder.encodePad(count);
    }

    /**
     * PKCS7 解码
     *
     * @param decrypted 解密后的明文
     * @return 删除补位字符后的明文
     */
    public static byte[] pkcs7Decode(byte[] decrypted) {
        return PKCS7Encoder.decodePad(decrypted);
    }

    /**
     * 提供PKCS7算法的补位处理
     */
    private static class PKCS7Encoder {

        /**
         * 块大小
         */
        private static final int BLOCK_SIZE = 32;

        /**
         * 获得对明文进行补位填充的字节.
         *
         * @param count 需要进行填充补位操作的明文字节个数
         * @return 补齐用的字节数组
         */
        private static byte[] encodePad(int count) {
            // 计算需要填充的位数
            int amountToPad = BLOCK_SIZE - (count % BLOCK_SIZE);
            // 获得补位所用的字符
            char padChr = chr(amountToPad);
            StringBuilder tmp = new StringBuilder();
            for (int index = 0; index < amountToPad; index++) {
                tmp.append(padChr);
            }
            return tmp.toString().getBytes(CHARSET);
        }

        /**
         * 删除解密后明文的补位字符
         *
         * @param decrypted 解密后的明文
         * @return 删除补位字符后的明文
         */
        private static byte[] decodePad(byte[] decrypted) {
            int pad = decrypted[decrypted.length - 1];
            if (pad < 1 || pad > BLOCK_SIZE) {
                pad = 0;
            }
            return Arrays.copyOfRange(decrypted, 0, decrypted.length - pad);
        }

        /**
         * 将数字转化成ASCII码对应的字符，用于对明文进行补码
         *
         * @param chr 需要转化的数字
         * @return 转化得到的字符
         */
        private static char chr(int chr) {
            byte target = (byte) (chr & 0xFF);
            return (char) target;
        }

    }

}
