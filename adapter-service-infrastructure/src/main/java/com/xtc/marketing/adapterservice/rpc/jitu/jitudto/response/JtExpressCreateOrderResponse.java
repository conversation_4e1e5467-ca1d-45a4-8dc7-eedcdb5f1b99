package com.xtc.marketing.adapterservice.rpc.jitu.jitudto.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 极兔创建订单响应
 */
@Getter
@Setter
@ToString
public class JtExpressCreateOrderResponse {

    /**
     * 集包地
     */
    private String lastCenterName;
    /**
     * 客户订单号
     */
    private String txlogisticId;
    /**
     * 订单创建时间 yyyy-MM-dd HH:mm:ss
     */
    private String createOrderTime;
    /**
     * 运单号
     */
    private String billCode;
    /**
     * 三段码
     */
    private String sortingCode;
    /**
     * 参考总运费（数值型）
     */
    private String sumFreight;

}
