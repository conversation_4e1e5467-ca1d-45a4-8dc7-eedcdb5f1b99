package com.xtc.marketing.adapterservice.rpc.sf.util;

import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.naming.NoNameCoder;
import com.thoughtworks.xstream.io.xml.DomDriver;
import com.thoughtworks.xstream.mapper.MapperWrapper;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * xml 解析工具，XStream 官网：https://x-stream.github.io
 */
public class XStreamUtil {

    /**
     * XStream 实例缓存
     * <ul>
     * <li>每个 Class 类型都需要 new XStream() 来支持</li>
     * <li>XStream 支持单例模式是线程安全的，但是每个单例只能处理一种 Class 类型</li>
     * <li>设置缓存避免每次接口请求都 new XStream() 导致内存泄露问题</li>
     * </ul>
     */
    private static final Map<String, XStream> XSTREAM_CACHE = new ConcurrentHashMap<>();

    private XStreamUtil() {
    }

    /**
     * xml 字符串转 bean 对象
     *
     * @param xml   xml 字符串
     * @param clazz bean 类型
     * @param <T>   bean 类型
     * @return bean 对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T toBean(String xml, Class<?> clazz) {
        return (T) XStreamUtil.getInstance(clazz).fromXML(xml);
    }

    /**
     * bean 对象转 xml 字符串
     *
     * @param bean bean 对象
     * @return xml 字符串
     */
    public static String toXml(Object bean) {
        return XStreamUtil.getInstance(bean.getClass()).toXML(bean);
    }

    /**
     * 获取 XStream
     *
     * @param clazz 类型
     * @return XStream
     */
    private static XStream getInstance(Class<?> clazz) {
        String key = clazz.getName();
        if (XSTREAM_CACHE.get(key) == null) {
            XSTREAM_CACHE.put(key, XStreamUtil.newInstance(clazz));
        }
        return XSTREAM_CACHE.get(key);
    }

    /**
     * 初始化 XStream
     * 使用 DomDriver 作为 XML 解析器，如果使用 Xpp3Driver 则需要引入额外的依赖
     * 使用 NoNameCoder 避免带下划线的字段，在生成 xml 时被自动替换为双下划线
     * 配置过滤掉不能识别的节点，例如 null 节点
     *
     * @param clazz 类型
     * @return XStream
     */
    private static XStream newInstance(Class<?> clazz) {
        XStream xStream = new XStream(new DomDriver("UTF-8", new NoNameCoder())) {
            @Override
            protected MapperWrapper wrapMapper(MapperWrapper next) {
                return new MapperWrapper(next) {
                    @Override
                    public boolean shouldSerializeMember(Class definedIn, String fieldName) {
                        if (definedIn == Object.class) {
                            return false;
                        }
                        return super.shouldSerializeMember(definedIn, fieldName);
                    }
                };
            }
        };
        // 设置默认的安全防护，防止被攻击
        xStream.allowTypesByWildcard(new String[]{"com.xtc.**"});
        // 开启自动检测注解，注解说明文档：https://x-stream.github.io/annotations-tutorial.html
        xStream.autodetectAnnotations(true);
        // 配置对应的 class 类型
        xStream.processAnnotations(clazz);
        return xStream;
    }

}
