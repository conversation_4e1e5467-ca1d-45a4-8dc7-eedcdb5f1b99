package com.xtc.marketing.adapterservice.notify.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xtc.marketing.adapterservice.config.BaseDO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
@TableName("t_push_log")
public class PushLogDO extends BaseDO {

    /**
     * 业务名称
     */
    private String bizName;
    /**
     * 模块代码
     */
    private String moduleCode;
    /**
     * 平台代码
     */
    private String platformCode;
    /**
     * 场景代码
     */
    private String scenarioCode;
    /**
     * 推送地址
     */
    private String pushUrl;
    /**
     * 数据id
     */
    private String dataId;
    /**
     * 接收记录id
     */
    private Long receiveLogId;
    /**
     * 推送成功标识
     */
    private Boolean pushSuccess;
    /**
     * 推送结果
     */
    private String response;

}
