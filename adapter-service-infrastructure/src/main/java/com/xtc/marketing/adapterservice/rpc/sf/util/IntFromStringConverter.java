package com.xtc.marketing.adapterservice.rpc.sf.util;

import com.thoughtworks.xstream.converters.basic.AbstractSingleValueConverter;

import java.math.BigDecimal;

/**
 * int 类型转换器，从 String 转换为 int
 */
public class IntFromStringConverter extends AbstractSingleValueConverter {

    public boolean canConvert(Class type) {
        return type == int.class || type == Integer.class;
    }

    public Object fromString(String str) {
        BigDecimal value = new BigDecimal(str);
        return value.intValue();
    }

}
