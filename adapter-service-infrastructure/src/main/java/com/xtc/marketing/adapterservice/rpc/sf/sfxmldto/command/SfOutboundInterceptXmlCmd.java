package com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.command;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.SfRequestXmlDTO;
import lombok.*;

import java.util.Collections;
import java.util.List;

/**
 * 顺丰仓库，出库拦截
 */
@Setter
@Getter
@ToString
@Builder
@XStreamAlias("WantedOrder")
public class SfOutboundInterceptXmlCmd extends SfRequestXmlDTO {

    /**
     * 运单号
     */
    @XStreamAlias("waybillNo")
    @XStreamAsAttribute
    private String waybillNo;

    /**
     * 月结卡号
     */
    @XStreamAlias("monthlyCardNo")
    @XStreamAsAttribute
    private String monthlyCardNo;

    @XStreamAlias("serviceCode")
    @XStreamAsAttribute
    private final String serviceCode = "2";

    @XStreamAlias("role")
    @XStreamAsAttribute
    private final String role = "1";

    @XStreamAlias("payMode")
    @XStreamAsAttribute
    private final String payMode = "4";

    /**
     * 拦截后需要接收的地址
     */
    @XStreamAlias("NewDestAddress")
    private Address receiveAddress;

    @Data
    @Builder
    @XStreamAlias("NewDestAddress")
    public static class Address {

        /**
         * 联系人
         */
        @XStreamAlias("contact")
        @XStreamAsAttribute
        private String contact;

        /**
         * 联系电话
         */
        @XStreamAlias("phone")
        @XStreamAsAttribute
        private String phone;

        /**
         * 省份
         */
        @XStreamAlias("province")
        @XStreamAsAttribute
        private String province;

        /**
         * 城市
         */
        @XStreamAlias("city")
        @XStreamAsAttribute
        private String city;

        /**
         * 区县
         */
        @XStreamAlias("county")
        @XStreamAsAttribute
        private String county;

        /**
         * 详细地址
         */
        @XStreamAlias("address")
        @XStreamAsAttribute
        private String address;

    }

    @Override
    public boolean otherXml() {
        return true;
    }

    @Override
    public String toRequestXml(String requestBodyXml, String companyCode) {
        return requestBodyXml;
    }

    @Override
    public List<String> getResponseParseElements() {
        return Collections.emptyList();
    }

}
