package com.xtc.marketing.adapterservice.notify.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xtc.marketing.adapterservice.config.BaseRepository;
import com.xtc.marketing.adapterservice.notify.dataobject.PushConfigDO;
import com.xtc.marketing.adapterservice.notify.dataobject.ReceiveLogDO;
import com.xtc.marketing.adapterservice.notify.dto.query.ReceiveLogPageQry;
import com.xtc.marketing.adapterservice.notify.repository.mapper.ReceiveLogMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Repository
public class ReceiveLogRepository extends BaseRepository<ReceiveLogMapper, ReceiveLogDO> {

    /**
     * 查询接收记录列表
     *
     * @param pushConfig      推送配置
     * @param updateTimeStart 更新时间开始
     * @param updateTimeEnd   更新时间结束
     * @param limitSql        限制条数 sql
     * @return 接收记录列表
     */
    public List<ReceiveLogDO> listByPushConfig(PushConfigDO pushConfig, LocalDateTime updateTimeStart,
                                               LocalDateTime updateTimeEnd, String limitSql) {
        if (ObjectUtils.anyNull(pushConfig, updateTimeStart, updateTimeEnd, limitSql)) {
            return Collections.emptyList();
        }
        Wrapper<ReceiveLogDO> wrapper = Wrappers.<ReceiveLogDO>lambdaQuery()
                .eq(ReceiveLogDO::getModuleCode, pushConfig.getModuleCode())
                .eq(ReceiveLogDO::getPlatformCode, pushConfig.getPlatformCode())
                .eq(ReceiveLogDO::getScenarioCode, pushConfig.getScenarioCode())
                .between(ReceiveLogDO::getUpdateTime, updateTimeStart, updateTimeEnd)
                .orderByAsc(ReceiveLogDO::getUpdateTime)
                .last(StringUtils.defaultIfBlank(limitSql, DEFAULT_LIST_SIZE_LIMIT));
        return this.list(wrapper);
    }

    /**
     * 查询接收记录分页列表
     *
     * @param qry 参数
     * @return 接收记录分页列表
     */
    public IPage<ReceiveLogDO> pageBy(ReceiveLogPageQry qry) {
        Wrapper<ReceiveLogDO> wrapper = Wrappers.<ReceiveLogDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(qry.getDataId()), ReceiveLogDO::getDataId, qry.getDataId())
                .eq(qry.getId() != null, ReceiveLogDO::getId, qry.getId())
                .orderByDesc(ReceiveLogDO::getId);
        if (wrapper.isEmptyOfWhere()) {
            return qry.buildPage();
        }
        return this.page(qry.buildPage(), wrapper);
    }

}
