package com.xtc.marketing.adapterservice.rpc.kuaishou.enums;

import com.xtc.marketing.adapterservice.util.DateUtil;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 快手平台快递公司
 */
@Getter
public enum KuaishouLogisticsCompany {
    /**
     * 顺丰
     */
    SF("SF", 4, "7698089145", "2",
            null, null,
            "", "", "{\"isvClientCode\":\"7698089145\"}", "https://u2-401.ecukwai.com/kos/nlav11586/EBST-EBSTO28.xml"),
    /**
     * 圆通
     */
    YTO("YTO", 6, "1030004233095", "",
            null, null,
            "754096", "广东省东莞市长安", "", "https://u2-401.ecukwai.com/kos/nlav11586/EBST-EBSTO12.xml"),
    /**
     * EMS
     */
    EMS("EMS", 2, "1030004233095", "",
            null, null,
            "", "", "{\"oneBillFeeType\":\"1\"}", "https://u2-401.ecukwai.com/kos/nlav11586/EBST-EBSTO30.xml"),
    /**
     * 京东
     */
    JD("JD", 14, "020K2415994", "ed-m-0001",
            30L, 360L,
            "", "", "", "https://u1-401.ecukwai.com/kos/nlav11586/EBST-EBSTO27.xml");

    /**
     * 快递公司名称
     */
    private final String name;
    /**
     * 公司代码
     */
    private final Integer code;
    /**
     * 月结卡号
     */
    private final String account;
    /**
     * 快递产品类型
     */
    private final String productCode;
    /**
     * 预约上门取件开始时间
     */
    private final Long reserveTime;
    /**
     * 预约上门取件结束时间
     */
    private final Long reserveEndTime;
    /**
     * 网点代码
     */
    private final String siteCode;
    /**
     * 网点名称
     */
    private final String siteName;
    /**
     * 扩展信息
     */
    private final String extData;
    /**
     * 电子面单模板地址
     */
    private final String templateUrl;

    KuaishouLogisticsCompany(String name, Integer code, String account, String productCode, Long reserveTime, Long reserveEndTime,
                             String siteCode, String siteName, String extData, String templateUrl) {
        this.name = name;
        this.code = code;
        this.account = account;
        this.productCode = productCode;
        this.reserveTime = reserveTime == null ? null : DateUtil.toEpochMilli(LocalDateTime.now().plusMinutes(reserveTime));
        this.reserveEndTime = reserveEndTime == null ? null : DateUtil.toEpochMilli(LocalDateTime.now().plusMinutes(reserveEndTime));
        this.siteCode = siteCode;
        this.siteName = siteName;
        this.extData = extData;
        this.templateUrl = templateUrl;
    }
}
