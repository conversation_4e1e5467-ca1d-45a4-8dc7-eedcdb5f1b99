package com.xtc.marketing.adapterservice.rpc.jitu.jitudto.request;

import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.JtExpressBaseRequest;
import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.response.JtExpressPrintResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 极兔面单打印请求
 */
@Getter
@Setter
@ToString
public class JtExpressPrintRequest extends JtExpressBaseRequest<JtExpressPrintResponse> {

    /**
     * 运单号
     */
    private String billCode;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 是否隐私面单 true 是 false 否 不传默认为否
     */
    private Boolean isPrivacyFlag;
    /**
     * 面单规格 1：76*130mm
     */
    private Integer noodleSpecification;

    @Override
    public Class<JtExpressPrintResponse> getResponseClass() {
        return JtExpressPrintResponse.class;
    }

    @Override
    public String getApiPath() {
        return "/webopenplatformapi/api/order/printOrder";
    }

}
