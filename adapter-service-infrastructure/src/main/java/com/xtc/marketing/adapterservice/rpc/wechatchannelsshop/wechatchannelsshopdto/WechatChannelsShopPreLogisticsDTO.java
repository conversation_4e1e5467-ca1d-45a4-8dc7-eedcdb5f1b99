package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 微信视频号小店预取号
 */
@Getter
@Setter
@ToString
public class WechatChannelsShopPreLogisticsDTO extends WechatChannelsShopBaseDTO {

    /**
     * 电子面单订单id
     */
    @SerializedName("ewaybill_order_id")
    private String waybillOrderId;

    @Override
    public String getResponseDataKey() {
        return "";
    }

}
