package com.xtc.marketing.adapterservice.rpc.sf.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;

/**
 * 退库库存类型
 */
@Getter
@AllArgsConstructor
public enum SfInventoryStatus {
    /**
     * 好料
     */
    GOOD("10"),
    /**
     * 残料
     */
    BAD("20");

    private final String code;

    /**
     * 获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static Optional<SfInventoryStatus> of(String value) {
        if (value == null) {
            return Optional.empty();
        }
        try {
            return Optional.of(SfInventoryStatus.valueOf(value.toUpperCase()));
        } catch (IllegalArgumentException e) {
            return Optional.empty();
        }
    }

    /**
     * 判断相等
     *
     * @param name 枚举值
     * @return 执行结果
     */
    public boolean equalsName(String name) {
        return this.name().equalsIgnoreCase(name);
    }
}
