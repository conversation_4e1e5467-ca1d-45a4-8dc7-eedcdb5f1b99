package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 微信视频号小店token
 */
@Getter
@Setter
@ToString
public class WechatChannelsShopRefreshTokenDTO {

    /**
     * 接口调用令牌
     */
    @SerializedName("access_token")
    private String accessToken;

    /**
     * 令牌有效时间（单位：秒）
     */
    @SerializedName("expires_in")
    private Long expiresIn;

}
