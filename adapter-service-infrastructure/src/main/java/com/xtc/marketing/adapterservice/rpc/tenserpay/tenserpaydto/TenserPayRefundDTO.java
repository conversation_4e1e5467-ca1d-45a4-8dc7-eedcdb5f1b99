package com.xtc.marketing.adapterservice.rpc.tenserpay.tenserpaydto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class TenserPayRefundDTO extends TenserPayBaseDTO {

    /**
     * 平台退款订单号
     */
    private String refundOrderNo;

    /**
     * 商户退款订单号
     * 发起退款方的退款订单号
     */
    private String outRefundOrderNo;

    /**
     * 退款金额
     */
    private String refundAmount;

    /**
     * 退款状态
     */
    private RefundState status;

    /**
     * 退款状态描述
     */
    private String description;

    /**
     * 退款成功时间
     * 退款通知格式：yyyyMMddHHmmss
     * 退款查询格式：yyyy-MM-dd HH:mm:ss
     */
    private String refundSuccessTime;

    /**
     * 原交易订单号
     */
    private String orderNo;

    /**
     * 原交易商户订单号
     */
    private String outOrderNo;

    /**
     * 原交易金额
     */
    private String totalAmount;

    /**
     * 退款状态
     */
    public enum RefundState {
        /**
         * 退款成功
         */
        S,
        /**
         * 退款处理中
         */
        P,
        /**
         * 退款失败
         */
        F
    }

}
