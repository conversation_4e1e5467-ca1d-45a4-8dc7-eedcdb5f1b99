package com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto;

import com.google.gson.JsonElement;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class InternalShopBaseDTO {

    /**
     * 请求成功代码
     */
    public static final String SUCCESS_CODE = "000001";

    private String code;

    private String desc;

    private JsonElement data;

    public boolean isFailure() {
        return !SUCCESS_CODE.equals(code);
    }

}
