package com.xtc.marketing.adapterservice.rpc.sto.stodto.command;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 申通取消参数
 */
@Getter
@Setter
@ToString
public class StoCancelOrderCmd {

    /**
     * 订单号（客户系统自己生成，唯一）
     */
    private String sourceOrderId;
    /**
     * 快递号
     */
    @SerializedName("billCode")
    private String waybillNo;
    /**
     * 订单类型
     */
    @SerializedName("orderType")
    private String orderType = "01";
    /**
     * 订单来源（订阅服务时填写的来源编码）
     */
    private String orderSource;
    /**
     * 备注
     */
    private String remark;

}
