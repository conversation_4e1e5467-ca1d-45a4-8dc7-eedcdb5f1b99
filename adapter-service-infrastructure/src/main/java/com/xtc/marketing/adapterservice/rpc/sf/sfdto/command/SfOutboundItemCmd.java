package com.xtc.marketing.adapterservice.rpc.sf.sfdto.command;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SfOutboundItemCmd {

    /**
     * skuId
     */
    @SerializedName("SkuNo")
    private String skuId;

    /**
     * 数量
     */
    @SerializedName("ItemQuantity")
    private String itemQuantity;

}
