package com.xtc.marketing.adapterservice.rpc.internalshop;

import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.InternalShopBaseDTO;
import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.InternalShopOrderDTO;
import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.InternalShopPageResponse;
import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.InternalShopRefundDTO;
import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.command.InternalShopShippingCmd;
import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.query.InternalShopPageQry;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpServerErrorException;

import java.rmi.RemoteException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

/**
 * 内部购机商城RPC
 */
@Slf4j
@Component
public class InternalShopRpc {

    /**
     * 生产环境地址
     */
    private static final String DOMAIN = "https://internal-shop.okii.com/seller-api";

    /**
     * API：查询订单分页列表
     */
    private static final String API_PAGE_ORDERS = "/seller/trade/orders/getOrderByShop" +
            "?seller_id=%s&key=%s&sign=%s&page_no=%s&page_size=%s&last_update_start_time=%s&last_update_end_time=%s";
    /**
     * API：查询订单
     */
    private static final String API_GET_ORDER = "/seller/trade/orders/getOrderByShop" +
            "?seller_id=%s&key=%s&sign=%s&page_no=1&page_size=10&order_sn=%s";
    /**
     * API：查询退款单分页列表
     */
    private static final String API_PAGE_REFUNDS = "/seller/after-sales/getRefundListBySystem" +
            "?seller_id=%s&page_no=%s&page_size=%s&last_update_start_time=%s&last_update_end_time=%s&order_sn=%s";
    /**
     * API：查询退款单
     */
    private static final String API_GET_REFUND = "/seller/after-sales/getRefundListBySystem" +
            "?seller_id=%s&page_no=1&page_size=100&sn=%s";
    /**
     * API：订单发货
     */
    private static final String API_SHIPPING = "/seller/trade/orders/deliveryBySystem" +
            "?order_sn=%s&ship_no=%s&logi_id=%s&logi_name=%s&seller_name=%s";
    /**
     * API：订单无物流发货
     */
    private static final String API_DUMMY_SHIPPING = "/seller/trade/orders/%s/deliveryWithNoShip" +
            "?no_need=1&seller_name=%s";
    /**
     * API：商家备注
     */
    private static final String API_SELLER_MEMO = "/seller/trade/orders/editRemarkByErp" +
            "?sn=%s&remark=%s";

    /**
     * 查询订单分页列表
     *
     * @param shop 店铺
     * @param qry  参数
     * @return 订单分页列表
     */
    public InternalShopPageResponse<InternalShopOrderDTO> pageOrders(ShopDO shop, InternalShopPageQry qry) {
        if (qry.checkIllegal()) {
            return new InternalShopPageResponse<>();
        }

        long nowEpochSecond = DateUtil.nowEpochSecond();
        String sign = InternalShopEncryptUtil.encrypt(shop.getAppKey() + ":" + nowEpochSecond);
        String urlSuffix = String.format(API_PAGE_ORDERS, shop.getAppKey(), nowEpochSecond, sign,
                qry.getPageNo(), qry.getPageSize(), qry.getUpdateTimeStart(), qry.getUpdateTimeEnd());
        InternalShopBaseDTO baseDTO = this.call(urlSuffix, HttpUtil::get);

        // json 返回值嵌套解析
        InternalShopPageResponse<InternalShopOrderDTO> response = GsonUtil.jsonToBean(baseDTO.getData().toString(),
                InternalShopPageResponse.class, InternalShopOrderDTO.class);

        // 订单区分平台，通过订单号前缀判断
        List<InternalShopOrderDTO> data = response.getData().stream()
                .filter(order -> order.getSn().startsWith(shop.getShopId()))
                .collect(Collectors.toList());
        response.setData(data);
        return response;
    }

    /**
     * 查询订单
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 订单
     */
    public InternalShopOrderDTO getOrder(ShopDO shop, String orderNo) {
        if (orderNo == null) {
            throw illegalArgumentException();
        }

        long nowEpochSecond = DateUtil.nowEpochSecond();
        String sign = InternalShopEncryptUtil.encrypt(shop.getAppKey() + ":" + nowEpochSecond);
        String urlSuffix = String.format(API_GET_ORDER, shop.getAppKey(), nowEpochSecond, sign, orderNo);
        InternalShopBaseDTO baseDTO = this.call(urlSuffix, HttpUtil::get);

        InternalShopPageResponse<InternalShopOrderDTO> response = GsonUtil.jsonToBean(baseDTO.getData().toString(),
                InternalShopPageResponse.class, InternalShopOrderDTO.class);

        // 订单区分平台，通过订单号前缀判断
        return Optional.ofNullable(response)
                .map(InternalShopPageResponse::getData)
                .flatMap(list -> list.stream().filter(order -> order.getSn().startsWith(shop.getShopId())).findFirst())
                .orElse(null);
    }

    /**
     * 订单发货
     *
     * @param shop 店铺
     * @param cmd  参数
     */
    public void shipping(ShopDO shop, InternalShopShippingCmd cmd) {
        if (cmd.checkIllegal()) {
            throw illegalArgumentException();
        }
        String encryptOrderNo = InternalShopEncryptUtil.encrypt(cmd.getOrderNo());
        String urlSuffix = String.format(API_SHIPPING, encryptOrderNo, cmd.getWaybillNo(),
                cmd.getLogisticsCompany().getCode(), cmd.getLogisticsCompany().getName(), shop.getShopName());
        this.call(urlSuffix, HttpUtil::post);
    }

    /**
     * 订单无物流发货
     *
     * @param orderNo 订单号
     */
    public void dummyShipping(ShopDO shop, String orderNo) {
        if (orderNo == null) {
            throw illegalArgumentException();
        }
        String urlSuffix = String.format(API_DUMMY_SHIPPING, orderNo, shop.getShopName());
        this.call(urlSuffix, HttpUtil::post);
    }

    /**
     * 商家备注
     *
     * @param orderNo 订单号
     * @param remark  备注
     */
    public void sellerMemo(String orderNo, String remark) {
        if (orderNo == null || remark == null) {
            throw illegalArgumentException();
        }
        String encryptOrderNo = InternalShopEncryptUtil.encrypt(orderNo);
        String urlSuffix = String.format(API_SELLER_MEMO, encryptOrderNo, remark);
        this.call(urlSuffix, HttpUtil::post);
    }

    /**
     * 查询退款单分页列表
     *
     * @param shop 店铺
     * @param qry  参数
     * @return 退款单分页列表
     */
    public InternalShopPageResponse<InternalShopRefundDTO> pageRefunds(ShopDO shop, InternalShopPageQry qry) {
        if (qry.checkIllegal()) {
            return null;
        }
        String urlSuffix = String.format(API_PAGE_REFUNDS, shop.getAppKey(), qry.getPageNo(), qry.getPageSize(),
                qry.getUpdateTimeStart(), qry.getUpdateTimeEnd(), StringUtils.defaultString(qry.getOrderSn()));
        InternalShopBaseDTO baseDTO = this.call(urlSuffix, HttpUtil::get);
        String dataJson = InternalShopEncryptUtil.decrypt(baseDTO.getData().getAsString());
        log.info("内部购机返回值: {}", dataJson);

        InternalShopPageResponse<InternalShopRefundDTO> response = GsonUtil.jsonToBean(dataJson,
                InternalShopPageResponse.class, InternalShopRefundDTO.class);

        // 订单区分平台，通过订单号前缀判断
        List<InternalShopRefundDTO> data = response.getData().stream()
                .filter(order -> order.getRefund() != null && order.getRefund().getOrderSn().startsWith(shop.getShopId()))
                .collect(Collectors.toList());
        response.setData(data);
        return response;
    }

    /**
     * 查询退款单
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 退款单
     */
    public InternalShopRefundDTO getRefund(ShopDO shop, String orderNo) {
        if (orderNo == null) {
            throw illegalArgumentException();
        }
        String urlSuffix = String.format(API_GET_REFUND, shop.getAppKey(), orderNo);
        InternalShopBaseDTO baseDTO = this.call(urlSuffix, HttpUtil::get);
        String dataJson = InternalShopEncryptUtil.decrypt(baseDTO.getData().getAsString());
        log.info("内部购机返回值: {}", dataJson);

        InternalShopPageResponse<InternalShopRefundDTO> response = GsonUtil.jsonToBean(dataJson,
                InternalShopPageResponse.class, InternalShopRefundDTO.class);

        // 订单区分平台，通过订单号前缀判断
        return Optional.ofNullable(response)
                .map(InternalShopPageResponse::getData)
                .flatMap(list -> list.stream().filter(order -> order.getRefund() != null)
                        .filter(order -> order.getRefund().getOrderSn().startsWith(shop.getShopId())).findFirst())
                .orElse(null);
    }

    /**
     * 接口调用
     *
     * @param urlSuffix 接口地址
     * @param httpCall  接口调用逻辑
     * @return 返回值
     */
    private InternalShopBaseDTO call(String urlSuffix, UnaryOperator<String> httpCall) {
        return this.call(urlSuffix, InternalShopBaseDTO.class, httpCall);
    }

    /**
     * 接口调用
     *
     * @param urlSuffix     接口地址
     * @param responseClass 响应类型
     * @param httpCall      接口调用逻辑
     * @param <T>           继承 InternalShopBaseDTO 的类型
     * @return 返回值
     */
    private <T extends InternalShopBaseDTO> T call(String urlSuffix, Class<T> responseClass, UnaryOperator<String> httpCall) {
        String url = DOMAIN + urlSuffix;
        String response = "";
        InternalShopBaseDTO baseDTO;
        try {
            response = httpCall.apply(url);
            baseDTO = GsonUtil.jsonToBean(response, InternalShopBaseDTO.class);
            if (baseDTO.isFailure()) {
                throw new RemoteException("RPC返回异常状态码");
            }

            String data = baseDTO.getData().toString();
            boolean jsonToBean = responseClass != InternalShopBaseDTO.class && baseDTO.getData().isJsonObject();
            T resultDTO = jsonToBean ? GsonUtil.jsonToBean(data, responseClass) : responseClass.newInstance();
            BeanUtils.copyProperties(baseDTO, resultDTO);
            return resultDTO;
        } catch (Exception e) {
            if (e instanceof HttpServerErrorException) {
                response = ((HttpServerErrorException) e).getResponseBodyAsString();
            }
            String msg = String.format("内部购机调用异常 response: %s, url: %s", response, url);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, response, msg, e);
        }
    }

    /**
     * 参数不合法异常
     *
     * @return 异常
     */
    private SysException illegalArgumentException() {
        return SysException.of(SysErrorCode.S_RPC_ERROR, "参数不合法");
    }

    public static void main(String[] args) {
        ShopDO shop = new ShopDO();
        shop.setAppKey("19");

        LocalDateTime updateTimeEnd = LocalDateTime.now();
        LocalDateTime updateTimeStart = updateTimeEnd.minusDays(1);
        InternalShopPageQry qry = InternalShopPageQry.builder()
                .pageNo(1)
                .pageSize(10)
                .updateTimeStart(DateUtil.toEpochSecond(updateTimeStart))
                .updateTimeEnd(DateUtil.toEpochSecond(updateTimeEnd))
                .build();

        InternalShopRpc rpc = new InternalShopRpc();
        InternalShopPageResponse<InternalShopOrderDTO> pageOrders = rpc.pageOrders(shop, qry);
        log.debug("{}", pageOrders);

        String dataJson = GsonUtil.objectToJson(pageOrders.getData());
        List<Map<String, Object>> listMap = GsonUtil.jsonToListMap(dataJson);
        log.debug("{}", listMap);
    }

}
