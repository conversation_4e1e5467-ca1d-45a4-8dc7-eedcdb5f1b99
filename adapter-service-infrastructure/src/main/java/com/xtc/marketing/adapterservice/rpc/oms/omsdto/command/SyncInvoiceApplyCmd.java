package com.xtc.marketing.adapterservice.rpc.oms.omsdto.command;

import com.google.gson.annotations.Expose;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.OmsBaseRequest;
import lombok.*;
import org.springframework.http.HttpMethod;

/**
 * 发票申请同步参数
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SyncInvoiceApplyCmd extends OmsBaseRequest<Void> {

    /**
     * 平台id
     */
    private String platformId;
    /**
     * 订单号
     */
    private String tradeId;
    /**
     * 事件（仅在请求url做标识，并且 Gson 序列化与反序列化都不处理）
     */
    @Expose
    private String event;

    @Override
    public String getApiPath() {
        String eventParam = event == null ? "" : "?event=" + event;
        return "/api/invoice-apply/import-sync" + eventParam;
    }

    @Override
    public HttpMethod getRequestMethod() {
        return HttpMethod.POST;
    }

    @Override
    public Class<Void> getResponseClass() {
        return Void.class;
    }

}
