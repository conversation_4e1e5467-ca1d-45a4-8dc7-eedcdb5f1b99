package com.xtc.marketing.adapterservice.rpc.pdd.pdddto;

import com.pdd.pop.ext.fasterxml.jackson.annotation.JsonProperty;
import com.pdd.pop.sdk.common.util.JsonUtil;
import com.pdd.pop.sdk.http.HttpMethod;
import com.pdd.pop.sdk.http.PopBaseHttpRequest;
import com.pdd.pop.sdk.http.api.pop.response.PddFdsWaybillGetResponse;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 拼多多一件代发电子面单，生成面单参数
 */
@Getter
@Setter
public class PddFdsWaybillGetRequest extends PopBaseHttpRequest<PddFdsWaybillGetResponse> {

    /**
     * 入参
     */
    @JsonProperty("param_fds_waybill_get_request")
    private ParamFdsWaybillGetRequest paramFdsWaybillGetRequest;

    public PddFdsWaybillGetRequest() {
    }

    public String getVersion() {
        return "V1";
    }

    public String getDataType() {
        return "JSON";
    }

    public Integer getPlatform() {
        return 0;
    }

    public String getType() {
        return "pdd.fds.waybill.get";
    }

    public HttpMethod getHttpMethod() {
        return HttpMethod.POST;
    }

    public Class<PddFdsWaybillGetResponse> getResponseClass() {
        return PddFdsWaybillGetResponse.class;
    }

    protected void setUserParams(Map<String, String> params) {
        this.setUserParam(params, "param_fds_waybill_get_request", this.paramFdsWaybillGetRequest);
    }

    @Getter
    @Setter
    public static class ParamFdsWaybillGetRequestTradeOrderInfoDtosItemPackageInfoItemsItem {

        /**
         * 数量
         */
        @JsonProperty("count")
        private Integer count;

        /**
         * 商品名称
         */
        @JsonProperty("name")
        private String name;

        public ParamFdsWaybillGetRequestTradeOrderInfoDtosItemPackageInfoItemsItem() {
        }

    }

    @Getter
    @Setter
    public static class ParamFdsWaybillGetRequestTradeOrderInfoDtosItemPackageInfo {

        /**
         * 快运货品描述
         */
        @JsonProperty("goods_description")
        private String goodsDescription;

        /**
         * 包裹id,拆合单使用
         */
        @JsonProperty("id")
        private String id;

        /**
         * 商品信息,数量限制为100
         */
        @JsonProperty("items")
        private List<ParamFdsWaybillGetRequestTradeOrderInfoDtosItemPackageInfoItemsItem> items;

        /**
         * 快运包装方式描述
         */
        @JsonProperty("packaging_description")
        private String packagingDescription;

        /**
         * 子母件总包裹数
         */
        @JsonProperty("total_packages_count")
        private String totalPackagesCount;

        /**
         * 体积, 单位 ml
         */
        @JsonProperty("volume")
        private Integer volume;

        /**
         * 重量,单位 g
         */
        @JsonProperty("weight")
        private Integer weight;

        public ParamFdsWaybillGetRequestTradeOrderInfoDtosItemPackageInfo() {
        }

        public String toString() {
            return JsonUtil.transferToJson(this);
        }

    }

    @Getter
    @Setter
    public static class ParamFdsWaybillGetRequestTradeOrderInfoDtosItemOrderInfoTradeOrderListItem {

        /**
         * 店铺id
         */
        @JsonProperty("mall_mask_id")
        private String mallMaskId;

        /**
         * 订单id
         */
        @JsonProperty("order_mask_sn")
        private String orderMaskSn;

        public ParamFdsWaybillGetRequestTradeOrderInfoDtosItemOrderInfoTradeOrderListItem() {
        }

        public String toString() {
            return JsonUtil.transferToJson(this);
        }

    }

    @Getter
    @Setter
    public static class ParamFdsWaybillGetRequestTradeOrderInfoDtosItemOrderInfo {

        /**
         * 订单渠道平台编码
         */
        @JsonProperty("order_channels_type")
        private String orderChannelsType;

        /**
         * 订单列表
         */
        @JsonProperty("trade_order_list")
        private List<ParamFdsWaybillGetRequestTradeOrderInfoDtosItemOrderInfoTradeOrderListItem> tradeOrderList;

        public ParamFdsWaybillGetRequestTradeOrderInfoDtosItemOrderInfo() {
        }

        public String toString() {
            return JsonUtil.transferToJson(this);
        }

    }

    @Getter
    @Setter
    public static class ParamFdsWaybillGetRequestTradeOrderInfoDtosItem {

        /**
         * 物流服务内容链接
         */
        @JsonProperty("logistics_services")
        private String logisticsServices;

        /**
         * 请求id
         */
        @JsonProperty("object_id")
        private String objectId;

        /**
         * 订单信息
         */
        @JsonProperty("order_info")
        private ParamFdsWaybillGetRequestTradeOrderInfoDtosItemOrderInfo orderInfo;

        /**
         * 包裹信息
         */
        @JsonProperty("package_info")
        private ParamFdsWaybillGetRequestTradeOrderInfoDtosItemPackageInfo packageInfo;

        /**
         * 标准模板模板URL
         */
        @JsonProperty("template_url")
        private String templateUrl;

        /**
         * 使用者ID
         */
        @JsonProperty("user_id")
        private Long userId;

        public ParamFdsWaybillGetRequestTradeOrderInfoDtosItem() {
        }

        public String toString() {
            return JsonUtil.transferToJson(this);
        }

    }

    @Getter
    @Setter
    public static class ParamFdsWaybillGetRequestSenderAddress {

        /**
         * 城市，仅支持非空值
         */
        @JsonProperty("city")
        private String city;

        /**
         * 国家，支持空值
         */
        @JsonProperty("country")
        private String country;

        /**
         * 详细地址，仅支持非空值
         */
        @JsonProperty("detail")
        private String detail;

        /**
         * 区，仅支持非空值
         */
        @JsonProperty("district")
        private String district;

        /**
         * 省，仅支持非空值
         */
        @JsonProperty("province")
        private String province;

        /**
         * 镇，支持空值
         */
        @JsonProperty("town")
        private String town;

        public ParamFdsWaybillGetRequestSenderAddress() {
        }

        public String toString() {
            return JsonUtil.transferToJson(this);
        }

    }

    @Getter
    @Setter
    public static class ParamFdsWaybillGetRequestSender {

        /**
         * 发货人地址
         */
        @JsonProperty("address")
        private ParamFdsWaybillGetRequestSenderAddress address;

        /**
         * 发货人手机号码
         */
        @JsonProperty("mobile")
        private String mobile;

        /**
         * 发货人姓名
         */
        @JsonProperty("name")
        private String name;

        /**
         * 发货人电话号码
         */
        @JsonProperty("phone")
        private String phone;

        public ParamFdsWaybillGetRequestSender() {
        }

        public String toString() {
            return JsonUtil.transferToJson(this);
        }

    }

    @Getter
    @Setter
    public static class ParamFdsWaybillGetRequest {

        /**
         * 发货人信息
         */
        @JsonProperty("sender")
        private ParamFdsWaybillGetRequestSender sender;

        /**
         * 请求面单信息
         */
        @JsonProperty("trade_order_info_dtos")
        private List<ParamFdsWaybillGetRequestTradeOrderInfoDtosItem> tradeOrderInfoDtos;

        /**
         * 物流公司编码
         */
        @JsonProperty("wp_code")
        private String wpCode;

        public String toString() {
            return JsonUtil.transferToJson(this);
        }

    }

}
