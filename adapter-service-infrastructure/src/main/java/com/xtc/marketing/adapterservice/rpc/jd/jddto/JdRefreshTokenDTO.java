package com.xtc.marketing.adapterservice.rpc.jd.jddto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * 京东刷新 access_token
 */
@Data
public class JdRefreshTokenDTO {

    /**
     * 接口调用令牌
     */
    @SerializedName("access_token")
    private String accessToken;

    /**
     * refresh_token 是用来程序方式延长 access_token 有效期, refresh_token 调用次数限制为 500次/月
     */
    @SerializedName("refresh_token")
    private String refreshToken;

    /**
     * 令牌有效时间（单位：秒）
     */
    @SerializedName("expires_in")
    private Long expiresIn;

    /**
     * 用户授权的作用域，使用英文逗号【,】分隔
     */
    private String scope;

    /**
     * 授权用户唯一标识
     */
    @SerializedName("open_id")
    private String openId;

}
