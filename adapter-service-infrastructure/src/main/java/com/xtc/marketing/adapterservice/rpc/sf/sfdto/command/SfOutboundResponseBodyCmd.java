package com.xtc.marketing.adapterservice.rpc.sf.sfdto.command;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SfOutboundResponseBodyCmd {

    /**
     * 唯一编码
     */
    @SerializedName("TransactionId")
    private String transactionId;

    /**
     * 货主编码
     */
    @SerializedName("CompanyCode")
    private String companyCode;

    /**
     * 订单列表
     */
    @SerializedName("SaleOrders")
    private List<SfOutboundCmd> orders;

}
