package com.xtc.marketing.adapterservice.shop.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xtc.marketing.adapterservice.config.BaseRepository;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.repository.mapper.ShopMapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Repository
public class ShopRepository extends BaseRepository<ShopMapper, ShopDO> {

    /**
     * 查询店铺
     *
     * @param shopCode 店铺代码
     * @return 店铺
     */
    public Optional<ShopDO> getByShopCode(String shopCode) {
        return getBy(ShopDO::getShopCode, shopCode);
    }

    /**
     * 查询店铺列表，开启刷新 token 任务并且过期时间小于等于指定时间
     *
     * @param expireTime 过期时间
     * @return 店铺列表
     */
    public List<ShopDO> listEnabledTokenRefresh(LocalDateTime expireTime) {
        if (expireTime == null) {
            return Collections.emptyList();
        }
        Wrapper<ShopDO> wrapper = Wrappers.<ShopDO>lambdaQuery()
                .eq(ShopDO::getEnabled, true)
                .eq(ShopDO::getEnabledTokenRefresh, true)
                .le(ShopDO::getAppExpireTime, expireTime)
                .orderByAsc(ShopDO::getId)
                .last(DEFAULT_LIST_SIZE_LIMIT);
        return this.list(wrapper);
    }

    /**
     * 查询店铺
     *
     * @param agentCode 代理代码
     * @return 店铺
     */
    public Optional<ShopDO> getByAgentCode(String agentCode) {
        if (agentCode == null) {
            return Optional.empty();
        }
        Wrapper<ShopDO> wrapper = Wrappers.<ShopDO>lambdaQuery()
                .likeRight(ShopDO::getAgentCode, agentCode)
                .orderByAsc(ShopDO::getId)
                .last(LIMIT_ONE);
        ShopDO one = this.getOne(wrapper);
        return Optional.ofNullable(one);
    }

    /**
     * 查询店铺
     *
     * @param shopId 店铺id
     * @return 店铺
     */
    public Optional<ShopDO> getByShopId(String shopId) {
        return getBy(ShopDO::getShopId, shopId);
    }

    /**
     * 查询店铺
     *
     * @param appKey 应用key
     * @return 店铺
     */
    public Optional<ShopDO> getByAppKey(String appKey) {
        return getBy(ShopDO::getAppKey, appKey);
    }

}
