package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop;

import com.google.common.collect.ImmutableList;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.*;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.command.WechatChannelsShopLogisticsCreateCmd;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.command.WechatChannelsShopShippingCmd;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.query.WechatChannelsShopOrderPageQry;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.query.WechatChannelsShopRefreshTokenQry;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.query.WechatChannelsShopRefundPageQry;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.rmi.RemoteException;
import java.util.Optional;
import java.util.function.Function;

/**
 * 微信视频号小店RPC
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class WechatChannelsShopRpc {

    /**
     * 微信视频号接口域名
     */
    private static final String DOMAIN = "https://api.weixin.qq.com";
    /**
     * 获取稳定版AccessToken
     */
    private static final String API_REFRESH_TOKEN = "/cgi-bin/stable_token";
    /**
     * 解密订单
     */
    private static final String API_ORDER_DECRYPT = "/channels/ec/order/sensitiveinfo/decode";
    /**
     * 获取订单分页列表
     */
    private static final String API_ORDER_PAGE = "/channels/ec/order/list/get";
    /**
     * 获取订单详情
     */
    private static final String API_ORDER_GET = "/channels/ec/order/get";
    /**
     * 修改订单备注
     */
    private static final String API_ORDER_UPDATE = "/channels/ec/order/merchantnotes/update";
    /**
     * 发货
     */
    private static final String API_ORDER_SHIPPING = "/channels/ec/order/delivery/send";
    /**
     * 获取退款单分页列表
     */
    private static final String API_AFTER_SALE_PAGE = "/channels/ec/aftersale/getaftersalelist";
    /**
     * 获取退款单
     */
    private static final String API_AFTER_SALE_GET = "/channels/ec/aftersale/getaftersaleorder";
    /**
     * 电子面单预取号
     */
    private static final String API_LOGISTICS_PRE_CALL = "/channels/ec/logistics/ewaybill/biz/order/precreate";
    /**
     * 电子面单取号
     */
    private static final String API_LOGISTICS_CALL = "/channels/ec/logistics/ewaybill/biz/order/create";
    /**
     * 执行重试逻辑的 token 异常
     */
    private static final ImmutableList<String> RETRY_TOKEN_EXCEPTION = ImmutableList.of(
            "access_token is invalid or not latest",
            "access_token missing",
            "access_token expired"
    );

    /**
     * 刷新AccessToken
     *
     * @param shop 店铺
     * @return AccessToken
     */
    public WechatChannelsShopRefreshTokenDTO refreshToken(ShopDO shop) {
        WechatChannelsShopRefreshTokenQry qry = new WechatChannelsShopRefreshTokenQry();
        qry.setAppid(shop.getAppKey());
        qry.setSecret(shop.getAppSecret());
        qry.setForceRefresh(false);
        String response = HttpUtil.post(DOMAIN + API_REFRESH_TOKEN, qry);
        return GsonUtil.jsonToBean(response, WechatChannelsShopRefreshTokenDTO.class);
    }

    /**
     * 解密订单
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 解密订单
     */
    public WechatChannelsShopDecryptDTO orderDecrypt(ShopDO shop, String orderNo) {
        JsonObject body = new JsonObject();
        body.addProperty("order_id", orderNo);
        return this.call(shop, API_ORDER_DECRYPT, body, WechatChannelsShopDecryptDTO.class);
    }

    /**
     * 订单列表
     *
     * @param shop 店铺
     * @param qry  参数
     * @return 解密订单
     */
    public WechatChannelsShopOrderDTO pageOrders(ShopDO shop, WechatChannelsShopOrderPageQry qry) {
        return this.call(shop, API_ORDER_PAGE, qry, WechatChannelsShopOrderDTO.class);
    }

    /**
     * 获取订单详情
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 解密订单
     */
    public WechatChannelsShopOrderItemDTO getOrder(ShopDO shop, String orderNo) {
        JsonObject body = new JsonObject();
        body.addProperty("order_id", orderNo);
        body.addProperty("encode_sensitive_info", false);
        return this.call(shop, API_ORDER_GET, body, WechatChannelsShopOrderItemDTO.class);
    }

    /**
     * 订单发货
     *
     * @param shop 店铺
     * @param cmd  参数
     * @return 执行结果
     */
    public boolean orderShipping(ShopDO shop, WechatChannelsShopShippingCmd cmd) {
        WechatChannelsShopShippingDTO dto = this.call(shop, API_ORDER_SHIPPING, cmd, WechatChannelsShopShippingDTO.class);
        return dto.isSucceed();
    }

    /**
     * 修改订单备注
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @param remark  备注
     * @return 执行结果
     */
    public boolean orderRemark(ShopDO shop, String orderNo, String remark) {
        JsonObject body = new JsonObject();
        body.addProperty("order_id", orderNo);
        body.addProperty("merchant_notes", remark);
        WechatChannelsShopRemarkDTO call = this.call(shop, API_ORDER_UPDATE, body, WechatChannelsShopRemarkDTO.class);
        return call.isSucceed();
    }

    /**
     * 电子面单预取号
     *
     * @param shop 店铺
     * @param cmd  参数
     * @return 执行结果
     */
    public String preCreateLogisticsOrder(ShopDO shop, WechatChannelsShopLogisticsCreateCmd cmd) {
        WechatChannelsShopPreLogisticsDTO call = this.call(shop, API_LOGISTICS_PRE_CALL, cmd, WechatChannelsShopPreLogisticsDTO.class);
        return Optional.of(call)
                .map(WechatChannelsShopPreLogisticsDTO::getWaybillOrderId)
                .orElse(null);
    }

    /**
     * 电子面单取号
     * <p>从 {@code /channels/ec/logistics/ewaybill/biz/order/precreate} 接口拿到电子面单订单id</p>
     *
     * @param shop 店铺
     * @param cmd  参数
     * @return 执行结果
     */
    public WechatChannelsShopLogisticsDTO createLogisticsOrder(ShopDO shop, WechatChannelsShopLogisticsCreateCmd cmd) {
        return this.call(shop, API_LOGISTICS_CALL, cmd, WechatChannelsShopLogisticsDTO.class);
    }

    /**
     * 获取退款列表
     *
     * @param shop 店铺
     * @param qry  参数
     * @return 执行结果
     */
    public WechatChannelsShopRefundPageDTO pageRefunds(ShopDO shop, WechatChannelsShopRefundPageQry qry) {
        return this.call(shop, API_AFTER_SALE_PAGE, qry, WechatChannelsShopRefundPageDTO.class);
    }

    /**
     * 获取退款单
     *
     * @param shop     店铺
     * @param refundId 退款单号
     * @return 执行结果
     */
    public WechatChannelsShopRefundDTO getRefund(ShopDO shop, String refundId) {
        JsonObject body = new JsonObject();
        body.addProperty("after_sale_order_id", refundId);
        return this.call(shop, API_AFTER_SALE_GET, body, WechatChannelsShopRefundDTO.class);
    }

    /**
     * 执行远程调用
     *
     * @param shop          店铺
     * @param api           请求路径
     * @param body          请求参数
     * @param responseClass 响应结果类型
     * @return 执行结果
     */
    private <T extends WechatChannelsShopBaseDTO> T call(ShopDO shop, String api, Object body, Class<T> responseClass) {
        // 构建请求 uri 参数
        final Function<String, URI> buildUri = accessToken -> UriComponentsBuilder.fromHttpUrl(DOMAIN + api)
                .queryParam("access_token", accessToken).build().toUri();
        // 执行请求
        final Function<URI, String> executeRequest = uri -> {
            String response = HttpUtil.postWithoutEncode(uri, body, String.class);
            log.info("微信视频号小店返回值: {}", response);
            return response;
        };

        String responseStr = "";
        URI uri = null;
        try {
            uri = buildUri.apply(shop.getAppAccessToken());
            responseStr = executeRequest.apply(uri);
            // 判断 token 异常，重新获取 token 并重试接口调用
            boolean retry = RETRY_TOKEN_EXCEPTION.stream().anyMatch(responseStr::contains);
            if (retry) {
                log.warn("微信视频号小店 token 异常，重新获取 token 并重试接口调用 [{}]", shop.getShopCode());
                WechatChannelsShopRefreshTokenDTO tokenDTO = this.refreshToken(shop);
                uri = buildUri.apply(tokenDTO.getAccessToken());
                responseStr = executeRequest.apply(uri);
            }
            // 解析响应结果
            return this.parseResponse(responseStr, responseClass);
        } catch (Exception e) {
            if (e instanceof HttpServerErrorException) {
                responseStr = ((HttpServerErrorException) e).getResponseBodyAsString();
                log.info("微信视频号小店返回值: {}", responseStr);
            }
            String msg = String.format("微信视频号小店平台调用异常 response: %s, url: %s", responseStr, uri);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
        }
    }

    /**
     * 解析响应结果
     *
     * @param responseStr   响应结果
     * @param responseClass 响应结果类型
     * @param <T>           继承 SfBaseXmlDTO 的类型
     * @return 响应结果
     */
    private <T extends WechatChannelsShopBaseDTO> T parseResponse(String responseStr, Class<T> responseClass) throws RemoteException {
        if (StringUtils.isBlank(responseStr)) {
            throw new RemoteException("RPC返回空结果");
        }
        // 解析通用响应结果，并判断是否成功
        T response = GsonUtil.jsonToBean(responseStr, responseClass);
        if (response.isFailure()) {
            throw new RemoteException("RPC返回异常状态码");
        }
        if (StringUtils.isBlank(response.getResponseDataKey())) {
            return response;
        }
        // 解析数据结果
        JsonObject responseObject = GsonUtil.jsonToObject(responseStr);
        String dataJson = Optional.ofNullable(responseObject)
                .map(obj -> obj.get(response.getResponseDataKey()))
                .map(JsonElement::toString)
                .orElse(null);
        if (StringUtils.isBlank(dataJson)) {
            return response;
        }
        T responseData = null;
        try {
            // 数据结果中设置响应结果
            responseData = GsonUtil.jsonToBean(dataJson, responseClass);
            responseData.setErrCode(response.getErrCode());
            responseData.setErrMsg(response.getErrMsg());
            responseData.setOriginData(responseStr);
        } catch (Exception e) {
            log.warn("微信视频号小店平台解析解析响应结果异常 {}", e.getMessage(), e);
        }
        return responseData;
    }

    public static void main(String[] args) {
        WechatChannelsShopRpc rpc = new WechatChannelsShopRpc();
        ShopDO shop = ShopDO.builder()
                .shopCode("WECHAT_CHANNELS_SHOP_XTC")
                .appKey("xxxxxx")
                .appSecret("xxxxxx")
                .appAccessToken("xxxxxx")
                .build();

        rpc.getOrder(shop, "3720157767442239744");
    }

}
