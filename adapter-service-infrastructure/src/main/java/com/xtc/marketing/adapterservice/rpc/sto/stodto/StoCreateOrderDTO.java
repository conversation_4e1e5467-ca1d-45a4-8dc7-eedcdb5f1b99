package com.xtc.marketing.adapterservice.rpc.sto.stodto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 申通下单
 */
@Getter
@Setter
@ToString
public class StoCreateOrderDTO extends StoBaseDTO {

    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 运单号
     */
    private String waybillNo;
    /**
     * 大字/三段码
     */
    private String bigWord;
    /**
     * 集包地
     */
    private String packagePlace;
    /**
     * 客户订单号（调度订单时返回客户订单号，非调度订单不返回该值）
     */
    private String sourceOrderId;
    /**
     * 安全号码
     */
    private String safeNo;

}
