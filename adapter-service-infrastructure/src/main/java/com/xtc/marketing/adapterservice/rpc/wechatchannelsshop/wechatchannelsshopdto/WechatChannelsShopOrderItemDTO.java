package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 微信视频号小店订单详情
 */
@Getter
@Setter
@ToString
public class WechatChannelsShopOrderItemDTO extends WechatChannelsShopBaseDTO {

    /**
     * 下单时间
     */
    @SerializedName(value = "orderTime", alternate = "create_time")
    private String orderTime;
    /**
     * 更新时间
     */
    @SerializedName("update_time")
    private String updateTime;
    /**
     * 订单ID
     */
    @SerializedName("order_id")
    private String orderNo;
    /**
     * 订单状态
     */
    @SerializedName("status")
    private Integer orderState;
    /**
     * 买家身份标识
     */
    @SerializedName("openid")
    private String buyerId;
    /**
     * 买家在开放平台的唯一标识符，若当前视频号小店已绑定到微信开放平台账号下，绑定成功后产生的订单会返回，详见UnionID 机制说明
     */
    @SerializedName("unionid")
    private String unionId;
    /**
     * 订单详细数据信息
     */
    @SerializedName("order_detail")
    private WechatChannelsShopOrderItemDetailDTO orderDetail;

    @Override
    public String getResponseDataKey() {
        return "order";
    }

}
