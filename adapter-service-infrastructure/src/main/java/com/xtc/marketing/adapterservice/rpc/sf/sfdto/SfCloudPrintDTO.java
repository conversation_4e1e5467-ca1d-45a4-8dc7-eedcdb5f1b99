package com.xtc.marketing.adapterservice.rpc.sf.sfdto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 顺丰面单云打印响应
 * <p><a href="https://open.sf-express.com/Api/ApiDetails?level3=661&interName=%E4%BA%91%E6%89%93%E5%8D%B0%E9%9D%A2%E5%8D%95%E8%BD%AC%E8%8F%9C%E9%B8%9F%E6%A8%A1%E6%9D%BF%E6%8E%A5%E5%8F%A3-COM_RECE_CLOUD_PRINT_CAINIAO#a27_JSON_a_188">接口文档</a></p>
 */
@Getter
@Setter
@ToString
public class SfCloudPrintDTO extends SfBaseDTO {

    /**
     * 客户编码
     */
    private String clientCode;
    /**
     * 模板编码
     */
    private String templateCode;
    /**
     * 文件类型
     */
    private String fileType;
    /**
     * 打印文件集合，不保证顺序，自行通过 seqNo 排序
     */
    private List<PrintFile> files;

    /**
     * 打印文件
     */
    @Getter
    @Setter
    @ToString
    public static class PrintFile {

        /**
         * 顺丰运单号（子母单则为子单号）
         */
        private String waybillNo;
        /**
         * 每一单的模板文件集合
         */
        private List<PrintTemplate> contents;
        /**
         * 面单序号（documents的序号）
         */
        private Integer seqNo;

    }

    /**
     * 打印模板
     */
    @Getter
    @Setter
    @ToString
    public static class PrintTemplate {

        /**
         * 菜鸟模板文件的 url 下载地址
         */
        private String templateURL;
        /**
         * 联编号（单联：1）
         */
        private Integer areaNo;
        /**
         * 每联的页号（单页：1）
         */
        private Integer pageNo;

    }

}
