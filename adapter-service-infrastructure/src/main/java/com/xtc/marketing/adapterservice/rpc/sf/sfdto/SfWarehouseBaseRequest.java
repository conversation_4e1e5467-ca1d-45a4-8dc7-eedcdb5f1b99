package com.xtc.marketing.adapterservice.rpc.sf.sfdto;

import com.google.gson.annotations.SerializedName;
import lombok.*;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class SfWarehouseBaseRequest {

    /**
     * 货主编码
     */
    @SerializedName("CompanyCode")
    private String companyCode;

    /**
     * 接入编码
     */
    @SerializedName("AccessCode")
    private String accessCode;

    /**
     * 校验码
     */
    @SerializedName("Checkword")
    private String checkWord;

    /**
     * 服务方法编码
     */
    @SerializedName("ServiceCode")
    private String serviceCode;

}
