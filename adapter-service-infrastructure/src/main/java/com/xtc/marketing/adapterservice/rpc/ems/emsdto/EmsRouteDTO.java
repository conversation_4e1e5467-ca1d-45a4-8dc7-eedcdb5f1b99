package com.xtc.marketing.adapterservice.rpc.ems.emsdto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class EmsRouteDTO extends EmsBaseDTO {

    /**
     * 物流运单号（一票多件、返单业务单号逗号分隔）
     */
    private String waybillNo;

    /**
     * 四段码（分拣码）
     */
    private String opTime;

    /**
     * 操作码
     */
    private String opCode;

    /**
     * 操作名
     */
    private String opName;

    /**
     * 操作描述
     */
    private String opDesc;

    /**
     * 操作网点省名
     */
    private String opOrgProvName;

    /**
     * 操作网点城市
     */
    private String opOrgCity;

    /**
     * 操作网点编码
     */
    private String opOrgCode;

    /**
     * 操作网点名称
     */
    private String opOrgName;

    /**
     * 操作员工号
     */
    private String operatorNo;

    /**
     * 操作员工名称
     */
    private String operatorName;

}
