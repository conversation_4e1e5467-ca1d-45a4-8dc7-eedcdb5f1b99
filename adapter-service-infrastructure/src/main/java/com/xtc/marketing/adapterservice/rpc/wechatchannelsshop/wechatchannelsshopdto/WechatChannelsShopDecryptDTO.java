package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 微信视频号小店解密
 */
@Getter
@Setter
@ToString
public class WechatChannelsShopDecryptDTO extends WechatChannelsShopBaseDTO {

    /**
     * 收货人姓名
     */
    @SerializedName("user_name")
    private String receiverName;
    /**
     * 省份
     */
    @SerializedName("province_name")
    private String receiverProvince;
    /**
     * 城市
     */
    @SerializedName("city_name")
    private String receiverCity;
    /**
     * 区
     */
    @SerializedName("county_name")
    private String receiverDistrict;
    /**
     * 详细地址
     */
    @SerializedName("detail_info")
    private String receiverAddress;
    /**
     * 联系方式
     */
    @SerializedName("tel_number")
    private String receiverMobile;

    @Override
    public String getResponseDataKey() {
        return "orderInfo";
    }

}
