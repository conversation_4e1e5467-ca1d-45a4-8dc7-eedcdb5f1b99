package com.xtc.marketing.adapterservice.rpc.yto.ytodto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YtoCreateOrderCmd {

    /**
     * 物流单号，打印拉取运单号前，物流单号和渠道唯一确定一笔快递物流订单。注：最低长度为7
     */
    private String logisticsNo;

    /**
     * 寄件人姓名
     */
    private String senderName;

    /**
     * 寄件人电话
     */
    private String senderMobile;

    /**
     * 寄件人省名称
     */
    private String senderProvinceName;

    /**
     * 寄件人市名称
     */
    private String senderCityName;

    /**
     * 寄件人区县名称
     */
    private String senderCountyName;

    /**
     * 寄件人乡镇名称
     */
    private String senderTownName;

    /**
     * 寄件人详细地址
     */
    private String senderAddress;

    /**
     * 收件人姓名
     */
    private String recipientName;

    /**
     * 收件人电话
     */
    private String recipientMobile;

    /**
     * 收件人省名称
     */
    private String recipientProvinceName;

    /**
     * 收件人市名称
     */
    private String recipientCityName;

    /**
     * 收件人区县名称
     */
    private String recipientCountyName;

    /**
     * 收件人乡镇名称
     */
    private String recipientTownName;

    /**
     * 收件人详细地址
     */
    private String recipientAddress;

    /**
     * 备注，打印在面单上的备注内容
     */
    private String remark;

    /**
     * 货物列表，注：支持20个以内
     */
    private List<YtoCargoCmd> goods;

}
