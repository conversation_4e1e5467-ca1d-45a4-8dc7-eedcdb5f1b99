package com.xtc.marketing.adapterservice.rpc.tenserpay.tenserpaydto.command;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TenserPayRefundCmd {

    /**
     * 平台交易订单号
     * 交易成功后，平台返回的交易订单号
     */
    private String orderNo;

    /**
     * 商户退款订单号
     * 发起退款方的退款订单号
     */
    private String outRefundOrderNo;

    /**
     * 退款金额
     * 发起退款的金额，不能大于付款金额
     */
    private String refundAmount;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 退款结果异步通知地址
     * 订单退款成功后，通知退款结果
     */
    private String notifyUrl;

}
