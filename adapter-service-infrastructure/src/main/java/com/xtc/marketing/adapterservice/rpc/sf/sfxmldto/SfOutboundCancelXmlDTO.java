package com.xtc.marketing.adapterservice.rpc.sf.sfxmldto;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 顺丰仓库，取消出库响应
 */
@Setter
@Getter
@ToString
@XStreamAlias("SaleOrder")
public class SfOutboundCancelXmlDTO extends SfResponseXmlDTO {

    /**
     * 出库单号（业务方单号）
     */
    @XStreamAlias("ErpOrder")
    private String orderId;

}
