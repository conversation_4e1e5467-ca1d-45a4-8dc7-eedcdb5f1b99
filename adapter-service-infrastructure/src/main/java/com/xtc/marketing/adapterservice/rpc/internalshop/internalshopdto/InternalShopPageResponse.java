package com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 内部购机分页响应
 */
@Getter
@Setter
@ToString
public class InternalShopPageResponse<T> {

    /**
     * 数据列表
     */
    private List<T> data;

    /**
     * 页码
     */
    @SerializedName("page_no")
    private Integer pageNo;

    /**
     * 页面数据量
     */
    @SerializedName("page_size")
    private Integer pageSize;

    /**
     * 数据总数
     */
    @SerializedName("data_total")
    private Integer total;

}
