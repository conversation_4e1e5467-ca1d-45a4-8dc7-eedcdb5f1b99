package com.xtc.marketing.adapterservice.rpc.ems.emsdto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class EmsOrderDTO extends EmsBaseDTO {

    /**
     * 物流订单号（客户内部订单号)
     */
    private String logisticsOrderNo;

    /**
     * 物流运单号（一票多件、返单业务单号逗号分隔）
     */
    private String waybillNo;

    /**
     * 四段码（分拣码）
     */
    private String routeCode;

    /**
     * 集包地编码
     */
    private String packageCode;

    /**
     * 集包地名称
     */
    private String packageCodeName;

    /**
     * 大头笔编码
     */
    private String markDestinationCode;

    /**
     * 大头笔
     */
    private String markDestinationName;

}
