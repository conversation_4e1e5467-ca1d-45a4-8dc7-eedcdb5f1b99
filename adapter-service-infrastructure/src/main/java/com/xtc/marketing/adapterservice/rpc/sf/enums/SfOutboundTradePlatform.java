package com.xtc.marketing.adapterservice.rpc.sf.enums;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.command.SfOutboundApplyXmlCmd;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.warehouse.dto.command.OutboundApplyCmd;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * 顺丰仓库出库交易平台枚举，用于标识不同的电商平台。
 * 每个枚举实例代表一个特定的电商平台，并包含该平台的代码和处理策略。
 */
@Getter
public enum SfOutboundTradePlatform {
    /**
     * 天猫
     */
    TMALL("TM", "SET_OAID"),
    /**
     * 小红书
     */
    XIAOHONGSHU("XHS", "SET_RECEIVER_EXTEND_DATA"),
    /**
     * 抖音
     */
    TIKTOK("DY", "SET_RECEIVER"),
    /**
     * 快手
     */
    KUAISHOU("KS", "SET_RECEIVER"),
    /**
     * 微信视频号
     */
    WECHAT_CHANNELS_SHOP("SPH", "SET_WECHAT_CHANNELS_SHOP"),
    /**
     * 京东
     */
    JD("JD", "SET_OAID"),
    /**
     * 拼多多
     */
    PDD("PDD", "DEFAULT"),
    /**
     * 抖音代发
     */
    TIKTOK_DISTR("DYDF", "SET_SHOP_NAME"),
    ;

    /**
     * 交易平台
     */
    private final String tradePlatform;
    /**
     * 收件人密文策略
     */
    private final String receiverStrategy;

    SfOutboundTradePlatform(String tradePlatform, String receiverStrategy) {
        this.tradePlatform = tradePlatform;
        this.receiverStrategy = receiverStrategy;
    }

    /**
     * 获取交易平台枚举
     *
     * @param platformCode 店铺的平台代码
     * @return 交易平台枚举
     */
    public static SfOutboundTradePlatform of(String platformCode) {
        if (platformCode == null) {
            return null;
        }
        try {
            return SfOutboundTradePlatform.valueOf(platformCode);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * 获取交易平台
     *
     * @param platformCode 店铺的平台代码
     * @return 交易平台
     */
    public static String getTradePlatform(String platformCode) {
        SfOutboundTradePlatform value = of(platformCode);
        if (value == null) {
            return null;
        }
        return value.getTradePlatform();
    }

    /**
     * 根据不同的处理策略设置收货人信息。
     *
     * @param cmd           统一出库申请参数
     * @param sfOutboundCmd 顺丰出库申请参数
     * @param shop          店铺
     */
    public void setReceiver(OutboundApplyCmd cmd, SfOutboundApplyXmlCmd sfOutboundCmd, ShopDO shop) {
        switch (this.receiverStrategy) {
            case "SET_RECEIVER":
                // 从加密数组中提取收货人信息并设置到出库申请单
                setReceiverCiphertext(cmd, sfOutboundCmd, shop);
                break;
            case "SET_OAID":
                // 设置收件人密文
                setReceiverOaid(cmd, sfOutboundCmd);
                break;
            case "SET_WECHAT_CHANNELS_SHOP":
                // 设置应用id和收件人密文，并设置店铺名称
                setReceiverWechatChannelsShop(cmd, sfOutboundCmd, shop);
                break;
            case "SET_RECEIVER_EXTEND_DATA":
                // 从加密数组中提取收货人信息并设置到出库申请单
                setReceiverCiphertext(cmd, sfOutboundCmd, shop);
                // 设置收件人扩展字段
                setReceiverAddress(cmd, sfOutboundCmd);
                break;
            case "SET_SHOP_NAME":
                setShopName(sfOutboundCmd, shop);
                break;
            case "DEFAULT":
            default:
                break;
        }
    }

    /**
     * 应用策略：适用于抖音代发。
     * <p>分割代理代码，设置店铺名称</p>
     *
     * @param sfOutboundCmd 顺丰出库申请参数
     * @param shop          店铺
     */
    private void setShopName(SfOutboundApplyXmlCmd sfOutboundCmd, ShopDO shop) {
        if (StringUtils.isBlank(shop.getAgentCode())) {
            throw SysException.of(SysErrorCode.S_PARAM_ERROR, "店铺名称不能为空");
        }
        String shopName = shop.getAgentCode().split("\\|")[1];
        sfOutboundCmd.setShopName(shopName);
    }

    /**
     * 应用策略：适用于抖音和快手。
     * <p>从加密数组中提取收货人信息并设置到出库申请单。</p>
     *
     * @param cmd           统一出库申请参数
     * @param sfOutboundCmd 顺丰出库申请参数
     * @param shop          店铺
     */
    private void setReceiverCiphertext(OutboundApplyCmd cmd, SfOutboundApplyXmlCmd sfOutboundCmd, ShopDO shop) {
        JsonElement receiver = GsonUtil.jsonToBean(cmd.getReceiverOaid(), JsonElement.class);
        JsonArray receiverArr = Optional.ofNullable(receiver).filter(JsonElement::isJsonArray)
                .map(JsonElement::getAsJsonArray).filter(arr -> arr.size() == 3).orElse(null);
        if (receiverArr != null) {
            Optional.ofNullable(receiverArr.get(0)).map(JsonElement::getAsString)
                    .ifPresent(outboundApply -> sfOutboundCmd.getReceiver().setReceiverName(outboundApply));
            Optional.ofNullable(receiverArr.get(1)).map(JsonElement::getAsString)
                    .ifPresent(receiverMobile -> sfOutboundCmd.getReceiver().setReceiverMobile(receiverMobile));
            Optional.ofNullable(receiverArr.get(2)).map(JsonElement::getAsString)
                    .ifPresent(receiverAddress -> sfOutboundCmd.getReceiver().setReceiverAddress(receiverAddress));
        }
        sfOutboundCmd.setShopName(shop.getShopId());
    }

    /**
     * 应用策略：适用于天猫、小红书和京东。
     * <p>设置收件人密文</p>
     *
     * @param cmd           统一出库申请参数
     * @param sfOutboundCmd 顺丰出库申请参数
     */
    private void setReceiverOaid(OutboundApplyCmd cmd, SfOutboundApplyXmlCmd sfOutboundCmd) {
        SfOutboundApplyXmlCmd.OrderExtendAttribute extendData = new SfOutboundApplyXmlCmd.OrderExtendAttribute();
        extendData.setUserDef30(cmd.getReceiverOaid());
        sfOutboundCmd.setExtendData(extendData);
    }

    /**
     * 应用策略：适用于微信视频号。
     * <p>设置应用id和收件人密文，并设置店铺名称</p>
     *
     * @param cmd           统一出库申请参数
     * @param sfOutboundCmd 顺丰出库申请参数
     * @param shop          店铺
     */
    private void setReceiverWechatChannelsShop(OutboundApplyCmd cmd, SfOutboundApplyXmlCmd sfOutboundCmd, ShopDO shop) {
        if (shop == null) {
            return;
        }
        sfOutboundCmd.setEwaybillOrderAppid(shop.getAppKey());
        sfOutboundCmd.setEwaybillOrderCode(cmd.getReceiverOaid());
        sfOutboundCmd.setShopName(shop.getShopId());
    }

    /**
     * 应用策略：适用于小红书。
     * <p>设置收件人扩展字段</p>
     *
     * @param cmd           统一出库申请参数
     * @param sfOutboundCmd 顺丰出库申请参数
     */
    private void setReceiverAddress(OutboundApplyCmd cmd, SfOutboundApplyXmlCmd sfOutboundCmd) {
        SfOutboundApplyXmlCmd.OrderExtendAttribute extendData = new SfOutboundApplyXmlCmd.OrderExtendAttribute();
        extendData.setUserDef30(cmd.getReceiverAddress());
        sfOutboundCmd.setExtendData(extendData);
    }
}
