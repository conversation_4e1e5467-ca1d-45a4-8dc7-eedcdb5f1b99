package com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto;

import com.google.gson.annotations.SerializedName;
import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.enums.InternalShopOrderStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 内部购机订单
 */
@Getter
@Setter
@ToString
public class InternalShopOrderDTO {

    /**
     * 交易编号
     */
    @SerializedName("trade_sn")
    private String tradeSn;

    /**
     * 订单编号
     */
    @SerializedName("sn")
    private String sn;

    /**
     * 店铺id
     */
    @SerializedName("seller_id")
    private Integer sellerId;

    /**
     * 店铺名称
     */
    @SerializedName("seller_name")
    private String sellerName;

    /**
     * 员工工号
     */
    @SerializedName("qy_user_id")
    private String qyUserId;

    /**
     * 买家id
     */
    @SerializedName("member_id")
    private Integer memberId;

    /**
     * 买家名称
     */
    @SerializedName("member_name")
    private String memberName;

    /**
     * 订单状态
     */
    @SerializedName("order_status")
    private InternalShopOrderStatus orderStatus;

    /**
     * 付款状态
     */
    @SerializedName("pay_status")
    private String payStatus;

    /**
     * 货运状态
     */
    @SerializedName("ship_status")
    private String shipStatus;

    /**
     * 评价状态
     */
    @SerializedName("comment_status")
    private String commentStatus;

    /**
     * 配送方式ID
     */
    @SerializedName("shipping_id")
    private Integer shippingId;

    /**
     * 配送方式
     */
    @SerializedName("shipping_type")
    private String shippingType;

    /**
     * 支付方式名称
     */
    @SerializedName("payment_method_name")
    private String paymentMethodName;

    /**
     * 支付方式类型
     */
    @SerializedName("payment_type")
    private String paymentType;

    /**
     * 支付时间（秒时间戳）
     */
    @SerializedName("payment_time")
    private Long paymentTime;

    /**
     * 收货人姓名
     */
    @SerializedName("ship_name")
    private String shipName;

    /**
     * 收货地址
     */
    @SerializedName("ship_addr")
    private String shipAddr;

    /**
     * 收货人邮编
     */
    @SerializedName("ship_zip")
    private String shipZip;

    /**
     * 收货人手机
     */
    @SerializedName("ship_mobile")
    private String shipMobile;

    /**
     * 收货人电话
     */
    @SerializedName("ship_tel")
    private String shipTel;

    /**
     * 配送地区-省份
     */
    @SerializedName("ship_province")
    private String shipProvince;

    /**
     * 配送地区-城市
     */
    @SerializedName("ship_city")
    private String shipCity;

    /**
     * 配送地区-区(县)
     */
    @SerializedName("ship_county")
    private String shipCounty;

    /**
     * 配送街道
     */
    @SerializedName("ship_town")
    private String shipTown;

    /**
     * 订单总额
     */
    @SerializedName("order_price")
    private BigDecimal orderPrice;

    /**
     * 商品总额
     */
    @SerializedName("goods_price")
    private BigDecimal goodsPrice;

    /**
     * 配送费用
     */
    @SerializedName("shipping_price")
    private BigDecimal shippingPrice;

    /**
     * 优惠金额
     */
    @SerializedName("discount_price")
    private BigDecimal discountPrice;

    /**
     * 需要支付的金额
     */
    @SerializedName("need_pay_money")
    private BigDecimal needPayMoney;

    /**
     * 支付金额
     */
    @SerializedName("pay_money")
    private BigDecimal payMoney;

    /**
     * 订单商品总重量
     */
    @SerializedName("weight")
    private BigDecimal weight;

    /**
     * 商品数量
     */
    @SerializedName("goods_num")
    private Integer goodsNum;

    /**
     * 买家备注
     */
    @SerializedName("buyer_remark")
    private String buyerRemark;

    /**
     * 卖家备注
     */
    @SerializedName("seller_remark")
    private String sellerRemark;

    /**
     * 管理员备注
     */
    @SerializedName("admin_remark")
    private Integer adminRemark;

    /**
     * 订单取消原因
     */
    @SerializedName("cancel_reason")
    private String cancelReason;

    /**
     * 签收人
     */
    @SerializedName("the_sign")
    private String theSign;

    /**
     * 物流单号
     */
    @SerializedName("ship_no")
    private String shipNo;

    /**
     * 物流公司ID
     */
    @SerializedName("logi_id")
    private Integer logiId;

    /**
     * 物流公司名称
     */
    @SerializedName("logi_name")
    private String logiName;

    /**
     * 订单的过期时间，只有待付款和待收货的订单才会显示过期时间，过期就自动取消或者自动收货
     */
    @SerializedName("expired_time")
    private String expiredTime;

    /**
     * 完成时间（秒时间戳）
     */
    @SerializedName("complete_time")
    private Long completeTime;

    /**
     * 订单创建时间（秒时间戳）
     */
    @SerializedName("create_time")
    private Long createTime;

    /**
     * 签收时间（秒时间戳）
     */
    @SerializedName("signing_time")
    private Long signingTime;

    /**
     * 送货时间（秒时间戳）
     */
    @SerializedName("ship_time")
    private Long shipTime;

    /**
     * 支付方式返回的交易号
     */
    @SerializedName("pay_order_no")
    private String payOrderNo;

    /**
     * 售后状态
     */
    @SerializedName("service_status")
    private String serviceStatus;

    /**
     * 结算状态
     */
    @SerializedName("bill_status")
    private Integer billStatus;

    /**
     * 结算单号
     */
    @SerializedName("bill_sn")
    private String billSn;

    /**
     * 订单来源
     */
    @SerializedName("client_type")
    private String clientType;

    /**
     * 是否需要发票
     */
    @SerializedName("need_receipt")
    private Integer needReceipt;

    /**
     * 是否抛单
     */
    @SerializedName("is_output")
    private Integer isOutput;

    /**
     * 最后更新时间（秒时间戳）
     */
    @SerializedName("last_update_time")
    private Long lastUpdateTime;

    /**
     * 订单状态文字
     */
    @SerializedName("order_status_text")
    private String orderStatusText;

    /**
     * 付款状态文字
     */
    @SerializedName("pay_status_text")
    private String payStatusText;

    /**
     * 发货状态文字
     */
    @SerializedName("ship_status_text")
    private String shipStatusText;

    /**
     * 售后状态文字
     */
    @SerializedName("service_status_text")
    private String serviceStatusText;

    /**
     * 发票信息
     */
    @SerializedName("receipt_history")
    private InternalShopReceiptDTO receiptHistory;

    /**
     * sku列表
     */
    @SerializedName("order_sku_list")
    private List<InternalShopOrderItemDTO> items;

}
