package com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.command;

import com.beust.jcommander.internal.Lists;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.SfRequestXmlDTO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 顺丰仓库，取消出库
 */
@Setter
@Getter
@ToString
@Builder
@XStreamAlias("SaleOrder")
public class SfOutboundCancelXmlCmd extends SfRequestXmlDTO {

    /**
     * 出库单号（业务方单号）
     */
    @XStreamAlias("ErpOrder")
    private String orderId;

    @Override
    public String toRequestXml(String requestBodyXml, String companyCode) {
        return "<CancelSaleOrderRequest>"
                + "<CompanyCode>"
                + companyCode
                + "</CompanyCode>"
                + "<SaleOrders>"
                + requestBodyXml
                + "</SaleOrders>"
                + "</CancelSaleOrderRequest>";
    }

    private static final List<String> RESPONSE_PARSE_ELEMENTS =
            Lists.newArrayList("CancelSaleOrderResponse", "SaleOrders", "SaleOrder");

    @Override
    public List<String> getResponseParseElements() {
        return RESPONSE_PARSE_ELEMENTS;
    }

}
