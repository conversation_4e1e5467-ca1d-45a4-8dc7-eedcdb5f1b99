package com.xtc.marketing.adapterservice.rpc.jitu.jitudto.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 极兔取消订单响应
 */
@Getter
@Setter
@ToString
public class JtExpressCancelOrderResponse {

    /**
     * 取消结果
     * 1：成功
     * 0：失败
     */
    private String result;
    /**
     * 取消结果描述
     */
    private String resultDesc;
    /**
     * 运单号
     */
    private String waybillNo;
    /**
     * 订单号
     */
    private String txlogisticId;
    /**
     * 取消时间
     */
    private String cancelTime;

}