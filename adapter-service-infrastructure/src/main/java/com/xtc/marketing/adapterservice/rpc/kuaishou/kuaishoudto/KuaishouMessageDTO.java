package com.xtc.marketing.adapterservice.rpc.kuaishou.kuaishoudto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 快手通用消息体结构
 */
@Getter
@Setter
@ToString
public class KuaishouMessageDTO {

    /**
     * 消息唯一id
     */
    private String eventId;
    /**
     * 业务id，如订单id
     */
    private Long bizId;
    /**
     * 商家id
     */
    private Long userId;
    /**
     * 应用Id
     */
    private String appKey;
    /**
     * 消息标示
     */
    private String event;
    /**
     * 消息内容，业务内容Json串
     */
    private String info;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 是否心跳测试事件
     */
    private Boolean test;

}
