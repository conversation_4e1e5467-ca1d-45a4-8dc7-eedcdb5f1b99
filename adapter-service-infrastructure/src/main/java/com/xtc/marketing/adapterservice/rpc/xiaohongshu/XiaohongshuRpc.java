package com.xtc.marketing.adapterservice.rpc.xiaohongshu;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.xiaohongshu.fls.opensdk.client.*;
import com.xiaohongshu.fls.opensdk.entity.BaseRequest;
import com.xiaohongshu.fls.opensdk.entity.BaseResponse;
import com.xiaohongshu.fls.opensdk.entity.afterSale.request.GetAfterSaleInfoRequest;
import com.xiaohongshu.fls.opensdk.entity.afterSale.request.ListAfterSaleInfosRequest;
import com.xiaohongshu.fls.opensdk.entity.afterSale.response.GetAfterSaleInfoResponse;
import com.xiaohongshu.fls.opensdk.entity.afterSale.response.ListAfterSaleInfosResponse;
import com.xiaohongshu.fls.opensdk.entity.express.request.ElectronicBillOrdersCreateRequest;
import com.xiaohongshu.fls.opensdk.entity.express.response.ElectronicBillOrdersCreateResponse;
import com.xiaohongshu.fls.opensdk.entity.invoice.request.ConfirmInvoiceRequest;
import com.xiaohongshu.fls.opensdk.entity.invoice.request.GetInvoiceListRequest;
import com.xiaohongshu.fls.opensdk.entity.invoice.response.GetInvoiceListResponse;
import com.xiaohongshu.fls.opensdk.entity.oauth.request.GetAccessTokenRequest;
import com.xiaohongshu.fls.opensdk.entity.oauth.request.RefreshTokenRequest;
import com.xiaohongshu.fls.opensdk.entity.oauth.response.GetAccessTokenResponse;
import com.xiaohongshu.fls.opensdk.entity.oauth.response.RefreshTokenResponse;
import com.xiaohongshu.fls.opensdk.entity.order.Requset.*;
import com.xiaohongshu.fls.opensdk.entity.order.Response.GetOrderDetailResponse;
import com.xiaohongshu.fls.opensdk.entity.order.Response.GetOrderListResponse;
import com.xiaohongshu.fls.opensdk.entity.order.Response.GetOrderReceiverInfoResponse;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.net.SocketTimeoutException;
import java.rmi.RemoteException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 小红书RPC
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class XiaohongshuRpc {

    /**
     * 缓存 client 对象
     */
    private static final Map<String, Object> CLIENT_CACHE = new ConcurrentHashMap<>();
    /**
     * 缓存 client 类型的 execute 方法
     */
    private static final ImmutableList<Method> EXECUTE_METHOD_CACHE;

    static {
        // 当前使用的 client 类型
        List<Class<? extends BaseClient>> classes = Lists.newArrayList(OauthClient.class, OrderClient.class,
                AfterSaleClient.class, ExpressClient.class, InvoiceClient.class);
        // 过滤出 execute 方法，且返回类型是 BaseResponse 类型，且参数数量在 1 和 2 之间，且第一个参数为 BaseRequest 类型的方法
        List<Method> collect = classes.stream().map(Class::getDeclaredMethods).flatMap(Stream::of)
                .filter(method -> "execute".equals(method.getName())
                        && method.getReturnType() == BaseResponse.class
                        && method.getParameterCount() >= 1
                        && method.getParameterCount() <= 2
                        && method.getParameterTypes()[0].getSuperclass() == BaseRequest.class)
                .collect(Collectors.toList());
        // 缓存 client 类型的 execute 方法
        EXECUTE_METHOD_CACHE = ImmutableList.copyOf(collect);
    }

    /**
     * 获取 AccessToken
     *
     * @param shop      店铺
     * @param grantCode 授权code
     * @return AccessToken
     */
    public GetAccessTokenResponse getAccessToken(ShopDO shop, String grantCode) {
        GetAccessTokenRequest request = new GetAccessTokenRequest();
        request.setCode(grantCode);
        return this.call(shop, request);
    }

    /**
     * 刷新 AccessToken
     *
     * @param shop 店铺
     * @return AccessToken
     */
    public RefreshTokenResponse refreshAccessToken(ShopDO shop) {
        RefreshTokenRequest request = new RefreshTokenRequest();
        request.setRefreshToken(shop.getAppRefreshToken());
        return this.call(shop, request);
    }

    /**
     * 查询订单详情
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 订单详情
     */
    public GetOrderDetailResponse getOrder(ShopDO shop, String orderNo) {
        GetOrderDetailRequest request = new GetOrderDetailRequest();
        request.setOrderId(orderNo);
        return this.call(shop, request);
    }

    /**
     * 分页查询订单列表
     *
     * @param shop    店铺
     * @param request 请求参数
     * @return 订单列表
     */
    public GetOrderListResponse pageOrders(ShopDO shop, GetOrderListRequest request) {
        return this.call(shop, request);
    }

    /**
     * 订单发货
     *
     * @param shop    店铺
     * @param request 参数
     * @return 执行结果
     */
    public boolean orderShipping(ShopDO shop, OrderDeliverRequest request) {
        String response = this.call(shop, request);
        return "发货成功".equals(response);
    }

    /**
     * 订单备注
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @param remark  备注
     * @return 执行结果
     */
    public boolean orderRemark(ShopDO shop, String orderNo, String remark) {
        ModifySellerMarkRequest request = new ModifySellerMarkRequest();
        request.setOrderId(orderNo);
        request.setSellerMarkNote(remark);
        request.setOperator("系统");
        // 5：蓝旗
        request.setSellerMarkPriority(5);
        String response = this.call(shop, request);
        return "修改成功".equals(response);
    }

    /**
     * 分页查询发票列表
     *
     * @param shop    店铺
     * @param request 请求参数
     * @return 查询发票列表
     */
    public GetInvoiceListResponse pageInvoiceApply(ShopDO shop, GetInvoiceListRequest request) {
        return this.call(shop, request);
    }

    /**
     * 分页查询退款列表
     *
     * @param shop    店铺
     * @param request 参数
     * @return 退款列表
     */
    public ListAfterSaleInfosResponse pageRefund(ShopDO shop, ListAfterSaleInfosRequest request) {
        return this.call(shop, request);
    }

    /**
     * 获取退款详情
     *
     * @param shop     店铺
     * @param refundId 退款单号
     * @return 退款详情
     */
    public GetAfterSaleInfoResponse getRefund(ShopDO shop, String refundId) {
        GetAfterSaleInfoRequest request = new GetAfterSaleInfoRequest();
        request.setReturnsId(refundId);
        return this.call(shop, request);
    }

    /**
     * 生成电子面单
     *
     * @param shop    店铺
     * @param request 请求参数
     * @return 电子面单
     */
    public ElectronicBillOrdersCreateResponse.ElectronicBillPrintData createLogisticsOrder(ShopDO shop, ElectronicBillOrdersCreateRequest request) {
        ElectronicBillOrdersCreateResponse response = this.call(shop, request);
        return Optional.of(response)
                .map(ElectronicBillOrdersCreateResponse::getWayBillList)
                .filter(CollectionUtils::isNotEmpty)
                .map(items -> items.get(0))
                .orElseThrow(() -> this.rpcSysException(response));
    }

    /**
     * 获取订单收件人信息
     *
     * @param shop          店铺
     * @param orderNo       订单号
     * @param openAddressId 加密地址ID
     * @return 订单收件人信息
     */
    public GetOrderReceiverInfoResponse getOrderReceiver(ShopDO shop, String orderNo, String openAddressId) {
        GetOrderReceiverInfoRequest request = new GetOrderReceiverInfoRequest();
        request.setIsReturn(false);
        GetOrderReceiverInfoRequest.OrderReceiverQuery query = new GetOrderReceiverInfoRequest.OrderReceiverQuery();
        query.setOrderId(orderNo);
        query.setOpenAddressId(openAddressId);
        request.setReceiverQueries(Collections.singletonList(query));
        return this.call(shop, request);
    }

    /**
     * 开票结果回传
     *
     * @param shop    店铺
     * @param request 请求
     */
    public void confirmInvoice(ShopDO shop, ConfirmInvoiceRequest request) {
        this.call(shop, request);
    }

    /**
     * 消息通知签名
     *
     * @param request   请求
     * @param timestamp 时间戳
     * @param appKey    应用Key
     * @param appSecret 应用密钥
     * @return 签名字符串
     */
    public String buildNotifySign(HttpServletRequest request, String timestamp, String appKey, String appSecret) {
        // 收集所有参数使用 TreeMap 按 key 排序（排除sign）：URL参数、Header参数
        Map<String, String> params = new TreeMap<>();
        params.put("timestamp", timestamp);
        params.put("app-key", appKey);
        // 收集URL参数
        String queryString = request.getQueryString();
        if (StringUtils.isNotBlank(queryString)) {
            Map<String, String> queryParams = Splitter.on('&').withKeyValueSeparator('=').split(queryString);
            params.putAll(queryParams);
        }
        // 构建签名参数字符串
        String paramString = Joiner.on('&').withKeyValueSeparator('=').join(params);
        // 拼接签名字符串: url + ? + 参数 + appSecret
        String signText;
        if (paramString.isEmpty()) {
            signText = request.getRequestURI() + appSecret;
        } else {
            signText = request.getRequestURI() + "?" + paramString + appSecret;
        }
        // 计算MD5签名
        return DigestUtils.md5Hex(signText);
    }

    /**
     * 执行远程调用
     *
     * @param shop    店铺
     * @param request 请求参数
     * @param <R>     响应结果类型
     * @return 响应结果
     */
    @SuppressWarnings("unchecked")
    private <R> R call(ShopDO shop, BaseRequest request) {
        String responseStr = "";
        try {
            log.info("小红书平台参数: {}", GsonUtil.objectToJson(request));
            // 获取执行方法
            Method executeMethod = this.getExecuteMethod(request)
                    .orElseThrow(() -> new IllegalArgumentException("未找到 RPC 执行方法"));
            // 获取客户端实例，根据执行方法所属的类型
            Object client = this.getClient(shop, executeMethod.getDeclaringClass());
            // 确认执行方法的参数，并发起请求
            Object[] executeMethodArgs = Stream.of(request, shop.getAppAccessToken()).limit(executeMethod.getParameterCount()).toArray();
            BaseResponse<R> response = (BaseResponse<R>) executeMethod.invoke(client, executeMethodArgs);
            // 解析响应结果
            responseStr = GsonUtil.objectToJson(response);
            log.info("小红书平台返回值: {}", responseStr);
            if (BooleanUtils.isNotTrue(response.isSuccess())) {
                throw new RemoteException("RPC返回异常状态码");
            }
            return response.getData();
        } catch (Exception e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                responseStr = "请求超时";
            }
            throw this.rpcSysException(responseStr, e);
        }
    }

    /**
     * 获取 RPC 执行方法
     *
     * @param request 请求参数
     * @return 执行方法
     */
    private Optional<Method> getExecuteMethod(BaseRequest request) {
        return EXECUTE_METHOD_CACHE.stream().filter(method -> method.getParameterTypes()[0] == request.getClass()).findFirst();
    }

    /**
     * 获取客户端
     *
     * @param shop        店铺
     * @param clientClass 客户端类
     * @return 客户端
     * @throws Exception 异常
     */
    private Object getClient(ShopDO shop, Class<?> clientClass) throws Exception {
        String key = shop.getShopCode() + ":" + clientClass.getSimpleName();
        Object client = CLIENT_CACHE.get(key);
        if (client != null) {
            return client;
        }
        // 获取子类的构造方法（前提是每个子类的构造方法都一样）
        Constructor<?> constructor = clientClass.getDeclaredConstructor(String.class, String.class, String.class, String.class);
        // 创建客户端，使用子类的构造方法
        Object newClient = constructor.newInstance(shop.getApiUrl(), shop.getAppKey(), "2.0", shop.getAppSecret());
        CLIENT_CACHE.put(key, newClient);
        return newClient;
    }

    /**
     * 远程调用异常
     *
     * @param response 响应
     * @return 异常
     */
    private SysException rpcSysException(Object response) {
        String responseStr = GsonUtil.objectToJson(response);
        String msg = String.format("小红书平台调用异常 response: %s", responseStr);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg);
    }

    /**
     * 远程调用异常
     *
     * @param responseStr 响应结果
     * @param e           异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, Exception e) {
        String msg = String.format("小红书平台调用异常 response: %s", responseStr);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
    }

    public static void main(String[] args) {
        XiaohongshuRpc rpc = new XiaohongshuRpc();
        ShopDO shop = ShopDO.builder()
                .shopCode("XIAOHONGSHU_XTC")
                .apiUrl("https://ark.xiaohongshu.com/ark/open_api/v3/common_controller")
                .appKey("xxxxxx")
                .appSecret("xxxxxx")
                .appAccessToken("xxxxxx")
                .build();
        rpc.getOrder(shop, "P742220814448485491");
    }

}
