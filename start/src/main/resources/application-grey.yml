spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: adapter_service
    password: zJnhkMYonsEzJclIa2qS
    url: ***************************************************************************************************************************************************************************************
  redis:
    host: xtc-marketing-service-new.redis.rds.aliyuncs.com
    port: 6379
    username: adapter_service
    password: jwGNQD5TWLm9r38XAdzT

xxl:
  job:
    accessToken: gKjvzcRNA5HOTsXfI3KEPL6ItoxDRJEFrMamVfAEskgFvkVKQCtq9jDqPJI_AjTG
    admin:
      addresses: http://xxl-job.marketing-component-env-prod.svc:8080/xxl-job-admin/
    executor:
      appname: ${spring.application.name}-grey
