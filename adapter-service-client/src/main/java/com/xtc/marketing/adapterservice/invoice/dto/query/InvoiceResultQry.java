package com.xtc.marketing.adapterservice.invoice.dto.query;

import com.xtc.marketing.adapterservice.invoice.enums.InvoicePlatform;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceResultQry {

    /**
     * 发票平台
     */
    @NotNull
    private InvoicePlatform invoicePlatform;
    /**
     * 店铺代码
     */
    @Length(max = 50)
    private String shopCode;
    /**
     * 收款方：税务登记证号
     */
    @NotBlank
    @Length(max = 50)
    private String payeeRegisterNo;
    /**
     * 流水号（serial_no 和 platform_code, platform_tid 必须填写其中一组，serial_no优先级更高）
     */
    @Length(max = 50)
    private String serialNo;
    /**
     * 电商平台代码（TB=淘宝，TM=天猫，OTHER=其他）
     */
    @Length(max = 50)
    private String platformCode;
    /**
     * 订单号
     */
    @Length(max = 50)
    private String orderNo;

}