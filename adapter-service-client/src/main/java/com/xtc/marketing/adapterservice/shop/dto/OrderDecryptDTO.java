package com.xtc.marketing.adapterservice.shop.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 解密
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDecryptDTO {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 座机
     */
    private String phone;

    /**
     * 手机号（可能是虚拟号）
     */
    private String mobile;

    /**
     * 虚拟号
     */
    private String virtualMobile;

    /**
     * 虚拟号过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime mobileExpireTime;

    /**
     * 地址
     */
    private String address;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县
     */
    private String district;

    /**
     * 城镇
     */
    private String town;

    /**
     * 未知类型的解密集合
     */
    private Map<String, String> decryptTexts;

}
