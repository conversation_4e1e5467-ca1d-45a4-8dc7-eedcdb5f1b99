package com.xtc.marketing.adapterservice.shop.dto;

import com.xtc.marketing.adapterservice.shop.enums.InvoiceTitleTypeEnum;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发票申请
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceApplyDTO {

    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 发票类型
     */
    private InvoiceTypeEnum invoiceType;
    /**
     * 发票抬头类型
     */
    private InvoiceTitleTypeEnum invoiceTitleType;
    /**
     * 发票抬头
     */
    private String invoiceTitle;
    /**
     * 开票金额
     */
    private Integer invoiceAmount;
    /**
     * 税号
     */
    private String taxNo;
    /**
     * 开户银行
     */
    private String bankName;
    /**
     * 银行账号
     */
    private String bankAccount;
    /**
     * 企业电话
     */
    private String companyMobile;
    /**
     * 企业地址
     */
    private String companyAddress;
    /**
     * 买家备注
     */
    private String buyerMemo;
    /**
     * 发票地址，由电商系统上传
     */
    private String invoiceUrl;
    /**
     * 发票备注
     */
    private String invoiceMemo;
    /**
     * 销售方税号
     */
    private String sellerIdentifyNo;
    /**
     * 销售方名称
     */
    private String sellerName;
    /**
     * 发票主体id
     */
    private String invoiceEntityId;

}
