package com.xtc.marketing.adapterservice.shop.dto.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 查询订单
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderGetQry {

    /**
     * 店铺代码
     */
    @NotBlank
    @Length(max = 50)
    private String shopCode;

    /**
     * 订单号
     */
    @NotBlank
    @Length(max = 50)
    private String orderNo;

}
