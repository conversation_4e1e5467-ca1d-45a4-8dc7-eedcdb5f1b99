package com.xtc.marketing.adapterservice.invoice.enums;

import lombok.Getter;

/**
 * 发票结果电商平台代码
 */
@Getter
public enum InvoiceResultPlatformCode {

    /**
     * 淘宝
     */
    TAO_BAO("taobao"),
    /**
     * 天猫
     */
    TMALL("tmall"),
    /**
     * 未知平台
     */
    UNKNOWN("unknown");

    private final String code;

    InvoiceResultPlatformCode(String code) {
        this.code = code;
    }

    /**
     * 查询平台类型
     *
     * @param code 代码
     * @return 平台类型
     */
    public static InvoiceResultPlatformCode getResultPlatformCode(String code) {
        for (InvoiceResultPlatformCode status : InvoiceResultPlatformCode.values()) {
            if (code.equals(status.getCode())) {
                return status;
            }
        }
        return InvoiceResultPlatformCode.UNKNOWN;
    }

}
