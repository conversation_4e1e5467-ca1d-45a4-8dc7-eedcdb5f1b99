package com.xtc.marketing.adapterservice.warehouse.dto.command;

import com.xtc.marketing.adapterservice.warehouse.enums.WarehouseEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 入库单取消
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InboundCancelCmd {

    /**
     * 仓库
     */
    @NotNull
    private WarehouseEnum warehouse;

    /**
     * 入库单号（业务方单号）
     */
    @NotBlank
    private String orderId;

}
