package com.xtc.marketing.adapterservice.logistics.dto.command;

import com.xtc.marketing.adapterservice.warehouse.enums.WarehouseEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsInterceptCmd {

    /**
     * 业务账号
     */
    @NotBlank
    @Length(max = 50)
    private String bizAccount;
    /**
     * 单号（运单号、物流单号）
     */
    @NotBlank
    @Length(max = 50)
    private String orderId;
    /**
     * 仓库
     */
    private WarehouseEnum warehouse;
    /**
     * 收件方姓名
     */
    @Length(max = 50)
    private String receiverName;
    /**
     * 收件方手机号
     */
    @Length(max = 50)
    private String receiverMobile;
    /**
     * 收件方省份
     */
    @Length(max = 50)
    private String receiverProvince;
    /**
     * 收件方城市
     */
    @Length(max = 50)
    private String receiverCity;
    /**
     * 收件方区县
     */
    @Length(max = 50)
    private String receiverDistrict;
    /**
     * 收件方地址
     */
    @Length(max = 200)
    private String receiverAddress;
    /**
     * 拦截备注
     */
    private String interceptMemo;
    /**
     * 平台参数
     */
    @Length(max = 1000)
    private String platformJson;

}
