package com.xtc.marketing.adapterservice.shop.dto.command;

import com.xtc.marketing.adapterservice.shop.enums.InvoiceItemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发票项目
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceItemCmd {

    /**
     * 价税合计
     */
    private String taxAmount;
    /**
     * 税收分类编码
     */
    private String taxClassificationCode;
    /**
     * 单价
     */
    private String price;
    /**
     * 数量
     */
    private int num;
    /**
     * 发票行性质
     */
    private InvoiceItemTypeEnum itemType;
    /**
     * 不含税金额
     */
    private String priceAmount;
    /**
     * 税额
     */
    private String tax;
    /**
     * 税率
     */
    private String taxRate;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 规格
     */
    private String specification;
    /**
     * 发票号码
     */
    private String invoiceNo;
    /**
     * 单位
     */
    private String unit;
    /**
     * 发票文件
     */
    private String invoiceFileContent;

}
