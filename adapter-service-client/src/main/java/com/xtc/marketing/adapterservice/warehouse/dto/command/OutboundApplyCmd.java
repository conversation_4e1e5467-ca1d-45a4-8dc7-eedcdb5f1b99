package com.xtc.marketing.adapterservice.warehouse.dto.command;

import com.xtc.marketing.adapterservice.warehouse.enums.WarehouseEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 出库申请单
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutboundApplyCmd {

    /**
     * 店铺代码
     */
    @Length(max = 50)
    private String shopCode;
    /**
     * 仓库
     */
    @NotNull
    private WarehouseEnum warehouse;
    /**
     * 仓库编码
     */
    @NotBlank
    @Length(max = 50)
    private String warehouseCode;
    /**
     * 出库单号（业务方单号）
     */
    @NotBlank
    @Length(max = 50)
    private String orderId;
    /**
     * 平台代码（顺丰传输密文数据时必传）
     */
    @Length(max = 50)
    private String platformCode;
    /**
     * 店铺订单号
     */
    @Length(max = 50)
    private String shopOrderNo;
    /**
     * 物流产品
     */
    @Length(max = 50)
    private String logisticsProduct;
    /**
     * 使用寄件方信息，否则使用后台配置的寄件方信息
     */
    @NotNull
    private Boolean useSender;
    /**
     * 寄件方公司
     */
    @Length(max = 50)
    private String senderCompany;
    /**
     * 寄件方姓名
     */
    @Length(max = 50)
    private String senderName;
    /**
     * 寄件方手机号
     */
    @Length(max = 50)
    private String senderPhone;
    /**
     * 寄件方省份
     */
    @Length(max = 50)
    private String senderProvince;
    /**
     * 寄件方城市
     */
    @Length(max = 50)
    private String senderCity;
    /**
     * 寄件方区县
     */
    @Length(max = 50)
    private String senderDistrict;
    /**
     * 寄件方地址
     */
    @Length(max = 200)
    private String senderAddress;
    /**
     * 收件人密文数据
     */
    @Length(max = 2000)
    private String receiverOaid;
    /**
     * 收件方公司
     */
    @Length(max = 50)
    private String receiverCompany;
    /**
     * 收件方姓名
     */
    @NotBlank
    @Length(max = 200)
    private String receiverName;
    /**
     * 收件方手机号
     */
    @NotBlank
    @Length(max = 200)
    private String receiverMobile;
    /**
     * 收件方电话
     */
    @Length(max = 200)
    private String receiverPhone;
    /**
     * 收件方省份
     */
    @NotBlank
    @Length(max = 50)
    private String receiverProvince;
    /**
     * 收件方城市
     */
    @Length(max = 50)
    private String receiverCity;
    /**
     * 收件方区县
     */
    @Length(max = 50)
    private String receiverDistrict;
    /**
     * 收件方地址
     */
    @NotBlank
    @Length(max = 500)
    private String receiverAddress;
    /**
     * 货物集合，最多 200 个
     */
    @Valid
    @NotEmpty
    @Size(min = 1, max = 200)
    private List<WarehouseItemCmd> items;
    /**
     * 平台参数
     */
    @Length(max = 1000)
    private String platformJson;

}
