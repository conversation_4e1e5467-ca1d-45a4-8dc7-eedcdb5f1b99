package com.xtc.marketing.adapterservice.shop.dto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 店铺电子面单发货条码
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShopLogisticsBarcodeCmd {

    /**
     * 条码
     */
    @Length(max = 50)
    private String barcode;
    /**
     * imei码
     */
    @Length(max = 50)
    private String imei;

}
