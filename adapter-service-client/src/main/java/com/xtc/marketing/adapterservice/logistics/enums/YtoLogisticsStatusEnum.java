package com.xtc.marketing.adapterservice.logistics.enums;

import lombok.Getter;

/**
 * 圆通物流状态枚举
 */
@Getter
public enum YtoLogisticsStatusEnum {
    /**
     * 已揽收
     */
    GOT("已揽收"),
    /**
     * 已收入
     */
    ARRIVAL("已收入"),
    /**
     * 已发出
     */
    DEPARTURE("已发出"),
    /**
     * 派件
     */
    SENT_SCAN("派件"),
    /**
     * 自提柜入柜
     */
    INBOUND("自提柜入柜"),
    /**
     * 签收成功
     */
    SIGNED("签收成功"),
    /**
     * 签收失败
     */
    FAILED("签收失败"),
    /**
     * 转寄
     */
    FORWARDING("转寄"),
    /**
     * 退回
     */
    TMS_RETURN("退回"),
    /**
     * 其他
     */
    OTHER("其他");

    private final String desc;

    YtoLogisticsStatusEnum(String desc) {
        this.desc = desc;
    }
}
