package com.xtc.marketing.adapterservice.logistics.dto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 下物流单并生成运单号参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsCreateOrderCmd {

    /**
     * 业务账号
     */
    @NotBlank
    @Length(max = 50)
    private String bizAccount;
    /**
     * 订单id，不传则默认生成UUID
     */
    private String orderId;
    /**
     * 快件产品类别
     */
    private String expressTypeId;
    /**
     * 预约上门时间开始
     */
    private LocalDateTime pickUpTimeStart;
    /**
     * 预约上门时间结束（不同平台不一定有效，平台可能有固定结束时间）
     */
    private LocalDateTime pickUpTimeEnd;
    /**
     * 寄件人姓名
     */
    @NotBlank
    @Length(max = 50)
    private String senderName;
    /**
     * 寄件人电话
     */
    @NotBlank
    @Length(max = 50)
    private String senderMobile;
    /**
     * 寄件人省份
     */
    @NotBlank
    @Length(max = 50)
    private String senderProvince;
    /**
     * 寄件人城市
     */
    @NotBlank
    @Length(max = 50)
    private String senderCity;
    /**
     * 寄件人区县
     */
    @NotBlank
    @Length(max = 50)
    private String senderDistrict;
    /**
     * 寄件人乡镇
     */
    private String senderTown;
    /**
     * 寄件人详细地址
     */
    @NotBlank
    @Length(max = 200)
    private String senderAddress;
    /**
     * 收件人姓名
     */
    @NotBlank
    @Length(max = 50)
    private String receiverName;
    /**
     * 收件人电话
     */
    @NotBlank
    @Length(max = 50)
    private String receiverMobile;
    /**
     * 收件人省份
     */
    @NotBlank
    @Length(max = 50)
    private String receiverProvince;
    /**
     * 收件人城市
     */
    @NotBlank
    @Length(max = 50)
    private String receiverCity;
    /**
     * 收件人区县
     */
    @Length(max = 50)
    private String receiverDistrict;
    /**
     * 收件人乡镇
     */
    private String receiverTown;
    /**
     * 收件人详细地址
     */
    @NotBlank
    @Length(max = 200)
    private String receiverAddress;
    /**
     * 备注，打印在面单上的备注内容
     */
    private String remark;
    /**
     * 订单总重量（单位：kg，保留小数点后两位）
     */
    private String weight;
    /**
     * 体积（单位：cm3，保留小数点后两位）
     */
    private String volume;
    /**
     * 货物集合，最多 200 个
     */
    @Valid
    @Size(max = 200)
    private List<LogisticsCargoCmd> cargos;
    /**
     * 平台参数
     */
    @Length(max = 1000)
    private String platformJson;

}
