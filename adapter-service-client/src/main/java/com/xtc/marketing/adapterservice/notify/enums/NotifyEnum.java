package com.xtc.marketing.adapterservice.notify.enums;

import lombok.Getter;

/**
 * 通知枚举
 */
@Getter
public enum NotifyEnum {
    /**
     * 顺丰仓库入库
     */
    SF_WAREHOUSE_INBOUND("SF", "WAREHOUSE", "inbound"),
    /**
     * 顺丰仓库出库
     */
    SF_WAREHOUSE_OUTBOUND("SF", "WAREHOUSE", "outbound"),
    /**
     * 顺丰物流国补通知
     */
    SF_LOGISTICS_NATIONAL_SUBSIDY("SF", "LOGISTICS", "national_subsidy"),
    /**
     * 微信视频号小店消息通知（根据消息的事件类型判断是什么通知）
     */
    WECHAT_CHANNELS_SHOP_NOTIFY("WECHAT_CHANNELS_SHOP", "SHOP", "notify"),
    /**
     * 天猫订单修改地址
     */
    TMALL_SHOP_MODIFY_ADDRESS("TMALL", "SHOP", "modify_address"),
    /**
     * 抖音订单修改地址
     */
    TIKTOK_SHOP_MODIFY_ADDRESS("TIKTOK", "SHOP", "modify_address"),
    /**
     * 快手订单修改地址
     */
    KUAISHOU_SHOP_MODIFY_ADDRESS("KUAISHOU", "SHOP", "modify_address"),
    /**
     * 小红书订单修改地址
     */
    XIAOHONGSHU_SHOP_MODIFY_ADDRESS("XIAOHONGSHU", "SHOP", "modify_address"),
    /**
     * 抖音消息通知
     */
    TIKTOK_SHOP_MESSAGE("TIKTOK", "SHOP", "message"),
    ;

    /**
     * 平台编码
     */
    private final String platformCode;
    /**
     * 模块编码
     */
    private final String moduleCode;
    /**
     * 场景编码
     */
    private final String scenarioCode;

    NotifyEnum(String platformCode, String moduleCode, String scenarioCode) {
        this.platformCode = platformCode;
        this.moduleCode = moduleCode;
        this.scenarioCode = scenarioCode;
    }
}