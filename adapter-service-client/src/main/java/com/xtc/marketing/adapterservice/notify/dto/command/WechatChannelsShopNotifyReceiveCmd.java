package com.xtc.marketing.adapterservice.notify.dto.command;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 微信视频号小店消息通知
 */
@Getter
@Setter
@ToString
public class WechatChannelsShopNotifyReceiveCmd {

    /**
     * 小店UserName
     */
    @Length(max = 200)
    @JsonProperty("ToUserName")
    private String toUserName;
    /**
     * 消息密文
     */
    @NotBlank
    @Length(max = 5000)
    @JsonProperty("Encrypt")
    private String encrypt;

}
