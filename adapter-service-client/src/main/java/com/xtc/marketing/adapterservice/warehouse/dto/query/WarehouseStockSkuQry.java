package com.xtc.marketing.adapterservice.warehouse.dto.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 库存查询sku
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseStockSkuQry {

    /**
     * skuId
     */
    @NotBlank
    @Length(max = 50)
    private String skuId;

}
