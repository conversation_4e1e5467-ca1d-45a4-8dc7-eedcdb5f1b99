package com.xtc.marketing.adapterservice.shop.dto.query;

import com.xtc.marketing.marketingcomponentdto.dto.BasePageQuery;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 查询退款单分页列表
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class RefundPageQry extends BasePageQuery {

    /**
     * 店铺代码
     */
    @NotBlank
    @Length(max = 50)
    private String shopCode;
    /**
     * 更新开始时间
     * （修改时间跨度不能大于一天，格式：yyyy-MM-dd HH:mm:ss）
     */
    private LocalDateTime updateTimeStart;
    /**
     * 更新结束时间
     * （修改时间跨度不能大于一天，格式：yyyy-MM-dd HH:mm:ss，建议使用30分钟以内的时间跨度，能大大提高响应速度和成功率）
     */
    private LocalDateTime updateTimeEnd;
    /**
     * 订单号（部分平台支持）
     */
    @Length(max = 50)
    private String orderNo;
    /**
     * 查询字段，不填会有默认（英文逗号分割）
     */
    private String fields;
    /**
     * 启用 has_next 的分页方式，如果指定 true 则返回的结果中不包含总记录数，但是会新增一个是否存在下一页的的字段
     * <ul>
     * <li>采用入参use_has_next=true的分页方式可以避免每次API请求时对淘宝数据库产生的count(*)，从而显著提升速度和稳定性。
     * <li>由于获取增量订单接口是用修改时间过滤的，而修改时间是可变的，所以需要从后往前翻页才能避免漏单。从后往前翻页必须要知道最后一页，所以必须在首次API请求时采用use_has_next=false方式统计订单总数，计算出总页数，然后再设置use_has_next=true终止订单统计，从后往前翻页。
     * </ul>
     */
    private Boolean useHasNext;
    /**
     * 获取下一页数据的 key
     */
    @Length(max = 200)
    private String nextKey;

}
