package com.xtc.marketing.adapterservice.bill;

import com.xtc.marketing.adapterservice.bill.dto.query.BillDownloadQry;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 对接服务 - 账单模块
 */
@FeignClient(
        contextId = "adapterBillFeignClient",
        name = "adapter-bill-feign",
        url = "${xtc.feign.client.adapter-service.url}"
)
public interface AdapterBillFeignClient {

    /**
     * 下载账单文件
     *
     * @param qry 参数
     * @return 账单文件
     * @apiNote 只能获取单个日期的账单，多个日期账单需要自行遍历处理
     */
    @GetMapping("/api/bill/download")
    ResponseEntity<Resource> billDownload(@SpringQueryMap BillDownloadQry qry);

}
