package com.xtc.marketing.adapterservice.invoice.enums;

import lombok.Getter;

/**
 * 电商平台代码
 */
@Getter
public enum InvoicePlatformCode {

    /**
     * 天猫
     */
    TM("TM"),
    /**
     * 其他
     */
    OTHER("OTHER");

    private final String code;

    InvoicePlatformCode(String code) {
        this.code = code;
    }

    /**
     * 查询平台类型
     *
     * @param code 代码
     * @return 平台类型
     */
    public static InvoicePlatformCode getPlatformCode(String code) {
        for (InvoicePlatformCode status : InvoicePlatformCode.values()) {
            if (code.equals(status.getCode())) {
                return status;
            }
        }
        return InvoicePlatformCode.OTHER;
    }

}
