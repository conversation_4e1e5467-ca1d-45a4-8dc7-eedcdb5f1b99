package com.xtc.marketing.adapterservice.logistics.dto.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 查询快递路由参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsRouteListQry {

    /**
     * 业务账号
     */
    @NotBlank
    @Length(max = 50)
    private String bizAccount;
    /**
     * 运单号
     */
    @NotBlank
    @Length(max = 50)
    private String waybillNo;
    /**
     * 快递场景
     */
    private String waybillScene;

}
