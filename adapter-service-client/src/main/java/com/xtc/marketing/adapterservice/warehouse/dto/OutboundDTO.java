package com.xtc.marketing.adapterservice.warehouse.dto;

import com.xtc.marketing.marketingcomponentdto.dto.BasePageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 出库单分页列表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutboundDTO extends BasePageQuery {

    /**
     * sku物料代码
     */
    private String skuErpCode;
    /**
     * 平台订单id
     */
    private String orderId;
    /**
     * 商品编号
     */
    private String goodsNo;

}
