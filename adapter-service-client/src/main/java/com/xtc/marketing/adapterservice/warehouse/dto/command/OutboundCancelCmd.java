package com.xtc.marketing.adapterservice.warehouse.dto.command;

import com.xtc.marketing.adapterservice.warehouse.enums.WarehouseEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 出库单取消
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutboundCancelCmd {

    /**
     * 仓库
     */
    @NotNull
    private WarehouseEnum warehouse;

    /**
     * 出库单号（业务方单号）
     */
    @NotBlank
    @Length(max = 50)
    private String orderId;

}
