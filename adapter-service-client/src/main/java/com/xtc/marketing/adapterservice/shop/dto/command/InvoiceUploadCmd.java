package com.xtc.marketing.adapterservice.shop.dto.command;

import com.xtc.marketing.adapterservice.invoice.enums.InvoiceCreateType;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTitleTypeEnum;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 发票上传参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceUploadCmd {

    /**
     * 店铺代码
     */
    @NotBlank
    @Length(max = 50)
    private String shopCode;
    /**
     * 订单号
     */
    @NotBlank
    @Length(max = 50)
    private String orderNo;
    /**
     * 发票文件（base64编码）
     */
    @NotBlank
    private String invoiceFileBase;
    /**
     * 发票类型
     */
    private InvoiceCreateType createType;
    /**
     * 发票抬头类型
     */
    private InvoiceTitleTypeEnum invoiceTitleType;
    /**
     * 发票抬头
     */
    private String invoiceTitle;
    /**
     * 发票类型
     */
    private InvoiceTypeEnum invoiceType;
    /**
     * 开票金额 单位:分
     */
    private Integer invoiceAmount;
    /**
     * 开票号码
     */
    private String blueInvoiceNo;
    /**
     * 开票代码
     */
    private String blueInvoiceCode;
    /**
     * 开票时间
     */
    private LocalDateTime blueTime;
    /**
     * 收款方税号
     */
    private String payeeRegisterNo;
    /**
     * 收款方名称
     */
    private String payeeName;
    /**
     * 开票人
     */
    private String payeeOperator;
    /**
     * 购买方抬头
     */
    private String payerName;
    /**
     * 购买方银行
     */
    private String payerBank;
    /**
     * 购买方地址
     */
    private String payerAddress;
    /**
     * 购买方电话
     */
    private String payerPhone;
    /**
     * 购买方账号
     */
    private String payerAccount;
    /**
     * 税号
     */
    private String taxNo;
    /**
     * 总价
     */
    private Integer sumPrice;
    /**
     * 税额
     */
    private Integer tax;
    /**
     * 税率
     */
    private String taxRate;
    /**
     * 发票明细列表
     */
    private List<InvoiceItemCmd> invoiceItems;

}
