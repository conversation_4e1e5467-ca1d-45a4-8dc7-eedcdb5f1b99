package com.xtc.marketing.adapterservice.invoice;

import com.xtc.marketing.adapterservice.invoice.dto.InvoiceResultDTO;
import com.xtc.marketing.adapterservice.invoice.dto.command.InvoiceCreateCmd;
import com.xtc.marketing.adapterservice.invoice.dto.command.InvoiceCreateRedCmd;
import com.xtc.marketing.adapterservice.invoice.dto.command.InvoiceSerialNoCmd;
import com.xtc.marketing.adapterservice.invoice.dto.query.InvoiceResultQry;
import com.xtc.marketing.marketingcomponentdto.dto.MultiResponse;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 对接服务 - 开票模块
 */
@FeignClient(
        contextId = "adapterInvoiceFeignClient",
        name = "adapter-invoice-feign",
        url = "${xtc.feign.client.adapter-service.url}"
)
public interface AdapterInvoiceFeignClient {

    /**
     * 开票
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/api/invoice/create-invoice")
    boolean createInvoice(@RequestBody InvoiceCreateCmd cmd);

    /**
     * 冲红
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/invoice/create-red-invoice")
    boolean createRedInvoice(@RequestBody InvoiceCreateRedCmd cmd);

    /**
     * 查询开票结果
     *
     * @param qry 参数
     * @return 开票结果
     */
    @GetMapping("/api/invoice/result")
    MultiResponse<InvoiceResultDTO> listInvoiceResult(@SpringQueryMap InvoiceResultQry qry);

    /**
     * 生成开票流水号
     *
     * @param cmd 参数
     * @return 开票流水号
     */
    @GetMapping("/api/invoice/create-serial-no")
    SingleResponse<String> createSerialNo(@SpringQueryMap InvoiceSerialNoCmd cmd);

}
