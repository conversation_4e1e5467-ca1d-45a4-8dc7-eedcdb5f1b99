package com.xtc.marketing.adapterservice.warehouse.dto.command;

import com.xtc.marketing.adapterservice.warehouse.enums.InventoryStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 仓库货物
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseItemCmd {

    /**
     * skuId
     */
    @NotBlank
    @Length(max = 50)
    private String skuId;
    /**
     * 数量
     */
    @NotNull
    @Range(min = 1, max = 10000)
    private Integer quantity;
    /**
     * 库存类型
     */
    private InventoryStatus inventoryStatus;

}

