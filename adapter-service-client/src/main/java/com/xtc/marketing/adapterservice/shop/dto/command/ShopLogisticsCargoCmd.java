package com.xtc.marketing.adapterservice.shop.dto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 店铺电子面单货物
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShopLogisticsCargoCmd {

    /**
     * 货物名称
     */
    @NotBlank
    @Length(max = 200)
    private String name;
    /**
     * 货物数量
     */
    @NotNull
    @Range(min = 1, max = 10000)
    private Integer quantity;
    /**
     * 货物单价
     */
    @Range(min = 0, max = 99999999)
    private Integer unitPrice;
    /**
     * 货物总价
     */
    @Range(min = 0, max = 99999999)
    private Integer priceTotal;

}
