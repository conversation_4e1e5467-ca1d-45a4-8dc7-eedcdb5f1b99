package com.xtc.marketing.adapterservice.invoice.enums;

import lombok.Getter;

/**
 * 开票类型
 */
@Getter
public enum InvoiceCreateType {

    /**
     * 冲红
     */
    RED("red"),
    /**
     * 开票
     */
    BLUE("blue"),
    /**
     * 未知类型
     */
    UNKNOWN("unknown");

    private final String type;

    InvoiceCreateType(String type) {
        this.type = type;
    }

    /**
     * 查询开票类型枚举
     *
     * @param type 类型
     * @return 开票类型枚举
     */
    public static InvoiceCreateType getInvoiceCreateType(String type) {
        for (InvoiceCreateType status : InvoiceCreateType.values()) {
            if (type.equals(status.getType())) {
                return status;
            }
        }
        return InvoiceCreateType.UNKNOWN;
    }

}
