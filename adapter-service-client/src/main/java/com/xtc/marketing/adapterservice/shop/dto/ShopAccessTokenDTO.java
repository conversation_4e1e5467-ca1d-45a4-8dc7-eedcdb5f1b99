package com.xtc.marketing.adapterservice.shop.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 店铺 AccessToken
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShopAccessTokenDTO {

    /**
     * 平台代码
     */
    private String platformCode;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 店铺代码
     */
    private String shopCode;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 应用过期时间
     */
    private LocalDateTime appExpireTime;
    /**
     * 应用access_token
     */
    private String appAccessToken;
    /**
     * 应用refresh_token
     */
    private String appRefreshToken;

}
