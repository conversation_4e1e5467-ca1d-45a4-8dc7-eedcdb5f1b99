package com.xtc.marketing.adapterservice.shop.dto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 店铺电子面单预约上门参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShopLogisticsReserveCmd {

    /**
     * 业务账号（例：月结卡号）
     */
    @NotBlank
    @Length(max = 50)
    private String bizAccount;
    /**
     * 预约上门时间开始
     */
    @NotNull
    private LocalDateTime pickUpTimeStart;
    /**
     * 预约上门时间结束
     */
    @NotNull
    private LocalDateTime pickUpTimeEnd;

}
