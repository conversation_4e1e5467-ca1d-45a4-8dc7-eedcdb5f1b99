package com.xtc.marketing.adapterservice.shop.dto.command;

import com.xtc.marketing.adapterservice.shop.enums.LogisticsCompanyEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 取消电子面单
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShopLogisticsOrderCancelCmd {

    /**
     * 店铺代码
     */
    @NotBlank
    @Length(max = 50)
    private String shopCode;
    /**
     * 物流公司名称
     */
    @NotNull
    private LogisticsCompanyEnum logisticsCompany;
    /**
     * 物流单号
     */
    @NotBlank
    @Length(max = 50)
    private String waybillNo;

}
