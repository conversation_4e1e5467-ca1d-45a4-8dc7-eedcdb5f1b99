package com.xtc.marketing.adapterservice.warehouse.dto.command;

import com.xtc.marketing.adapterservice.warehouse.enums.WarehouseEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 出库单拦截
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutboundInterceptCmd {

    /**
     * 仓库
     */
    @NotNull
    private WarehouseEnum warehouse;

    /**
     * 出库单号（业务方单号）
     */
    @NotBlank
    @Length(max = 50)
    private String orderId;

    /**
     * 业务账号
     */
    @Length(max = 50)
    private String bizAccount;

    /**
     * 收件人姓名
     */
    @Length(max = 50)
    private String receiverName;

    /**
     * 收件人电话
     */
    @Length(max = 50)
    private String receiverMobile;

    /**
     * 收件人省份
     */
    @Length(max = 50)
    private String receiverProvince;

    /**
     * 收件人城市
     */
    @Length(max = 50)
    private String receiverCity;

    /**
     * 收件人区县
     */
    @Length(max = 50)
    private String receiverDistrict;

    /**
     * 收件人详细地址
     */
    @Length(max = 200)
    private String receiverAddress;

}
