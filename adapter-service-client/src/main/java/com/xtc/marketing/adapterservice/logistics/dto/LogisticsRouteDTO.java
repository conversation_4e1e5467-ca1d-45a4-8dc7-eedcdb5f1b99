package com.xtc.marketing.adapterservice.logistics.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 快递路由
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsRouteDTO {

    /**
     * 节点（省市区地址）
     */
    private String node;
    /**
     * 更新时间 yyyy-MM-dd HH:mm:ss
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime time;
    /**
     * 详情
     */
    private String detail;
    /**
     * 标题
     */
    private String title;
    /**
     * 路由代码
     */
    private String code;

}
