package com.xtc.marketing.adapterservice.warehouse.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseStockDTO {

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * skuId
     */
    private String skuId;

    /**
     * 库存状态：正品、残品
     */
    private String inventoryStatus;

    /**
     * 在库库存数量
     */
    private Integer onHandQuantity;

    /**
     * 在途库存数量
     */
    private Integer inTransitQuantity;

    /**
     * 总库存数量 = 在库库存数量 + 在途库存数量
     */
    private Integer totalQuantity;

    /**
     * 可用库存数量 = 在库库存数量 - 占用库存数量
     */
    private Integer availableQuantity;

}
