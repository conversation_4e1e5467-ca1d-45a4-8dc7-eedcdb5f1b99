package com.xtc.marketing.adapterservice.invoice.dto.command;

import com.xtc.marketing.adapterservice.invoice.enums.InvoicePlatform;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceCreateRedCmd {

    /**
     * 店铺代码
     */
    @Length(max = 50)
    private String shopCode;
    /**
     * 发票平台
     */
    @NotNull
    private InvoicePlatform invoicePlatform;
    /**
     * 税号
     */
    @NotBlank
    private String taxNo;
    /**
     * 蓝票流水号，优先级高于发票代码+发票号码
     */
    private String blueSerialNo;
    /**
     * 红票流水号
     */
    @NotBlank
    private String redSerialNo;
    /**
     * 蓝票发票代码
     */
    private String blueInvoiceCode;
    /**
     * 蓝票发票号码
     */
    private String blueInvoiceNo;

}
