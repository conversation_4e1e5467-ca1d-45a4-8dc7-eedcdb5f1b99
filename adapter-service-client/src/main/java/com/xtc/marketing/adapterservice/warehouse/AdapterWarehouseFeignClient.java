package com.xtc.marketing.adapterservice.warehouse;

import com.xtc.marketing.adapterservice.warehouse.dto.OutboundDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.OutboundDetailDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.WarehouseStockDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.command.*;
import com.xtc.marketing.adapterservice.warehouse.dto.query.OutboundDetailQry;
import com.xtc.marketing.adapterservice.warehouse.dto.query.OutboundPageQry;
import com.xtc.marketing.adapterservice.warehouse.dto.query.WarehouseStockQry;
import com.xtc.marketing.marketingcomponentdto.dto.MultiResponse;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.marketing.marketingcomponentdto.dto.Response;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 对接服务 - 仓库模块
 */
@FeignClient(
        contextId = "adapterWarehouseFeignClient",
        name = "adapter-warehouse-feign",
        url = "${xtc.feign.client.adapter-service.url}"
)
public interface AdapterWarehouseFeignClient {

    /**
     * 分页查询出库单列表
     *
     * @param qry 参数
     * @return 出库单分页列表
     */
    @GetMapping("/api/warehouse/outbound-page")
    PageResponse<OutboundDTO> pageOutbound(@SpringQueryMap OutboundPageQry qry);

    /**
     * 查询出库单详情
     *
     * @param qry 参数
     * @return 出库单详情
     */
    @GetMapping("/api/warehouse/outbound-detail")
    SingleResponse<OutboundDetailDTO> getOutboundDetail(@SpringQueryMap OutboundDetailQry qry);

    /**
     * 查询库存
     *
     * @param qry 查询参数
     * @return 库存
     */
    @PostMapping("/api/warehouse/query-stocks")
    MultiResponse<WarehouseStockDTO> queryStocks(@RequestBody WarehouseStockQry qry);

    /**
     * 申请入库
     *
     * @param cmd 参数
     * @return 平台入库单号
     */
    @PostMapping("/api/warehouse/apply-inbound")
    SingleResponse<String> applyInbound(@RequestBody InboundApplyCmd cmd);

    /**
     * 取消入库
     *
     * @param cmd 参数
     * @return 入库单号（业务方单号）
     */
    @PostMapping("/api/warehouse/cancel-inbound")
    SingleResponse<String> cancelInbound(@RequestBody InboundCancelCmd cmd);

    /**
     * 申请出库
     *
     * @param cmd 参数
     * @return 平台出库单号
     */
    @PostMapping("/api/warehouse/apply-outbound")
    SingleResponse<String> applyOutbound(@RequestBody OutboundApplyCmd cmd);

    /**
     * 取消出库
     *
     * @param cmd 参数
     * @return 业务订单号
     */
    @PostMapping("/api/warehouse/cancel-outbound")
    SingleResponse<String> cancelOutbound(@RequestBody OutboundCancelCmd cmd);

    /**
     * 拦截出库
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/api/warehouse/intercept-outbound")
    Response interceptOutbound(@RequestBody OutboundInterceptCmd cmd);

}
