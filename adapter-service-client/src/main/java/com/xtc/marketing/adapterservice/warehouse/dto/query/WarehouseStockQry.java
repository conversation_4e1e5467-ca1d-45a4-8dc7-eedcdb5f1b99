package com.xtc.marketing.adapterservice.warehouse.dto.query;

import com.xtc.marketing.adapterservice.warehouse.enums.WarehouseEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 库存查询
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseStockQry {

    /**
     * 仓库
     */
    @NotNull
    private WarehouseEnum warehouse;

    /**
     * 仓库编码
     */
    @NotBlank
    @Length(max = 50)
    private String warehouseCode;

    /**
     * sku集合，最多 100 个
     */
    @Valid
    @NotEmpty
    @Size(min = 1, max = 100)
    private List<String> skuIds;

    /**
     * 店铺代码
     */
    @Length(max = 50)
    private String shopCode;

}
