package com.xtc.marketing.adapterservice.notify.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 仓库出库货物
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseOutboundNotifyCargoDTO {

    /**
     * 商品编号
     */
    private String skuId;
    /**
     * 商品名称
     */
    private String skuName;
    /**
     * 出库数量
     */
    private Integer quantity;
    /**
     * 库存类型
     */
    private String inventoryStatus;
    /**
     * 出库条码集合
     */
    private List<String> barcodes;

}
