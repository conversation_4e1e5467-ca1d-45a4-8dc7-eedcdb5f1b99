package com.xtc.marketing.adapterservice.invoice.dto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceCreateItemCmd {

    /**
     * 总价，格式：100.00； 当开红票时，该字段为负数
     */
    @NotBlank
    private String sumPrice;
    /**
     * 税额； 当开红票时，该字段为负数
     */
    @NotBlank
    private String tax;
    /**
     * 单价，格式：100.00。新版电子发票，折扣行此参数不能传，非折扣行必传；红票、蓝票都为正数
     */
    private String price;
    /**
     * 税收分类编码
     */
    private String itemNo;
    /**
     * 单位
     */
    private String unit;
    /**
     * 商品IMIE号(不用传，将废弃)
     */
    private String imei;
    /**
     * 发票项目名称（或商品名称）
     */
    @NotBlank
    private String itemName;
    /**
     * 税率。税率只能为0或0.03或0.04或0.06或0.11或0.13或0.17
     */
    @NotBlank
    private String taxRate;
    /**
     * 发票行性质。0表示正常行，1表示折扣行，2表示被折扣行。比如充电器单价100元，折扣10元，则明细为2行，充电器行性质为2，折扣行性质为1。如果充电器没有折扣，则值应为0
     */
    @NotBlank
    private String rowType;
    /**
     * 数量
     */
    private String quantity;
    /**
     * 规格型号,可选
     */
    private String specification;
    /**
     * 价税合计。(等于sumPrice和tax之和)
     */
    private String amount;

}
