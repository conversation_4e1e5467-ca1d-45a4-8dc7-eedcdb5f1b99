package com.xtc.marketing.adapterservice.notify.dto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.servlet.http.HttpServletRequest;

/**
 * 通知接收参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotifyReceiveCmd {

    /**
     * 店铺编码
     */
    private String shopCode;
    /**
     * 接收参数
     */
    private String data;
    /**
     * 接收请求
     */
    private HttpServletRequest request;

}
