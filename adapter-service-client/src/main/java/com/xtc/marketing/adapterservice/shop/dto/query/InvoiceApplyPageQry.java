package com.xtc.marketing.adapterservice.shop.dto.query;

import com.xtc.marketing.marketingcomponentdto.dto.BasePageQuery;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 分页查询发票申请
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class InvoiceApplyPageQry extends BasePageQuery {

    /**
     * 店铺代码
     */
    @NotBlank
    @Length(max = 50)
    private String shopCode;

    /**
     * 开始时间
     */
    @NotNull
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @NotNull
    private LocalDateTime endTime;

}
