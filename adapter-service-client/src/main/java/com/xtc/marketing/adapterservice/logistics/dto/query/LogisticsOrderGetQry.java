package com.xtc.marketing.adapterservice.logistics.dto.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsOrderGetQry {

    /**
     * 业务账号
     */
    @NotBlank
    @Length(max = 50)
    private String bizAccount;

    /**
     * 订单id
     */
    @NotBlank
    @Length(max = 50)
    private String orderId;

    /**
     * 快递场景
     */
    private String waybillScene;

}
