package com.xtc.marketing.adapterservice.notify.dto.command;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 小红书消息通知
 */
@Getter
@Setter
@ToString
public class XiaohongshuNotifyReceiveCmd {

    /**
     * 消息类型
     */
    @Length(max = 200)
    private String msgTag;
    /**
     * 商家店铺Id
     */
    @Length(max = 200)
    private String sellerId;
    /**
     * 消息内容
     */
    @Length(max = 5000)
    private String data;

}
