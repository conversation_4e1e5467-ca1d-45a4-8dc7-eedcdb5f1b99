package com.xtc.marketing.adapterservice.shop.dto.command;

import com.xtc.marketing.adapterservice.shop.enums.BarcodeUploadCategoryEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 条码上传参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BarcodeUploadCmd {

    /**
     * 店铺代码
     */
    @NotBlank
    @Length(max = 50)
    private String shopCode;
    /**
     * IMEI码
     */
    @Length(max = 50)
    private String imei;
    /**
     * 69码
     */
    @NotBlank
    @Length(max = 50)
    private String barcode69;
    /**
     * sn条码
     */
    @NotBlank
    @Length(max = 50)
    private String barcode;
    /**
     * 上传品类
     */
    @NotNull
    private BarcodeUploadCategoryEnum barcodeUploadCategory;

}
