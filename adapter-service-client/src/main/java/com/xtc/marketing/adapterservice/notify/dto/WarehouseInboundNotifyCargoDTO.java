package com.xtc.marketing.adapterservice.notify.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 仓库入库货物
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseInboundNotifyCargoDTO {

    /**
     * 商品编号
     */
    private String skuId;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 收货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiptTime;

    /**
     * 计划数量
     */
    private Integer planQuantity;

    /**
     * 实收数量
     */
    private Integer actualQuantity;

    /**
     * 拒收数量
     */
    private Integer rejectionQuantity;

    /**
     * 入库条码集合
     */
    private List<String> barcodes;

}
