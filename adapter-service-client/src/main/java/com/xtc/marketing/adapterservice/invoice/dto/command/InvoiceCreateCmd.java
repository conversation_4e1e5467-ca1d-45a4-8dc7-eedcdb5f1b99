package com.xtc.marketing.adapterservice.invoice.dto.command;

import com.xtc.marketing.adapterservice.invoice.enums.InvoiceCreateType;
import com.xtc.marketing.adapterservice.invoice.enums.InvoicePlatform;
import com.xtc.marketing.adapterservice.invoice.enums.InvoicePlatformCode;
import com.xtc.marketing.adapterservice.invoice.enums.InvoiceType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceCreateCmd {

    /**
     * 店铺代码
     */
    @Length(max = 50)
    private String shopCode;
    /**
     * 发票平台
     */
    @NotNull
    private InvoicePlatform invoicePlatform;
    /**
     * 电商平台代码
     */
    @NotNull
    private InvoicePlatformCode platformCode;
    /**
     * 开票流水号，唯一标志开票请求。如果两次请求流水号相同，则表示重复请求。
     * 请调用平台统一流水号获取接口，alibaba.einvoice.serialno.generate。
     */
    @NotBlank
    private String serialNo;
    /**
     * 订单号
     */
    @NotBlank
    private String orderNo;
    /**
     * 开票类型
     * 蓝票blue,红票red，默认blue
     */
    @NotNull
    private InvoiceCreateType createType;
    /**
     * 发票类型
     */
    private InvoiceType invoiceType;
    /**
     * 付款方名称, 对应发票抬头
     */
    @NotBlank
    private String invoiceTitle;
    /**
     * 电子发票明细
     */
    @NotNull
    private List<InvoiceCreateItemCmd> invoiceItems;
    /**
     * 原发票代码(开红票时传入)
     */
    private String blueInvoiceCode;
    /**
     * 原发票号码(开红票时传入)
     */
    private String blueInvoiceNo;
    /**
     * 开票人
     */
    private String payeeOperator;
    /**
     * 收款方名称
     */
    private String payeeName;
    /**
     * 收款方银行
     */
    private String payeeBankName;
    /**
     * 收款方银行账号
     */
    private String payeeBankAccount;
    /**
     * 收款方地址
     */
    private String payeeAddress;
    /**
     * 开票金额
     * 当开红票时，该字段为负数
     */
    @NotBlank
    private String invoiceAmount;
    /**
     * 开票日期
     * 格式"YYYY-MM-DD HH:SS:MM"
     */
    private LocalDateTime invoiceTime;
    /**
     * 收款人
     */
    private String payeeReceiver;
    /**
     * 收款方电话
     */
    private String payeePhone;
    /**
     * 收款方税号
     */
    private String payeeRegisterNo;
    /**
     * 付款方税务登记证号。对企业开具电子发票时必填。
     */
    private String payerRegisterNo;
    /**
     * 付款方开票开户银行
     */
    private String payerBankName;
    /**
     * 付款方开票开户银行账号
     */
    private String payerBankAccount;
    /**
     * 付款方地址
     */
    private String payerAddress;
    /**
     * 付款方电话
     */
    private String payerMobile;
    /**
     * 复核人
     */
    private String payeeChecker;
    /**
     * 合计金额 当开红票时，该字段为负数
     */
    @NotBlank
    private String priceTotal;
    /**
     * 合计税额 当开红票时，该字段为负数
     */
    @NotBlank
    private String taxTotal;
    /**
     * 发票备注
     */
    private String invoiceMemo;

}
