package com.xtc.marketing.adapterservice.bill.dto.query;

import com.xtc.marketing.adapterservice.bill.enums.BillPlatformEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 下载账单
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillDownloadQry {

    /**
     * 账单平台
     */
    @NotNull
    private BillPlatformEnum billPlatform;

    /**
     * 账单日期，格式：yyyy-MM-dd
     */
    @NotNull
    private LocalDate billDate;

    /**
     * 业务代码（基础服务数据）
     */
    private String bizCode;

    /**
     * 商户id
     */
    private String mchId;

}
