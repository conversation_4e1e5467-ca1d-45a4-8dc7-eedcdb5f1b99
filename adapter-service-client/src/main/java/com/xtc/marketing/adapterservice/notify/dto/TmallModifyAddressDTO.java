package com.xtc.marketing.adapterservice.notify.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TmallModifyAddressDTO {

    /**
     * 店铺编码
     */
    private String shopCode;
    /**
     * 店铺订单id
     */
    private String shopOrderId;
    /**
     * 收件人密文数据
     */
    private String receiverOaid;
    /**
     * 收件人姓名
     */
    private String receiverName;
    /**
     * 收件人手机号
     */
    private String receiverMobile;
    /**
     * 收件人省份
     */
    private String receiverProvince;
    /**
     * 收件人城市
     */
    private String receiverCity;
    /**
     * 收件人区县
     */
    private String receiverDistrict;
    /**
     * 收件人乡镇
     */
    private String receiverTown;
    /**
     * 收件人详细地址
     */
    private String receiverAddress;

}
