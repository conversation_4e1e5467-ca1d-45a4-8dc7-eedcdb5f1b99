package com.xtc.marketing.adapterservice.shop.dto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 退货确认入仓参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundGoodsToWarehouseCmd {

    /**
     * 店铺代码
     */
    @NotBlank
    @Length(max = 50)
    private String shopCode;
    /**
     * 订单号
     */
    @NotBlank
    @Length(max = 50)
    private String orderNo;
    /**
     * 退款单号
     */
    @NotBlank
    @Length(max = 50)
    private String refundId;
    /**
     * 运单号
     */
    @Length(max = 50)
    private String waybillNo;

}
