package com.xtc.marketing.adapterservice.notify.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 推送记录
 */
@Getter
@Setter
@ToString
public class PushLogDTO {

    /**
     * 唯一标识
     */
    private Long id;
    /**
     * 业务名称
     */
    private String bizName;
    /**
     * 模块代码
     */
    private String moduleCode;
    /**
     * 平台代码
     */
    private String platformCode;
    /**
     * 场景代码
     */
    private String scenarioCode;
    /**
     * 推送地址
     */
    private String pushUrl;
    /**
     * 数据id
     */
    private String dataId;
    /**
     * 接收记录id
     */
    private Long receiveLogId;
    /**
     * 推送成功标识
     */
    private Boolean pushSuccess;
    /**
     * 推送结果
     */
    private String response;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
