package com.xtc.marketing.adapterservice.notify.dto.query;

import com.xtc.marketing.marketingcomponentdto.dto.BasePageQuery;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Positive;

/**
 * 查询接收记录分页列表参数
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ReceiveLogPageQry extends BasePageQuery {

    /**
     * 唯一标识
     */
    @Positive
    private Long id;
    /**
     * 数据id
     */
    @Length(max = 50)
    private String dataId;

}
