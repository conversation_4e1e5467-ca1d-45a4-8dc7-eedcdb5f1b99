package com.xtc.marketing.adapterservice.notify.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 接收记录
 */
@Getter
@Setter
@ToString
public class ReceiveLogDTO {

    /**
     * 唯一标识
     */
    private Long id;
    /**
     * 模块代码
     */
    private String moduleCode;
    /**
     * 平台代码
     */
    private String platformCode;
    /**
     * 场景代码
     */
    private String scenarioCode;
    /**
     * 数据id
     */
    private String dataId;
    /**
     * 数据
     */
    private String rawData;
    /**
     * 通知响应结果
     */
    private String responseStr;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
