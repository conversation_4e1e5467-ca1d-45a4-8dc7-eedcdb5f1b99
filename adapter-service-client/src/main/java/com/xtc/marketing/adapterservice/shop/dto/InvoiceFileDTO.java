package com.xtc.marketing.adapterservice.shop.dto;

import lombok.Data;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * 发票文件
 */
@Data
public class InvoiceFileDTO implements MultipartFile {

    private final String name;
    private String originalFilename;
    private String contentType;
    private final byte[] content;

    public InvoiceFileDTO(String name, byte[] content) {
        this(name, "", (String) null, (byte[]) content);
    }

    public InvoiceFileDTO(String name, InputStream contentStream) throws IOException {
        this(name, "", (String) null, (byte[]) FileCopyUtils.copyToByteArray(contentStream));
    }

    public InvoiceFileDTO(String name, @Nullable String originalFilename, @Nullable String contentType, @Nullable byte[] content) {
        Assert.hasLength(name, "Name must not be null");
        this.name = name;
        this.originalFilename = originalFilename != null ? originalFilename : "";
        this.contentType = contentType;
        this.content = content != null ? content : new byte[0];
    }

    public InvoiceFileDTO(String name, @Nullable String originalFilename, @Nullable String contentType, InputStream contentStream) throws IOException {
        this(name, originalFilename, contentType, FileCopyUtils.copyToByteArray(contentStream));
    }

    public boolean isEmpty() {
        return this.content.length == 0;
    }

    public long getSize() {
        return (long) this.content.length;
    }

    public byte[] getBytes() throws IOException {
        return this.content;
    }

    public InputStream getInputStream() throws IOException {
        return new ByteArrayInputStream(this.content);
    }

    public void transferTo(File dest) throws IOException, IllegalStateException {
        FileCopyUtils.copy(this.content, dest);
    }

}
