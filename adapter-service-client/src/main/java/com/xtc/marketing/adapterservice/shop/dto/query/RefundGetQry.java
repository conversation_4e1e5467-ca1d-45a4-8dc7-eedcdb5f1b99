package com.xtc.marketing.adapterservice.shop.dto.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 查询退款单
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundGetQry {

    /**
     * 店铺代码
     */
    @NotBlank
    @Length(max = 50)
    private String shopCode;

    /**
     * 退款单号
     */
    @NotBlank
    @Length(max = 50)
    private String refundId;

    /**
     * 订单号（个别平台必填）
     */
    @Length(max = 50)
    private String orderNo;

}
