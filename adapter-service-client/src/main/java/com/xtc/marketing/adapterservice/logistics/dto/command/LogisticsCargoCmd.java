package com.xtc.marketing.adapterservice.logistics.dto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsCargoCmd {

    /**
     * 物品名称
     */
    @NotBlank
    @Length(max = 200)
    private String name;

    /**
     * 物品数量
     */
    @NotNull
    @Range(min = 1, max = 10000)
    private Integer quantity;

}
