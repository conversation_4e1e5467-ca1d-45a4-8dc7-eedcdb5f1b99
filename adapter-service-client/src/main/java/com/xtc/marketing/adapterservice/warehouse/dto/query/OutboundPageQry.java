package com.xtc.marketing.adapterservice.warehouse.dto.query;

import com.xtc.marketing.adapterservice.warehouse.enums.WarehouseEnum;
import com.xtc.marketing.marketingcomponentdto.dto.BasePageQuery;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 出库分页查询
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class OutboundPageQry extends BasePageQuery {

    /**
     * 仓库
     */
    @NotNull
    private WarehouseEnum warehouse;
    /**
     * 开始时间
     */
    @NotNull
    private LocalDateTime startTimeStart;
    /**
     * 结束时间
     */
    @NotNull
    private LocalDateTime endTimeEnd;
    /**
     * 店铺代码
     */
    @Length(max = 50)
    private String shopCode;

}
