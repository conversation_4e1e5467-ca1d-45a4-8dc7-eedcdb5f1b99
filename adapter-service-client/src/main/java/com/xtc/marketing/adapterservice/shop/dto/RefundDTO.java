package com.xtc.marketing.adapterservice.shop.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 退款单
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundDTO {

    /**
     * 平台代码
     */
    private String platformCode;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 子订单单号
     */
    private String orderItemNo;

    /**
     * 退款单号
     */
    private String serviceNo;

    /**
     * 退款单类型
     */
    private String serviceType;

    /**
     * 退款单类型描述
     */
    private String serviceTypeDesc;

    /**
     * 退款单状态
     */
    private String serviceState;

    /**
     * 退款单状态描述
     */
    private String serviceStateDesc;

    /**
     * 退款申请金额（单位：分）
     */
    private Integer refundApplyAmount;

    /**
     * 退款金额（单位：分）
     */
    private Integer refundAmount;

    /**
     * 退款状态
     */
    private String refundState;

    /**
     * 退款状态描述
     */
    private String refundStateDesc;

    /**
     * 退款完成时间（平台不提供该数据时，填入更新时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime refundTime;

    /**
     * 申请原因
     */
    private String applyReason;

    /**
     * 商户id
     */
    private String sellerId;

    /**
     * 商户名称
     */
    private String sellerName;

    /**
     * 商户备注
     */
    private String sellerMemo;

    /**
     * 买家id
     */
    private String buyerId;

    /**
     * 买家名称
     */
    private String buyerName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;

    /**
     * 审核人id
     */
    private String auditorId;

    /**
     * 审核人名称
     */
    private String auditorName;

    /**
     * 退货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime returnTime;

    /**
     * 退货快递公司
     */
    private String returnExpressCompany;

    /**
     * 退货快递单号
     */
    private String returnWaybillNo;

    /**
     * 商品id
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品的最小库存单位sku的id
     */
    private String skuId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku物料代码
     */
    private String skuErpCode;

    /**
     * 商品数量
     */
    private Integer num;

    /**
     * 商品价格（单位：分）
     */
    private Integer unitPrice;

    /**
     * 支付金额（单位：分）
     */
    private Integer payment;

    /**
     * 原始平台数据，只有查询详情接口返回，并且只有特定平台返回，用于特殊业务处理
     */
    private String originData;

}
