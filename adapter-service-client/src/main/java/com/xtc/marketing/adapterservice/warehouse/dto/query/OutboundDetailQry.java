package com.xtc.marketing.adapterservice.warehouse.dto.query;

import com.xtc.marketing.adapterservice.warehouse.enums.WarehouseEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * 出库详情查询
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutboundDetailQry {

    /**
     * 仓库
     */
    @NotNull
    private WarehouseEnum warehouse;
    /**
     * 店铺订单号
     */
    @Length(max = 50)
    private String shopOrderNo;
    /**
     * 平台订单id
     */
    @Length(max = 50)
    private String orderId;
    /**
     * 店铺代码
     */
    @Length(max = 50)
    private String shopCode;

}
