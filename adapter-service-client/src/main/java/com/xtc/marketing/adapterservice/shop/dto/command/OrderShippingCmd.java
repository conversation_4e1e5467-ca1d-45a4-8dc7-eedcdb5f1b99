package com.xtc.marketing.adapterservice.shop.dto.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xtc.marketing.adapterservice.shop.enums.LogisticsCompanyEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单发货
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderShippingCmd {

    /**
     * 店铺代码
     */
    @NotBlank
    @Length(max = 50)
    private String shopCode;

    /**
     * 订单号
     */
    @NotBlank
    @Length(max = 50)
    private String orderNo;

    /**
     * 物流公司名称
     */
    @NotNull
    private LogisticsCompanyEnum logisticsCompany;

    /**
     * 物流单号
     */
    @NotBlank
    @Length(max = 50)
    private String waybillNo;

    /**
     * 发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shippingTime;

    /**
     * 发货条码
     */
    @Size(max = 20)
    private List<OrderShippingBarcodeCmd> barcodes;

}
