package com.xtc.marketing.adapterservice.invoice.enums;

import lombok.Getter;

/**
 * 发票文件类型
 */
@Getter
public enum InvoiceFileType {

    /**
     * pdf文件
     */
    PDF("pdf"),
    /**
     * jpg文件
     */
    JPG("jpg"),
    /**
     * png文件
     */
    PNG("png"),
    /**
     * 未知文件类型
     */
    UNKNOWN("unknown");

    private final String type;

    InvoiceFileType(String type) {
        this.type = type;
    }

    /**
     * 查询发票文件类型
     *
     * @param type 类型
     * @return 发票文件类型
     */
    public static InvoiceFileType getInvoiceFileType(String type) {
        for (InvoiceFileType status : InvoiceFileType.values()) {
            if (type.equals(status.getType())) {
                return status;
            }
        }
        return InvoiceFileType.UNKNOWN;
    }

}
