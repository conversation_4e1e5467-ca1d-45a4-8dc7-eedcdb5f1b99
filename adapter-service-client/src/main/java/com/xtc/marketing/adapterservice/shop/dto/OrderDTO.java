package com.xtc.marketing.adapterservice.shop.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDTO {

    /**
     * 平台代码
     */
    private String platformCode;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 店铺编码
     */
    private String shopCode;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 订单类型
     */
    private String orderType;
    /**
     * 订单类型描述
     */
    private String orderTypeDesc;
    /**
     * 订单状态
     */
    private String orderState;
    /**
     * 订单状态描述
     */
    private String orderStateDesc;
    /**
     * 分阶段订单状态
     */
    private String stepOrderState;
    /**
     * 分阶段订单状态描述
     */
    private String stepOrderStateDesc;
    /**
     * 风险状态（平台判定订单有风险，建议业务不做处理）
     */
    private Boolean riskState;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    /**
     * 下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderTime;
    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;
    /**
     * 最晚发货时间（订单将在此时间前发出）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime latestShippingTime;
    /**
     * 发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shippingTime;
    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completedTime;
    /**
     * 订单金额（单位：分）
     */
    private Integer priceTotal;
    /**
     * 支付金额（单位：分）
     * 支付金额 = 订单金额 + 运费 - 折扣金额 - 店铺优惠金额
     */
    private Integer payment;
    /**
     * 运费（单位：分）
     */
    private Integer shippingPayment;
    /**
     * 折扣金额（单位：分）
     */
    private Integer discount;
    /**
     * 商户id
     */
    private String sellerId;
    /**
     * 商户名称
     */
    private String sellerName;
    /**
     * 商户备注
     */
    private String sellerMemo;
    /**
     * 买家id
     */
    private String buyerId;
    /**
     * 买家名称
     */
    private String buyerName;
    /**
     * 买家备注
     */
    private String buyerMemo;
    /**
     * 收货人姓名
     */
    private String receiverName;
    /**
     * 收货人手机号码
     */
    private String receiverMobile;
    /**
     * 收货人省份
     */
    private String receiverProvince;
    /**
     * 收货人城市
     */
    private String receiverCity;
    /**
     * 收货人区县
     */
    private String receiverDistrict;
    /**
     * 收货人详细地址
     */
    private String receiverAddress;
    /**
     * 运单号
     */
    private String waybillNo;
    /**
     * 仓库类型
     */
    private String warehouseType;
    /**
     * 仓库类型描述
     */
    private String warehouseTypeDesc;
    /**
     * 订单明细，有些平台只在订单详情接口返回
     */
    private List<OrderItemDTO> items;
    /**
     * 发票申请
     */
    private InvoiceApplyDTO invoiceApply;
    /**
     * 密文列表
     */
    private List<String> cipherTexts;
    /**
     * 原始平台订单数据，只有订单详情接口返回，并且只有特定平台返回，用于特殊业务处理
     */
    private String originOrderData;

}
