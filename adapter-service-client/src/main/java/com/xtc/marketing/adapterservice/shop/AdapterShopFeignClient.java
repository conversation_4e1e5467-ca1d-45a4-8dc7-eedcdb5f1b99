package com.xtc.marketing.adapterservice.shop;

import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.dto.query.*;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.marketing.marketingcomponentdto.dto.Response;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 对接服务 - 店铺模块
 */
@FeignClient(
        contextId = "adapterShopFeignClient",
        name = "adapter-shop-feign",
        url = "${xtc.feign.client.adapter-service.url}"
)
public interface AdapterShopFeignClient {

    /**
     * 查询店铺 AccessToken
     *
     * @param shopCode 店铺代码
     * @return 店铺 AccessToken
     */
    @GetMapping("/api/shop/accessToken")
    SingleResponse<ShopAccessTokenDTO> getAccessToken(@RequestParam("shopCode") String shopCode);

    /**
     * 分页查询订单列表
     *
     * @param qry 参数
     * @return 订单分页列表
     */
    @GetMapping("/api/shop/orders")
    PageResponse<OrderDTO> pageOrders(@SpringQueryMap OrderPageQry qry);

    /**
     * 查询订单
     *
     * @param qry 参数
     * @return 订单
     */
    @GetMapping("/api/shop/order/detail")
    SingleResponse<OrderDTO> getOrder(@SpringQueryMap OrderGetQry qry);

    /**
     * 订单发货
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/api/shop/order/shipping")
    SingleResponse<Boolean> shipping(@RequestBody OrderShippingCmd cmd);

    /**
     * 订单取消发货
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/api/shop/order/shipping-cancel")
    SingleResponse<Boolean> shippingCancel(@RequestBody OrderShippingCancelCmd cmd);

    /**
     * 订单无需物流发货
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/api/shop/order/dummy-shipping")
    SingleResponse<Boolean> dummyShipping(@RequestBody OrderDummyShippingCmd cmd);

    /**
     * 订单备注
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/api/shop/order/remark")
    SingleResponse<Boolean> remark(@RequestBody OrderRemarkCmd cmd);

    /**
     * 订单解密
     *
     * @param cmd 参数
     * @return 解密数据
     */
    @PostMapping("/api/shop/order/decrypt")
    SingleResponse<OrderDecryptDTO> decrypt(@RequestBody OrderDecryptCmd cmd);

    /**
     * 分页查询发票申请列表
     *
     * @param qry 参数
     * @return 发票申请分页列表
     */
    @GetMapping("/api/shop/order/invoice-apply")
    PageResponse<InvoiceApplyDTO> pageInvoiceApply(@SpringQueryMap InvoiceApplyPageQry qry);

    /**
     * 查询发票申请
     *
     * @param qry 参数
     * @return 发票申请
     */
    @GetMapping("/api/shop/order/invoice-apply/detail")
    SingleResponse<InvoiceApplyDTO> getInvoiceApply(@SpringQueryMap InvoiceApplyGetQry qry);

    /**
     * 上传发票 - 文件流
     *
     * @param cmd         参数
     * @param invoiceFile 发票文件
     * @return 执行结果
     */
    @PostMapping(value = "/api/shop/order/uploadInvoice", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    SingleResponse<Boolean> uploadInvoice(@SpringQueryMap OrderUploadInvoiceCmd cmd,
                                          @RequestPart("invoiceFile") InvoiceFileDTO invoiceFile);

    /**
     * 上传发票 - base64字符串
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping(value = "/api/shop/order/invoice-upload")
    SingleResponse<Boolean> invoiceUpload(@RequestBody InvoiceUploadCmd cmd);

    /**
     * 查询发票开票金额
     *
     * @param qry 参数
     * @return 发票开票金额
     */
    @GetMapping("/api/shop/order/invoice-amount")
    SingleResponse<InvoiceAmountDTO> getInvoiceAmount(@SpringQueryMap InvoiceAmountGetQry qry);

    /**
     * 分页查询评价列表
     *
     * @param qry 参数
     * @return 评价分页列表
     */
    @GetMapping("/api/shop/comments")
    PageResponse<CommentDTO> pageComments(@SpringQueryMap CommentPageQry qry);

    /**
     * 分页查询退款单列表
     *
     * @param qry 参数
     * @return 退款单分页列表
     */
    @GetMapping("/api/shop/refunds")
    PageResponse<RefundDTO> pageRefunds(@SpringQueryMap RefundPageQry qry);

    /**
     * 查询退款单
     *
     * @param qry 参数
     * @return 退款单
     */
    @GetMapping("/api/shop/refund/detail")
    SingleResponse<RefundDTO> getRefund(@SpringQueryMap RefundGetQry qry);

    /**
     * 生成电子面单
     *
     * @param cmd 参数
     * @return 电子面单
     */
    @PostMapping("/api/shop/logistics/create-order")
    SingleResponse<ShopLogisticsOrderDTO> createLogisticsOrder(@RequestBody ShopLogisticsOrderCreateCmd cmd);

    /**
     * 取消电子面单
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/api/shop/logistics/cancel-order")
    SingleResponse<Boolean> cancelLogisticsOrder(@RequestBody ShopLogisticsOrderCancelCmd cmd);

    /**
     * 退货确认入仓
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/api/shop/refund/goods-to-warehouse")
    Response refundGoodsToWarehouse(@RequestBody RefundGoodsToWarehouseCmd cmd);

    /**
     * 上传条码
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/api/shop/barcode/upload")
    Response barcodeUpload(@RequestBody BarcodeUploadCmd cmd);

}
