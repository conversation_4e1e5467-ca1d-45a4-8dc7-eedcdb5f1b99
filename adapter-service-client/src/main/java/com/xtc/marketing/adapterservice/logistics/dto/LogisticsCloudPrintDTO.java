package com.xtc.marketing.adapterservice.logistics.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 面单云打印数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsCloudPrintDTO {

    /**
     * 模板地址
     */
    private String templateUrl;
    /**
     * pdf 文件地址
     */
    private String pdfUrl;
    /**
     * pdf base64 文件内容
     */
    private String pdfBase64;

}
