package com.xtc.marketing.adapterservice.invoice.dto;

import com.xtc.marketing.adapterservice.invoice.enums.InvoiceCreateType;
import com.xtc.marketing.adapterservice.invoice.enums.InvoicePlatformCode;
import com.xtc.marketing.adapterservice.invoice.enums.InvoiceStatus;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceResultDTO {

    /**
     * 电商平台代码
     */
    private InvoicePlatformCode platformCode;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 开票流水号，唯一标志开票请求。如果两次请求流水号相同，则表示重复请求。
     */
    private String serialNo;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 发票号码
     */
    private String invoiceNo;
    /**
     * 开票状态
     */
    private InvoiceStatus invoiceStatus;
    /**
     * 发票类型，blue=蓝票，red=红票
     */
    private InvoiceCreateType createType;
    /**
     * 发票种类，0=电子发票，1=纸质发票，2=纸质专票
     */
    private InvoiceTypeEnum invoiceTitle;
    /**
     * 开票时间
     */
    private LocalDateTime invoiceTime;
    /**
     * 开票金额
     */
    private String invoiceAmount;
    /**
     * 发票PDF的下载地址(仅在单个查询接口上显示，批量查询不显示)
     */
    private String invoiceFilePath;
    /**
     * 购买方抬头，erp开票不返回，用来erp获取自动开票结果
     */
    private String payerName;
    /**
     * 购买方税号，erp开票不返回，用来erp获取自动开票结果
     */
    private String payerTaxNo;
    /**
     * 购买方企业电话，erp开票不返回，用来erp获取自动开票结果
     */
    private String payerMobile;
    /**
     * 购买方企业地址，erp开票不返回，用来erp获取自动开票结果
     */
    private String payerAddress;
    /**
     * 购买方企业银行及账号，erp开票不返回，用来erp获取自动开票结果
     */
    private String payerBankAccount;
    /**
     * 销售方税号
     */
    private String payeeRegisterNo;
    /**
     * 收款人，erp开票不返回，用来erp获取自动开票结果
     */
    private String payeeReceiver;
    /**
     * 复核人，erp开票不返回，用来erp获取自动开票结果
     */
    private String payeeChecker;
    /**
     * 开票人，erp开票不返回，用来erp获取自动开票结果
     */
    private String payeeOperator;
    /**
     * 原蓝票发票代码，invoiceType=red时有值
     */
    private String blueInvoiceCode;
    /**
     * 原蓝票发票号码，invoiceType=red时有值
     */
    private String blueInvoiceNo;
    /**
     * 错误编码
     */
    private String errorCode;
    /**
     * 错误信息
     */
    private String errorMsg;
    /**
     * 电子发票明细，erp开票默认不返回，如果erp需要获取阿里发票平台自动开票的结果，需要先找阿里小二开通权限
     */
    private List<InvoiceResultItemDTO> invoiceItems;

    @Data
    public static class InvoiceResultItemDTO {

        /**
         * 单位
         */
        private String unit;
        /**
         * 总价，格式：100.00(不含税)
         */
        private String sumPrice;
        /**
         * 税额
         */
        private String tax;
        /**
         * 单价，格式：100.00(不含税)
         */
        private String price;
        /**
         * 税收分类编码
         */
        private String itemNo;
        /**
         * 发票项目名称（或商品名称）
         */
        private String itemName;
        /**
         * 规格型号,可选
         */
        private String specification;
        /**
         * 数量
         */
        private String quantity;
        /**
         * 税率。税率只能为0或0.03或0.04或0.06或0.11或0.13或0.17
         */
        private String taxRate;
        /**
         * 发票行性质。0表示正常行，1表示折扣行，2表示被折扣行。比如充电器单价100元，折扣10元，则明细为2行，充电器行性质为2，折扣行性质为1。如果充电器没有折扣，则值应为0
         */
        private String rowType;
        /**
         * 价税合计。(等于sumPrice和tax之和)
         */
        private String amount;
        /**
         * 0税率标识，0=出口零税率，1=免税，2=不征收，3=普通零税率
         */
        private String zeroRateFlag;
        /**
         * 商品的外部系统id，如果有sku则取sku的outerId，否则取item的outerId，，阿里发票平台自动开票时才有
         */
        private String outerId;
        /**
         * 淘宝子订单号，阿里发票平台自动开票时才有
         */
        private String bizOrderId;
        /**
         * 是否为运费行，，阿里发票平台自动开票时才有
         */
        private Boolean isPostFeeRow;

    }

}
