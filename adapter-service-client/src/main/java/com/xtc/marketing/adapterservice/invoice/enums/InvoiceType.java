package com.xtc.marketing.adapterservice.invoice.enums;

import lombok.Getter;

/**
 * 发票类型枚举
 */
@Getter
public enum InvoiceType {

    /**
     * 电子发票
     */
    ELECTRONIC("ELECTRONIC", 0L),
    /**
     * 普通发票
     */
    NORMAL("NORMAL", 1L),
    /**
     * 专用发票
     */
    SPECIAL("NORMAL", 2L);

    private final String type;
    private final Long num;

    InvoiceType(String type, Long num) {
        this.type = type;
        this.num = num;
    }

}
