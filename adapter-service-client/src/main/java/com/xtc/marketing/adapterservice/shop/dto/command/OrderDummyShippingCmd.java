package com.xtc.marketing.adapterservice.shop.dto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 订单无需物流发货
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDummyShippingCmd {

    /**
     * 店铺代码
     */
    @NotBlank
    private String shopCode;

    /**
     * 订单号
     */
    @NotBlank
    @Length(max = 50)
    private String orderNo;

}
