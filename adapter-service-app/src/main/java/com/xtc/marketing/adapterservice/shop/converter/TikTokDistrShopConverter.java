package com.xtc.marketing.adapterservice.shop.converter;

import com.doudian.open.api.iop_orderInfo.data.IopOrderInfoData;
import com.doudian.open.api.iop_orderList.data.OrderListItem;
import com.doudian.open.api.iop_waybillGet.param.IopWaybillGetParam;
import com.doudian.open.api.iop_waybillGet.param.TradeOrderListItem;
import com.xtc.marketing.adapterservice.rpc.tiktok.tiktokdto.enums.TikTokLogisticsCompany;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.OrderDTO;
import com.xtc.marketing.adapterservice.shop.dto.OrderItemDTO;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsCargoCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsOrderCreateCmd;
import com.xtc.marketing.adapterservice.util.DateUtil;
import org.mapstruct.*;

import java.time.LocalDateTime;

@Mapper(
        componentModel = "spring",
        uses = {BaseShopConverter.class},
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface TikTokDistrShopConverter {

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "distrOrderId")
    @Mapping(target = "orderState", source = "distrStatus")
    @Mapping(target = "sellerId", source = "userId")
    @Mapping(target = "sellerName", source = "userName")
    @Mapping(target = "buyerMemo", source = "buyerWords")
    @Mapping(target = "receiverName", source = "userId")
    @Mapping(target = "receiverMobile", source = "receiverId")
    @Mapping(target = "receiverProvince", source = "province")
    @Mapping(target = "receiverCity", source = "city")
    @Mapping(target = "receiverDistrict", source = "district")
    @Mapping(target = "receiverAddress", source = "receiverId")
    // 金额和时间
    @Mapping(target = "priceTotal", expression = "java(source.getProductPrice().intValue() * source.getProductCount().intValue())")
    @Mapping(target = "payment", expression = "java(source.getProductPrice().intValue() * source.getProductCount().intValue())")
    @Mapping(target = "updateTime", source = "distrTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "orderTime", source = "distrTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "payTime", source = "distrTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "latestShippingTime", source = "expShipTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "shippingTime", source = "shipTime", qualifiedByName = "toLocalDateTime")
    OrderDTO toAdapterOrderDTO(OrderListItem source);

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "distrOrderId")
    @Mapping(target = "orderState", source = "distrStatus")
    @Mapping(target = "buyerMemo", source = "buyerWords")
    @Mapping(target = "sellerId", source = "userId")
    @Mapping(target = "sellerName", source = "userName")
    @Mapping(target = "receiverName", source = "userId")
    @Mapping(target = "receiverMobile", source = "receiverId")
    @Mapping(target = "receiverProvince", source = "province")
    @Mapping(target = "receiverCity", source = "city")
    @Mapping(target = "receiverDistrict", source = "district")
    @Mapping(target = "receiverAddress", source = "receiverId")
    // 金额和时间
    @Mapping(target = "priceTotal", expression = "java(source.getProductPrice().intValue() * source.getProductCount().intValue())")
    @Mapping(target = "payment", expression = "java(source.getProductPrice().intValue() * source.getProductCount().intValue())")
    @Mapping(target = "updateTime", source = "distrTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "orderTime", source = "distrTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "payTime", source = "distrTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "latestShippingTime", source = "expShipTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "shippingTime", source = "shipTime", qualifiedByName = "toLocalDateTime")
    OrderDTO toAdapterOrderDTO(IopOrderInfoData source);

    /**
     * 转换统一订单明细
     *
     * @param source 平台订单明细
     * @return 统一订单明细
     */
    @Mapping(target = "orderNo", source = "distrOrderId")
    @Mapping(target = "itemNo", expression = "java(source.getDistrOrderId() + \"-\" + source.getProductId())")
    @Mapping(target = "productId", source = "productId")
    @Mapping(target = "productName", source = "productName")
    @Mapping(target = "skuName", expression = "java(source.getProductName() + \"-\" + source.getSkuSpec())")
    @Mapping(target = "skuErpCode", source = "outSkuId")
    @Mapping(target = "num", source = "productCount")
    @Mapping(target = "unitPrice", source = "productPrice")
    @Mapping(target = "priceTotal", expression = "java(source.getProductPrice().intValue() * source.getProductCount().intValue())")
    @Mapping(target = "payment", expression = "java(source.getProductPrice().intValue() * source.getProductCount().intValue())")
    OrderItemDTO toAdapterOrderListItemDTO(OrderListItem source);

    /**
     * 转换统一订单明细
     *
     * @param source 平台订单明细
     * @return 统一订单明细
     */
    @Mapping(target = "orderNo", source = "distrOrderId")
    @Mapping(target = "itemNo", expression = "java(source.getDistrOrderId() + \"-\" + source.getProductId())")
    @Mapping(target = "productId", source = "productId")
    @Mapping(target = "productName", source = "productName")
    @Mapping(target = "skuName", expression = "java(source.getProductName() + \"-\" + source.getSkuSpec())")
    @Mapping(target = "skuErpCode", source = "outSkuId")
    @Mapping(target = "num", source = "productCount")
    @Mapping(target = "unitPrice", source = "productPrice")
    @Mapping(target = "priceTotal", expression = "java(source.getProductPrice().intValue() * source.getProductCount().intValue())")
    @Mapping(target = "payment", expression = "java(source.getProductPrice().intValue() * source.getProductCount().intValue())")
    OrderItemDTO toAdapterOrderInfoItemDTO(IopOrderInfoData source);

    /**
     * 转换平台电子面单参数
     *
     * @param shopLogisticsOrderCreateCmd 统一电子面单参数
     * @param shop                        店铺
     * @param logisticsCompany            物流公司
     * @return 平台电子面单参数
     */
    @Mapping(target = "sender.contact.name", source = "shopLogisticsOrderCreateCmd.senderName")
    @Mapping(target = "sender.contact.mobile", source = "shopLogisticsOrderCreateCmd.senderPhone")
    @Mapping(target = "sender.address.countryCode", constant = "CHN")
    @Mapping(target = "sender.address.provinceName", source = "shopLogisticsOrderCreateCmd.senderProvince")
    @Mapping(target = "sender.address.cityName", source = "shopLogisticsOrderCreateCmd.senderCity")
    @Mapping(target = "sender.address.districtName", expression = "java(\"东莞市\".equals(shopLogisticsOrderCreateCmd.getSenderCity()) ? \"东莞市\" : shopLogisticsOrderCreateCmd.getSenderDistrict())")
    @Mapping(target = "sender.address.streetName", expression = "java(\"东莞市\".equals(shopLogisticsOrderCreateCmd.getSenderCity()) ? shopLogisticsOrderCreateCmd.getSenderDistrict() : shopLogisticsOrderCreateCmd.getSenderTown())")
    @Mapping(target = "sender.address.detailAddress", source = "shopLogisticsOrderCreateCmd.senderAddress")
    @Mapping(target = "tradeOrderList", expression = "java(java.util.Collections.singletonList(toTradeOrderListItem(shopLogisticsOrderCreateCmd, shop,logisticsCompany)))")
    IopWaybillGetParam toIopWaybillGetParam(
            ShopLogisticsOrderCreateCmd shopLogisticsOrderCreateCmd,
            ShopDO shop,
            TikTokLogisticsCompany logisticsCompany
    );

    /**
     * 转换平台电子面单参数
     *
     * @param shopLogisticsOrderCreateCmd 统一电子面单参数
     * @param shop                        店铺
     * @param logisticsCompany            物流公司
     * @return 平台电子面单参数
     */
    @Mapping(target = "userId", expression = "java(java.lang.Long.valueOf(shop.getAgentCode().split(\"\\\\|\")[0]))")
    @Mapping(target = "companyCode", source = "logisticsCompany.code")
    @Mapping(target = "orderInfos", expression = "java(java.util.Collections.singletonList(toOrderInfosItem(shopLogisticsOrderCreateCmd, shop)))")
    TradeOrderListItem toTradeOrderListItem(ShopLogisticsOrderCreateCmd shopLogisticsOrderCreateCmd, ShopDO shop,
                                            TikTokLogisticsCompany logisticsCompany);

    /**
     * 转换平台电子面单参数
     *
     * @param shopLogisticsOrderCreateCmd 统一电子面单参数
     * @param shop                        店铺
     * @return 平台电子面单参数
     */
    @Mapping(target = "distrOrderId", source = "shopLogisticsOrderCreateCmd.shopOrderNo")
    @Mapping(target = "productType", source = "shopLogisticsOrderCreateCmd.logisticsType")
    @Mapping(target = "items", source = "shopLogisticsOrderCreateCmd.cargos")
    com.doudian.open.api.iop_waybillGet.param.OrderInfosItem toOrderInfosItem(ShopLogisticsOrderCreateCmd shopLogisticsOrderCreateCmd,
                                                                              ShopDO shop);

    /**
     * 转换平台电子面单参数
     *
     * @param cmd 统一电子面单参数
     * @return 平台电子面单参数
     */
    @Mapping(target = "itemName", source = "name")
    @Mapping(target = "itemCount", source = "quantity")
    com.doudian.open.api.iop_waybillGet.param.ItemsItem toItemsItem(ShopLogisticsCargoCmd cmd);

    /**
     * 时间转换
     *
     * @param epochSecond 秒
     * @return LocalDateTime
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(Long epochSecond) {
        if (epochSecond == null || epochSecond < 1) {
            return null;
        }
        return DateUtil.toLocalDateTime(epochSecond);
    }

}
