package com.xtc.marketing.adapterservice.shop.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.taobao.api.FileItem;
import com.taobao.api.domain.Order;
import com.taobao.api.domain.Refund;
import com.taobao.api.domain.Trade;
import com.taobao.api.request.*;
import com.taobao.api.response.*;
import com.xtc.marketing.adapterservice.exception.BizErrorCode;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.tmall.TmallRpc;
import com.xtc.marketing.adapterservice.rpc.tmall.tmalldto.enums.TmallLogisticsCompany;
import com.xtc.marketing.adapterservice.shop.converter.TmallShopConverter;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.dto.query.*;
import com.xtc.marketing.adapterservice.shop.util.SkuUtil;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = ShopExtConstant.USE_CASE, scenario = ShopExtConstant.SCENARIO_TMALL)
public class TmallShopExt implements ShopExtPt {

    private final TmallRpc tmallRpc;
    private final TmallShopConverter tmallShopConverter;

    @Override
    public ShopDO getShopToken(ShopDO shop) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<OrderDTO> pageOrders(ShopDO shop, OrderPageQry qry) {
        TradesSoldIncrementGetRequest request = new TradesSoldIncrementGetRequest();
        request.setFields(StringUtils.defaultIfBlank(qry.getFields(), TmallRpc.FIELDS_ORDER_LIST));
        request.setStartModified(DateUtil.toDate(qry.getUpdateTimeStart()));
        request.setEndModified(DateUtil.toDate(qry.getUpdateTimeEnd()));
        request.setPageNo((long) qry.getPageIndex());
        request.setPageSize((long) qry.getPageSize());
        request.setUseHasNext(BooleanUtils.isTrue(qry.getUseHasNext()));

        TradesSoldIncrementGetResponse response = tmallRpc.pageOrders(shop, request);
        if (CollectionUtils.isEmpty(response.getTrades())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }

        // 过滤掉未付款的订单，数据转换，组装返回值
        List<OrderDTO> listOrderDTO = response.getTrades().stream()
                .filter(trade -> !"WAIT_BUYER_PAY".equals(trade.getStatus()))
                .map(this::splitSkuIdsAndConvertToAdapterOrderDTO)
                .collect(Collectors.toList());
        PageResponse<OrderDTO> pageResponse = PageResponse.of(listOrderDTO, -1, qry.getPageSize(), qry.getPageIndex());
        Optional.ofNullable(response.getTotalResults()).map(Long::intValue).ifPresent(pageResponse::setTotalCount);
        Optional.ofNullable(response.getHasNext()).ifPresent(pageResponse::setHasNext);
        return pageResponse;
    }

    @Override
    public OrderDTO getOrder(ShopDO shop, OrderGetQry qry) {
        Trade trade = tmallRpc.getOrder(shop, qry.getOrderNo());
        if (trade == null) {
            return null;
        }
        // 在处理订单数据前，保存原始数据
        String originOrderData = GsonUtil.objectToJson(trade);
        // 转换统一的数据结构，订单数据会被修改
        OrderDTO orderDTO = this.splitSkuIdsAndConvertToAdapterOrderDTO(trade);
        orderDTO.setOriginOrderData(originOrderData);
        return orderDTO;
    }

    @Override
    public boolean orderRemark(ShopDO shop, OrderRemarkCmd cmd) {
        TradeMemoUpdateRequest request = new TradeMemoUpdateRequest();
        request.setTid(Long.parseLong(cmd.getOrderNo()));
        request.setMemo(cmd.getRemark());
        request.setFlag(4L);
        return tmallRpc.orderRemark(shop, request);
    }

    @Override
    public boolean orderShipping(ShopDO shop, OrderShippingCmd cmd) {
        Trade trade = tmallRpc.getOrder(shop, cmd.getOrderNo());
        boolean hasShipping = this.checkOrderHasShipping(trade);
        if (hasShipping) {
            return true;
        }
        AlibabaAscpLogisticsOfflineSendRequest request = new AlibabaAscpLogisticsOfflineSendRequest();
        request.setTid(cmd.getOrderNo());
        // 设置快递数据
        AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest consignPkg =
                new AlibabaAscpLogisticsOfflineSendRequest.TopConsignPkgRequest();
        consignPkg.setOutSid(cmd.getWaybillNo());
        consignPkg.setCompanyCode(cmd.getLogisticsCompany().name());
        request.setConsignPkgs(Collections.singletonList(consignPkg));
        // 设置国补字段数据
        if (StringUtils.isNotBlank(trade.getGovSnCheck()) && CollectionUtils.isNotEmpty(cmd.getBarcodes())) {
            // 入参SN码和IMEI码格式：sn="subTradeId1:SN1:IMEI1,IMEI2|subTradeId1:SN2:IMEI3|subTradeId2:SN3"
            String feature = cmd.getBarcodes().stream()
                    .map(barcode -> {
                        // 获取平台子订单号，由于子订单号会经过订单处理加工，所以额外使用 “-” 分割
                        String subTradeId = barcode.getItemNo().split("-")[0];
                        return Stream.of(subTradeId, barcode.getBarcode(), barcode.getImei())
                                .filter(StringUtils::isNotBlank).collect(Collectors.joining(":"));
                    })
                    .collect(Collectors.joining("|", "sn=", ""));
            request.setFeature(feature);
            log.info("设置国补字段数据 feature=\"{}\"", request.getFeature());
        }
        return tmallRpc.orderShipping(shop, request);
    }

    @Override
    public boolean orderShippingCancel(ShopDO shop, OrderShippingCancelCmd cmd) {
        return tmallRpc.orderShippingCancel(shop, cmd.getOrderNo(), cmd.getRefundId());
    }

    @Override
    public boolean orderDummyShipping(ShopDO shop, OrderDummyShippingCmd cmd) {
        Trade trade = tmallRpc.getOrder(shop, cmd.getOrderNo());
        boolean hasShipping = this.checkOrderHasShipping(trade);
        if (hasShipping) {
            return true;
        }
        return tmallRpc.orderDummyShipping(shop, cmd.getOrderNo());
    }

    @Override
    public OrderDecryptDTO orderDecrypt(ShopDO shop, OrderDecryptCmd cmd) {
        TopOaidDecryptRequest.ReceiverQuery receiverQuery = new TopOaidDecryptRequest.ReceiverQuery();
        receiverQuery.setScene("1005");
        receiverQuery.setTid(cmd.getOrderNo());
        receiverQuery.setOaid(cmd.getCiphers().get(0));

        TopOaidDecryptRequest request = new TopOaidDecryptRequest();
        request.setQueryList(Collections.singletonList(receiverQuery));
        List<TopOaidDecryptResponse.Receiver> receivers = tmallRpc.orderDecrypt(shop, request);
        if (CollectionUtils.isEmpty(receivers)) {
            return null;
        }
        return tmallShopConverter.toAdapterOrderDecryptDTO(receivers.get(0));
    }

    @Override
    public PageResponse<InvoiceApplyDTO> pageInvoiceApply(ShopDO shop, InvoiceApplyPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public InvoiceApplyDTO getInvoiceApply(ShopDO shop, InvoiceApplyGetQry qry) {
        AlibabaEinvoiceApplyGetRequest request = new AlibabaEinvoiceApplyGetRequest();
        request.setPlatformTid(qry.getOrderNo());
        List<AlibabaEinvoiceApplyGetResponse.Apply> invoiceApplyList = tmallRpc.getInvoiceApply(shop, request);
        if (CollectionUtils.isEmpty(invoiceApplyList)) {
            return null;
        }
        return tmallShopConverter.toAdapterInvoiceApplyDTO(invoiceApplyList.get(0));
    }

    @Override
    public boolean uploadInvoiceFile(ShopDO shop, OrderUploadInvoiceCmd cmd, MultipartFile invoiceFile) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceBase64(ShopDO shop, InvoiceUploadCmd cmd) {
        AlibabaEinvoiceDetailUploadRequest req = tmallShopConverter.toAlibabaEinvoiceDetailUploadRequest(cmd);
        // 设置文件数据
        byte[] baseCode = Base64.getDecoder().decode(cmd.getInvoiceFileBase());
        FileItem fileItem = new FileItem(cmd.getBlueInvoiceNo(), baseCode, MediaType.APPLICATION_PDF_VALUE);
        req.setInvoiceFileData(fileItem);
        // 设置发票项目
        List<AlibabaEinvoiceDetailUploadRequest.InvoiceItem> invoiceItems = tmallShopConverter.toInvoiceItem(cmd.getInvoiceItems());
        req.setInvoiceItems(invoiceItems);
        return tmallRpc.uploadInvoiceFile(shop, req);
    }

    @Override
    public InvoiceAmountDTO getInvoiceAmount(ShopDO shop, String orderNo) {
        TradeInvoiceAmountGetResponse invoiceAmount = tmallRpc.getInvoiceAmount(shop, orderNo);

        /*
        条件1：查询开票金额接口有数据，不为 null
        条件2：consumer_invoice_amount 和 platform_invoice_amount 都不为空
        条件3：consumer_invoice_amount 和 platform_invoice_amount 存在非 0 金额
        */
        Integer shouldInvoiceAmount = Optional.ofNullable(invoiceAmount)
                .filter(amount -> StringUtils.isNoneBlank(amount.getConsumerInvoiceAmount(), amount.getPlatformInvoiceAmount()))
                .filter(amount -> !amount.getConsumerInvoiceAmount().equals("0") || !amount.getPlatformInvoiceAmount().equals("0"))
                .map(TradeInvoiceAmountGetResponse::getConsumerInvoiceAmount)
                .map(BigDecimal::new)
                .map(BigDecimal::intValue)
                .orElse(null);

        return InvoiceAmountDTO.builder()
                .shouldInvoiceAmount(shouldInvoiceAmount)
                .originData(invoiceAmount != null ? invoiceAmount.getBody() : null)
                .build();
    }

    @Override
    public PageResponse<CommentDTO> pageComments(ShopDO shop, CommentPageQry qry) {
        TraderatesGetRequest request = new TraderatesGetRequest();
        request.setFields(TmallRpc.FIELDS_COMMENT);
        request.setRateType("get");
        request.setRole("buyer");
        request.setPageNo((long) qry.getPageIndex());
        request.setPageSize((long) qry.getPageSize());
        request.setUseHasNext(BooleanUtils.isTrue(qry.getUseHasNext()));
        request.setStartDate(DateUtil.toDate(qry.getCreateTimeStart()));
        request.setEndDate(DateUtil.toDate(qry.getCreateTimeEnd()));

        TraderatesGetResponse response = tmallRpc.pageComments(shop, request);
        if (CollectionUtils.isEmpty(response.getTradeRates())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }

        // 数据转换，组装返回值
        List<CommentDTO> listCommentDTO = tmallShopConverter.toAdapterCommentDTO(response.getTradeRates());
        PageResponse<CommentDTO> pageResponse = PageResponse.of(listCommentDTO, -1, qry.getPageSize(), qry.getPageIndex());
        Optional.ofNullable(response.getTotalResults()).map(Long::intValue).ifPresent(pageResponse::setTotalCount);
        Optional.ofNullable(response.getHasNext()).ifPresent(pageResponse::setHasNext);
        return pageResponse;
    }

    @Override
    public PageResponse<RefundDTO> pageRefunds(ShopDO shop, RefundPageQry qry) {
        if (ObjectUtils.allNull(qry.getUpdateTimeStart(), qry.getUpdateTimeEnd())) {
            throw BizException.of("更新时间范围必填");
        }

        RefundsReceiveGetRequest request = new RefundsReceiveGetRequest();
        request.setPageNo((long) qry.getPageIndex());
        request.setPageSize((long) qry.getPageSize());
        request.setFields(StringUtils.defaultIfBlank(qry.getFields(), TmallRpc.FIELDS_REFUND));
        request.setStartModified(DateUtil.toDate(qry.getUpdateTimeStart()));
        request.setEndModified(DateUtil.toDate(qry.getUpdateTimeEnd()));
        request.setUseHasNext(BooleanUtils.isTrue(qry.getUseHasNext()));

        RefundsReceiveGetResponse response = tmallRpc.pageRefunds(shop, request);
        if (CollectionUtils.isEmpty(response.getRefunds())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }

        // 数据转换，组装返回值
        List<RefundDTO> listRefundDTO = tmallShopConverter.toAdapterRefundDTO(response.getRefunds());
        PageResponse<RefundDTO> pageResponse = PageResponse.of(listRefundDTO, -1, qry.getPageSize(), qry.getPageIndex());
        Optional.ofNullable(response.getTotalResults()).map(Long::intValue).ifPresent(pageResponse::setTotalCount);
        Optional.ofNullable(response.getHasNext()).ifPresent(pageResponse::setHasNext);
        return pageResponse;
    }

    @Override
    public RefundDTO getRefund(ShopDO shop, RefundGetQry qry) {
        RefundGetRequest request = new RefundGetRequest();
        request.setFields(TmallRpc.FIELDS_REFUND);
        request.setRefundId(Long.parseLong(qry.getRefundId()));
        Refund refund = tmallRpc.getRefund(shop, request);

        // 数据转换，设置原始数据
        RefundDTO refundDTO = tmallShopConverter.toAdapterRefundDTO(refund);
        refundDTO.setOriginData(GsonUtil.objectToJson(refund));
        return refundDTO;
    }

    @Override
    public ShopLogisticsOrderDTO createLogisticsOrder(ShopDO shop, ShopLogisticsOrderCreateCmd cmd) {
        TmallLogisticsCompany logisticsCompany = TmallLogisticsCompany.valueOf(cmd.getLogisticsCompany().name());
        CainiaoWaybillIiGetRequest.TradeOrderInfoDto orderInfo =
                tmallShopConverter.toTmallWaybillCloudPrintApplyNewRequest(cmd, shop, logisticsCompany);
        CainiaoWaybillIiGetRequest.WaybillCloudPrintApplyNewRequest request =
                tmallShopConverter.toTmallWaybillCloudPrintApplyNewRequest(cmd, logisticsCompany, orderInfo);
        CainiaoWaybillIiGetRequest requestParam = new CainiaoWaybillIiGetRequest();
        requestParam.setParamWaybillCloudPrintApplyNewRequest(request);
        try {
            CainiaoWaybillIiGetResponse.WaybillCloudPrintResponse response = tmallRpc.createLogisticsOrder(shop, requestParam);
            return ShopLogisticsOrderDTO.builder()
                    .wayBillNo(response.getWaybillCode())
                    .orderDetail(response.getPrintData())
                    .build();
        } catch (Exception e) {
            // 根据异常信息定义业务异常，调用方根据 errCode 做特定的业务处理
            if (e.getMessage().contains("停发") || e.getMessage().contains("服务不可达")) {
                throw BizException.of(BizErrorCode.B_ORDER_LogisticsOrderPackageNotReachable.getErrCode(), e.getMessage(), e);
            }
            throw e;
        }
    }

    @Override
    public boolean cancelLogisticsOrder(ShopDO shop, ShopLogisticsOrderCancelCmd cmd) {
        TmallLogisticsCompany logisticsCompany = TmallLogisticsCompany.valueOf(cmd.getLogisticsCompany().name());
        CainiaoWaybillIiCancelRequest req = new CainiaoWaybillIiCancelRequest();
        req.setCpCode(logisticsCompany.getCode());
        req.setWaybillCode(cmd.getWaybillNo());
        return tmallRpc.cancelLogisticsOrder(shop, req);
    }

    @Override
    public void refundGoodsToWarehouse(ShopDO shop, RefundGoodsToWarehouseCmd cmd) {
        tmallRpc.refundGoodsToWarehouse(shop, cmd.getRefundId());
    }

    @Override
    public void barcodeUpload(ShopDO shop, BarcodeUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    /**
     * 根据订单明细里的 skuIds 并且转换成统一订单数据
     *
     * @param platformOrder 平台订单数据
     * @return 统一订单数据
     */
    private OrderDTO splitSkuIdsAndConvertToAdapterOrderDTO(Trade platformOrder) {
        // 补差订单直接转换，不拆分 skuId
        if (platformOrder.getReceiptType() != null) {
            return tmallShopConverter.toAdapterOrderDTO(platformOrder);
        }
        // 物料代码可能存在多个字段 outerIid outerSkuId，并且 outerSkuId 优先级最高
        Function<Order, String> getSkuFunc = order -> order.getOuterSkuId() != null ? order.getOuterSkuId() : order.getOuterIid();
        BiConsumer<Order, String> setSkuFunc = (order, skuId) -> {
            if (order.getOuterSkuId() != null) {
                order.setOuterSkuId(skuId);
            } else {
                order.setOuterIid(skuId);
            }
        };
        // 拆分平台维护的 skuId
        List<Order> splitSkuItems = SkuUtil.splitSkuIdsAndCloneItem(platformOrder.getOrders(), getSkuFunc, setSkuFunc);
        platformOrder.setOrders(splitSkuItems);
        // 转换统一的数据结构
        return tmallShopConverter.toAdapterOrderDTO(platformOrder);
    }

    /**
     * 检查订单已发货
     *
     * @param trade 订单
     * @return 执行结果
     */
    private boolean checkOrderHasShipping(Trade trade) {
        // 判断订单状态已发货，无需重复发货
        if ("WAIT_BUYER_CONFIRM_GOODS,TRADE_BUYER_SIGNED,TRADE_FINISHED".contains(trade.getStatus())) {
            log.warn("订单已发货，无需重复发货 {} {}", trade.getTid(), trade.getStatus());
            return true;
        }
        // 判断订单状态不是待发货，抛异常
        if (!"WAIT_SELLER_SEND_GOODS,PAID_FORBID_CONSIGN".contains(trade.getStatus())) {
            StringBuilder msgDetail = new StringBuilder(BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrDesc());
            // 预定订单未付尾款设置特殊提醒内容
            if (StringUtils.isNotBlank(trade.getStepTradeStatus()) && !"FRONT_PAID_FINAL_PAID".equals(trade.getStepTradeStatus())) {
                msgDetail.append("，并且该预定订单未付尾款");
            }
            // 例：平台的订单状态不符合推送发货状态的条件，并且该预定订单未付尾款 35437927338780253222 orderState: WAIT_BUYER_PAY stepOrderState: FRONT_PAID_FINAL_NOPAID
            String msg = String.format("%s %s orderState: %s stepOrderState: %s", msgDetail,
                    trade.getTid(), trade.getStatus(), trade.getStepTradeStatus());
            throw new BizException(BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrCode(), msg);
        }
        return false;
    }

}
