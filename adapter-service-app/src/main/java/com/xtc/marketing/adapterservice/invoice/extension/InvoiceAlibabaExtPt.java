package com.xtc.marketing.adapterservice.invoice.extension;

import com.alibaba.cola.extension.Extension;
import com.taobao.api.request.AlibabaEinvoiceCreateResultGetRequest;
import com.taobao.api.request.AlibabaEinvoiceCreatereqRequest;
import com.taobao.api.request.AlibabaEinvoiceRedCreatereqRequest;
import com.taobao.api.response.AlibabaEinvoiceCreateResultGetResponse;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.invoice.converter.InvoiceConverter;
import com.xtc.marketing.adapterservice.invoice.dto.InvoiceResultDTO;
import com.xtc.marketing.adapterservice.invoice.dto.command.InvoiceCreateCmd;
import com.xtc.marketing.adapterservice.invoice.dto.command.InvoiceCreateRedCmd;
import com.xtc.marketing.adapterservice.invoice.dto.query.InvoiceResultQry;
import com.xtc.marketing.adapterservice.invoice.enums.InvoiceCreateType;
import com.xtc.marketing.adapterservice.rpc.alibaba.AlibabaInvoiceRpc;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.executor.query.ShopGetQryExe;
import com.xtc.marketing.adapterservice.util.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = InvoiceExtConstant.USE_CASE, scenario = InvoiceExtConstant.SCENARIO_ALIBABA)
public class InvoiceAlibabaExtPt implements InvoiceExtPt {

    private final ShopGetQryExe shopGetQryExe;
    private final AlibabaInvoiceRpc alibabaInvoiceRpc;
    private final InvoiceConverter invoiceConverter;

    @Override
    public boolean createInvoice(InvoiceCreateCmd cmd) {
        ShopDO shop = shopGetQryExe.execute(cmd.getShopCode());
        if (cmd.getCreateType() == InvoiceCreateType.RED && StringUtils.isAnyBlank(cmd.getBlueInvoiceCode(), cmd.getBlueInvoiceNo())) {
            throw new BizException("发票冲红失败，原发票信息缺失");
        }
        AlibabaEinvoiceCreatereqRequest request = this.buildInvoiceCreateRequest(cmd);
        return alibabaInvoiceRpc.createBlueInvoice(shop, request);
    }

    @Override
    public boolean createRedInvoice(InvoiceCreateRedCmd cmd) {
        ShopDO shop = shopGetQryExe.execute(cmd.getShopCode());
        AlibabaEinvoiceRedCreatereqRequest request = new AlibabaEinvoiceRedCreatereqRequest();
        request.setInvoiceNo(cmd.getBlueInvoiceNo());
        request.setInvoiceCode(cmd.getBlueInvoiceCode());
        request.setPayeeRegisterNo(cmd.getTaxNo());
        request.setRedSerialNo(cmd.getRedSerialNo());
        request.setBlueSerialNo(cmd.getBlueSerialNo());
        String redInvoice = alibabaInvoiceRpc.createRedInvoice(shop, request);
        return Boolean.parseBoolean(redInvoice);
    }

    @Override
    public List<InvoiceResultDTO> listInvoiceResult(InvoiceResultQry qry) {
        if (StringUtils.isBlank(qry.getSerialNo()) && StringUtils.isAnyBlank(qry.getPlatformCode(), qry.getOrderNo())) {
            throw new BizException("serialNo和platformCode, platformTid至少传入其中一组");
        }
        ShopDO shop = shopGetQryExe.execute(qry.getShopCode());
        AlibabaEinvoiceCreateResultGetRequest request = new AlibabaEinvoiceCreateResultGetRequest();
        request.setPayeeRegisterNo(qry.getPayeeRegisterNo());
        request.setSerialNo(qry.getSerialNo());
        request.setPlatformTid(qry.getOrderNo());
        request.setPlatformCode(qry.getPlatformCode());
        List<AlibabaEinvoiceCreateResultGetResponse.InvoiceResult> invoiceResults = alibabaInvoiceRpc.listInvoiceResult(shop, request);
        return invoiceConverter.toInvoiceResultDetailsDTO(invoiceResults);
    }

    @Override
    public String createSerialNo(String shopCode) {
        ShopDO shop = shopGetQryExe.execute(shopCode);
        return alibabaInvoiceRpc.createSerialNo(shop);
    }

    /**
     * 构建开票请求参数
     *
     * @param cmd 参数
     * @return 开票请求参数
     */
    private AlibabaEinvoiceCreatereqRequest buildInvoiceCreateRequest(InvoiceCreateCmd cmd) {
        List<AlibabaEinvoiceCreatereqRequest.InvoiceItem> invoiceItems = cmd.getInvoiceItems().stream()
                .map(item -> {
                    AlibabaEinvoiceCreatereqRequest.InvoiceItem invoiceItem = new AlibabaEinvoiceCreatereqRequest.InvoiceItem();
                    invoiceItem.setItemName(item.getItemName());
                    invoiceItem.setItemNo(item.getItemNo());
                    invoiceItem.setAmount(item.getAmount());
                    invoiceItem.setPrice(item.getPrice());
                    invoiceItem.setTaxRate(item.getTaxRate());
                    invoiceItem.setImei(item.getImei());
                    invoiceItem.setQuantity(item.getQuantity());
                    invoiceItem.setUnit(item.getUnit());
                    invoiceItem.setRowType(item.getRowType());
                    invoiceItem.setSpecification(item.getSpecification());
                    invoiceItem.setSumPrice(item.getSumPrice());
                    invoiceItem.setTax(item.getTax());
                    return invoiceItem;
                })
                .collect(Collectors.toList());

        AlibabaEinvoiceCreatereqRequest request = new AlibabaEinvoiceCreatereqRequest();
        request.setBusinessType(0L);
        request.setInvoiceKind(0L);
        request.setInvoiceTime(DateUtil.toDate(cmd.getInvoiceTime()));
        request.setInvoiceAmount(cmd.getInvoiceAmount());
        request.setInvoiceItems(invoiceItems);
        request.setInvoiceType(cmd.getCreateType().getType());
        request.setNormalInvoiceCode(cmd.getBlueInvoiceCode());
        request.setNormalInvoiceNo(cmd.getBlueInvoiceNo());
        request.setPayeeName(cmd.getPayeeName());
        request.setPayeeRegisterNo(cmd.getPayeeRegisterNo());
        request.setPayeePhone(cmd.getPayeePhone());
        request.setPayeeAddress(cmd.getPayeeAddress());
        request.setPayeeBankaccount(StringUtils.join(cmd.getPayeeBankName(), cmd.getPayeeBankAccount()));
        request.setPayeeReceiver(cmd.getPayeeReceiver());
        request.setPayeeChecker(cmd.getPayeeChecker());
        request.setPayeeOperator(cmd.getPayeeOperator());
        request.setPayerName(cmd.getInvoiceTitle());
        request.setPayerRegisterNo(cmd.getPayerRegisterNo());
        request.setPayerPhone(cmd.getPayerMobile());
        request.setPayerAddress(cmd.getPayerAddress());
        request.setPayerBankaccount(StringUtils.join(cmd.getPayerBankName(), cmd.getPayerBankAccount()));
        request.setPlatformCode(cmd.getPlatformCode().getCode());
        request.setPlatformTid(cmd.getOrderNo());
        request.setSerialNo(cmd.getSerialNo());
        request.setSumPrice(cmd.getPriceTotal());
        request.setSumTax(cmd.getTaxTotal());
        request.setInvoiceMemo(cmd.getInvoiceMemo());
        return request;
    }

}
