package com.xtc.marketing.adapterservice.subscribe.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.rpc.jd.JcqClient;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.subscribe.ability.client.JdJcqClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = SubscribeExtConstant.USE_CASE, scenario = SubscribeExtConstant.SCENARIO_JD)
public class JdSubscribeExtPt implements SubscribeExtPt {

    private final JdJcqClient jdJcqClient;

    @Override
    public String connect(ShopDO shop, String bizParam) {
        JcqClient jcqClient = jdJcqClient.getJcqClient(shop, bizParam);
        if (jcqClient == null) {
            throw BizException.of("jcq-client 创建失败");
        }

        // 未连接状态才能发起连接
        if (BooleanUtils.isNotTrue(jcqClient.isRunning())) {
            try {
                jcqClient.startPull();
            } catch (Exception e) {
                throw BizException.of("jcq-client 连接失败 message: " + e.getMessage(), e);
            }
        }
        return String.format("jcq-client 在线状态 isOnline: %s", jcqClient.isRunning());
    }

    @Override
    public String close(ShopDO shop) {
        JcqClient jcqClient = jdJcqClient.removeJcqClient(shop);
        boolean isOnline = Optional.ofNullable(jcqClient).map(JcqClient::isRunning).orElse(false);
        if (isOnline) {
            try {
                jcqClient.stopPull();
                isOnline = jcqClient.isRunning();
            } catch (Exception e) {
                throw BizException.of("jcq-client 关闭失败 message: " + e.getMessage(), e);
            }
        }
        return String.format("jcq-client 在线状态 isOnline: %s", isOnline);
    }

}
