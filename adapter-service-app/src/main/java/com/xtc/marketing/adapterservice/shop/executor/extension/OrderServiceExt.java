package com.xtc.marketing.adapterservice.shop.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.google.common.base.Joiner;
import com.xtc.marketing.adapterservice.exception.BizErrorCode;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.orderservice.XtcMallRpc;
import com.xtc.marketing.adapterservice.rpc.orderservice.XtcShopRpc;
import com.xtc.marketing.adapterservice.rpc.orderservice.enums.OrderServiceLogisticsCompany;
import com.xtc.marketing.adapterservice.rpc.orderservice.xtcmalldto.XtcMallDecryptDTO;
import com.xtc.marketing.adapterservice.rpc.orderservice.xtcmalldto.query.XtcMallEncryptQry;
import com.xtc.marketing.adapterservice.rpc.orderservice.xtcshopdto.XtcShopDecryptDTO;
import com.xtc.marketing.adapterservice.rpc.orderservice.xtcshopdto.query.XtcShopEncryptQry;
import com.xtc.marketing.adapterservice.shop.converter.OrderServiceShopConverter;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.dto.query.*;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTitleTypeEnum;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTypeEnum;
import com.xtc.marketing.adapterservice.shop.util.SkuUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.marketing.marketingcomponentdto.dto.Response;
import com.xtc.marketing.orderservice.order.OrderFeignClient;
import com.xtc.marketing.orderservice.order.dto.InvoiceDTO;
import com.xtc.marketing.orderservice.order.dto.command.OrderSellerMemoCmd;
import com.xtc.marketing.orderservice.order.enums.InvoiceTitleType;
import com.xtc.marketing.orderservice.order.enums.OrderState;
import com.xtc.marketing.orderservice.serviceorder.ServiceOrderFeignClient;
import com.xtc.marketing.orderservice.serviceorder.dto.ServiceOrderDTO;
import com.xtc.marketing.orderservice.serviceorder.dto.query.ServiceOrderPageQry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = ShopExtConstant.USE_CASE, scenario = ShopExtConstant.SCENARIO_XTC)
public class OrderServiceExt implements ShopExtPt {

    private final OrderServiceShopConverter orderServiceShopConverter;
    private final OrderFeignClient orderFeignClient;
    private final ServiceOrderFeignClient serviceOrderFeignClient;
    private final XtcShopRpc xtcShopRpc;
    private final XtcMallRpc xtcMallRpc;

    @Override
    public ShopDO getShopToken(ShopDO shop) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<OrderDTO> pageOrders(ShopDO shop, OrderPageQry qry) {
        OrderState orderState = qry.getOrderState() != null ? OrderState.valueOf(qry.getOrderState()) : null;
        com.xtc.marketing.orderservice.order.dto.query.OrderPageQry orderPageQry =
                com.xtc.marketing.orderservice.order.dto.query.OrderPageQry.builder()
                        .bizCode(shop.getAppKey())
                        .orderState(orderState)
                        .updateTimeStart(qry.getUpdateTimeStart())
                        .updateTimeEnd(qry.getUpdateTimeEnd())
                        .pageIndex(qry.getPageIndex())
                        .pageSize(qry.getPageSize())
                        .build();
        PageResponse<com.xtc.marketing.orderservice.order.dto.OrderDTO> response = orderFeignClient.pageOrders(orderPageQry);
        if (CollectionUtils.isEmpty(response.getData())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }

        List<OrderDTO> listOrderDTO = response.getData().stream()
                .map(this::splitSkuIdsAndConvertToAdapterOrderDTO)
                .collect(Collectors.toList());
        return PageResponse.of(listOrderDTO, response.getTotalCount(), response.getPageSize(), response.getPageIndex());
    }

    @Override
    public OrderDTO getOrder(ShopDO shop, OrderGetQry qry) {
        com.xtc.marketing.orderservice.order.dto.OrderDTO order = this.getOrder(shop, qry.getOrderNo());
        OrderDTO orderDTO = this.splitSkuIdsAndConvertToAdapterOrderDTO(order);
        orderDTO.setOriginOrderData(GsonUtil.objectToJson(order));
        return orderDTO;
    }

    @Override
    public boolean orderShipping(ShopDO shop, OrderShippingCmd cmd) {
        com.xtc.marketing.orderservice.order.dto.OrderDTO order = this.getOrder(shop, cmd.getOrderNo());
        boolean hasShipping = this.checkOrderHasShipping(shop, order.getOrderNo());
        if (hasShipping) {
            return true;
        }

        // 调用发货接口
        com.xtc.marketing.orderservice.order.dto.command.OrderShippingCmd shippingCmd =
                com.xtc.marketing.orderservice.order.dto.command.OrderShippingCmd.builder()
                        .waybillNo(cmd.getWaybillNo())
                        .expressCompany(OrderServiceLogisticsCompany.valueOf(cmd.getLogisticsCompany().name()).getName())
                        .shippingTime(cmd.getShippingTime())
                        .build();

        // 构建发货条码
        if (CollectionUtils.isNotEmpty(cmd.getBarcodes())) {
            // list 转 map（key: itemNo, value: barcode 拼接使用逗号分隔）
            Map<String, String> productCode = cmd.getBarcodes().stream()
                    .collect(Collectors.toMap(OrderShippingBarcodeCmd::getItemNo,
                            OrderShippingBarcodeCmd::getBarcode, Joiner.on(",")::join));
            shippingCmd.setProductCode(productCode);
        }

        Response response = orderFeignClient.shipping(order.getOrderNo(), shippingCmd);
        return response.isSuccess();
    }

    @Override
    public boolean orderShippingCancel(ShopDO shop, OrderShippingCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean orderDummyShipping(ShopDO shop, OrderDummyShippingCmd cmd) {
        com.xtc.marketing.orderservice.order.dto.OrderDTO order = this.getOrder(shop, cmd.getOrderNo());
        boolean hasShipping = this.checkOrderHasShipping(shop, order.getOrderNo());
        if (hasShipping) {
            return true;
        }
        com.xtc.marketing.orderservice.order.dto.command.OrderDummyShippingCmd dummyShippingCmd =
                com.xtc.marketing.orderservice.order.dto.command.OrderDummyShippingCmd.builder().build();
        Response response = orderFeignClient.dummyShipping(order.getOrderNo(), dummyShippingCmd);
        return response.isSuccess();
    }

    @Override
    public boolean orderRemark(ShopDO shop, OrderRemarkCmd cmd) {
        com.xtc.marketing.orderservice.order.dto.OrderDTO order = this.getOrder(shop, cmd.getOrderNo());
        OrderSellerMemoCmd sellerMemoCmd = OrderSellerMemoCmd.builder().sellerMemo(cmd.getRemark()).build();
        Response response = orderFeignClient.sellerMemoFromErp(order.getOrderNo(), sellerMemoCmd);
        return response.isSuccess();
    }

    @Override
    public OrderDecryptDTO orderDecrypt(ShopDO shop, OrderDecryptCmd cmd) {
        // 校验订单是否存在
        this.getOrder(shop, cmd.getOrderNo());
        // 官方商城订单解密
        if ("XTC_SHOP".equals(shop.getShopCode())) {
            XtcShopEncryptQry encryptQry = GsonUtil.jsonToBean(cmd.getCiphers().get(0), XtcShopEncryptQry.class);
            XtcShopDecryptDTO xtcShopDecryptDTO = xtcShopRpc.xtcShopOrderDecrypt(encryptQry);
            return OrderDecryptDTO.builder().name(xtcShopDecryptDTO.getReceiverName())
                    .mobile(xtcShopDecryptDTO.getReceiverMobile()).address(xtcShopDecryptDTO.getReceiverAddress()).build();
        }
        // 会员商城订单解密
        if ("XTC_MALL".equals(shop.getShopCode())) {
            XtcMallEncryptQry encryptQry = GsonUtil.jsonToBean(cmd.getCiphers().get(0), XtcMallEncryptQry.class);
            XtcMallDecryptDTO xtcMallDecryptDTO = xtcMallRpc.xtcMallOrderDecrypt(encryptQry);
            return OrderDecryptDTO.builder().name(xtcMallDecryptDTO.getReceiverName())
                    .mobile(xtcMallDecryptDTO.getReceiverMobile()).address(xtcMallDecryptDTO.getReceiverAddress()).build();
        }
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<InvoiceApplyDTO> pageInvoiceApply(ShopDO shop, InvoiceApplyPageQry qry) {
        com.xtc.marketing.orderservice.order.dto.query.OrderPageQry orderQry = com.xtc.marketing.orderservice.order.dto.query.OrderPageQry.builder()
                .bizCode(shop.getAppKey())
                .orderState(OrderState.WAIT_BUYER_CONFIRM_PRODUCT)
                .updateTimeStart(qry.getStartTime())
                .updateTimeEnd(qry.getEndTime())
                .pageIndex(qry.getPageIndex())
                .pageSize(qry.getPageSize())
                .build();
        PageResponse<com.xtc.marketing.orderservice.order.dto.OrderDTO> pageOrders = orderFeignClient.pageOrders(orderQry);
        if (CollectionUtils.isEmpty(pageOrders.getData())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }

        // 转化统一发票申请记录
        List<OrderDTO> orderDTO = orderServiceShopConverter.toAdapterOrderDTO(pageOrders.getData());
        List<InvoiceApplyDTO> invoiceApplyList = orderDTO.stream()
                .map(OrderDTO::getInvoiceApply)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pageOrders.getData())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }
        return PageResponse.of(invoiceApplyList, invoiceApplyList.size(), qry.getPageSize(), qry.getPageIndex());
    }

    @Override
    public InvoiceApplyDTO getInvoiceApply(ShopDO shop, InvoiceApplyGetQry qry) {
        InvoiceDTO invoiceDTO = orderFeignClient.getInvoice(qry.getOrderNo()).getData();
        return InvoiceApplyDTO.builder()
                .orderNo(qry.getOrderNo())
                .invoiceType(InvoiceTypeEnum.ELECTRONIC)
                .invoiceTitleType(invoiceDTO.getInvoiceTitleType() == InvoiceTitleType.COMPANY ? InvoiceTitleTypeEnum.COMPANY : InvoiceTitleTypeEnum.PERSONAL)
                .invoiceTitle(invoiceDTO.getInvoiceTitle())
                .taxNo(invoiceDTO.getTaxNumber())
                .build();
    }

    @Override
    public boolean uploadInvoiceFile(ShopDO shop, OrderUploadInvoiceCmd cmd, MultipartFile invoiceFile) {
        Response response = orderFeignClient.uploadInvoice(cmd.getOrderNo(), invoiceFile);
        return response.isSuccess();
    }

    @Override
    public boolean uploadInvoiceBase64(ShopDO shop, InvoiceUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public InvoiceAmountDTO getInvoiceAmount(ShopDO shop, String orderNo) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<CommentDTO> pageComments(ShopDO shop, CommentPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<RefundDTO> pageRefunds(ShopDO shop, RefundPageQry qry) {
        ServiceOrderPageQry servicePageQry = ServiceOrderPageQry.builder()
                .bizCode(shop.getAppKey())
                .pageIndex(qry.getPageIndex())
                .pageSize(qry.getPageSize())
                .updateTimeStart(qry.getUpdateTimeStart())
                .updateTimeEnd(qry.getUpdateTimeEnd())
                .orderNo(qry.getOrderNo())
                .build();

        PageResponse<ServiceOrderDTO> response = serviceOrderFeignClient.pageServices(servicePageQry);
        if (CollectionUtils.isEmpty(response.getData())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }

        List<RefundDTO> refunds = orderServiceShopConverter.toAdapterRefundDTO(response.getData());
        return PageResponse.of(refunds, response.getTotalCount(), response.getPageSize(), response.getPageIndex());
    }

    @Override
    public RefundDTO getRefund(ShopDO shop, RefundGetQry qry) {
        ServiceOrderDTO serviceOrder = serviceOrderFeignClient.getByServiceNo(qry.getRefundId()).getData();
        if (!shop.getAppKey().equals(serviceOrder.getBizCode())) {
            throw BizException.of("退款单不存在");
        }
        RefundDTO refundDTO = orderServiceShopConverter.toAdapterRefundDTO(serviceOrder);
        refundDTO.setOriginData(GsonUtil.objectToJson(serviceOrder));
        return refundDTO;
    }

    @Override
    public ShopLogisticsOrderDTO createLogisticsOrder(ShopDO shop, ShopLogisticsOrderCreateCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean cancelLogisticsOrder(ShopDO shop, ShopLogisticsOrderCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void refundGoodsToWarehouse(ShopDO shop, RefundGoodsToWarehouseCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void barcodeUpload(ShopDO shop, BarcodeUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    /**
     * 查询订单
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 订单
     */
    private com.xtc.marketing.orderservice.order.dto.OrderDTO getOrder(ShopDO shop, String orderNo) {
        com.xtc.marketing.orderservice.order.dto.OrderDTO order = orderFeignClient.getByOrderNo(orderNo).getData();
        if (order == null || !shop.getAppKey().equals(order.getBizCode())) {
            throw BizException.of("订单不存在");
        }
        return order;
    }

    /**
     * 根据订单明细里的 skuIds 并且转换成统一订单数据
     *
     * @param platformOrder 平台订单
     * @return 统一订单数据
     */
    private OrderDTO splitSkuIdsAndConvertToAdapterOrderDTO(com.xtc.marketing.orderservice.order.dto.OrderDTO platformOrder) {
        // 拆分平台维护的 skuId
        List<com.xtc.marketing.orderservice.order.dto.OrderItemDTO> splitSkuItems =
                SkuUtil.splitSkuIdsAndCloneItem(platformOrder.getItems(),
                        com.xtc.marketing.orderservice.order.dto.OrderItemDTO::getSkuErpCode,
                        com.xtc.marketing.orderservice.order.dto.OrderItemDTO::setSkuErpCode);
        platformOrder.setItems(splitSkuItems);
        // 转换统一的数据结构
        return orderServiceShopConverter.toAdapterOrderDTO(platformOrder);
    }

    /**
     * 检查订单已发货
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 执行结果
     */
    private boolean checkOrderHasShipping(ShopDO shop, String orderNo) {
        com.xtc.marketing.orderservice.order.dto.OrderDTO order = this.getOrder(shop, orderNo);
        // 判断订单状态已发货，无需重复发货
        if (order.getOrderState() == OrderState.WAIT_BUYER_CONFIRM_PRODUCT ||
                order.getOrderState() == OrderState.TRADE_FINISHED) {
            log.warn("订单已发货，无需重复发货 {} {}", orderNo, order.getOrderState());
            return true;
        }
        // 判断订单状态不是待发货，抛异常
        if (order.getOrderState() != OrderState.WAIT_SELLER_SHIP_PRODUCT) {
            // 例：平台的订单状态不符合推送发货状态的条件 T1920231025000209 orderState: WAIT_BUYER_PAY
            String msg = String.format("%s %s orderState: %s",
                    BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrDesc(),
                    orderNo, order.getOrderState());
            throw new BizException(BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrCode(), msg);
        }
        return false;
    }

}
