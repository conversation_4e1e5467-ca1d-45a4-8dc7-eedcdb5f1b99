package com.xtc.marketing.adapterservice.notify;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.notify.dataobject.PushLogDO;
import com.xtc.marketing.adapterservice.notify.dataobject.ReceiveLogDO;
import com.xtc.marketing.adapterservice.notify.dto.PushLogDTO;
import com.xtc.marketing.adapterservice.notify.dto.ReceiveLogDTO;
import com.xtc.marketing.adapterservice.notify.dto.query.PushLogPageQry;
import com.xtc.marketing.adapterservice.notify.dto.query.ReceiveLogPageQry;
import com.xtc.marketing.adapterservice.notify.repository.PushLogRepository;
import com.xtc.marketing.adapterservice.notify.repository.ReceiveLogRepository;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 通知管理服务实现
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class NotifyAdminServiceImpl implements NotifyAdminService {

    private final PushLogRepository pushLogRepository;
    private final ReceiveLogRepository receiveLogRepository;

    @Override
    public PageResponse<PushLogDTO> pagePushLogs(PushLogPageQry qry) {
        IPage<PushLogDO> page = pushLogRepository.pageBy(qry);
        return PageResponse.of(page, PushLogDTO::new);
    }

    @Override
    public PageResponse<ReceiveLogDTO> pageReceiveLogs(ReceiveLogPageQry qry) {
        IPage<ReceiveLogDO> page = receiveLogRepository.pageBy(qry);
        return PageResponse.of(page, ReceiveLogDTO::new);
    }

    @Override
    public void removeReceiveLog(long id) {
        ReceiveLogDO receiveLogDO = receiveLogRepository.getById(id);
        if (receiveLogDO == null) {
            throw BizException.of("接收记录不存在");
        }
        receiveLogRepository.removeById(id);
    }

}
