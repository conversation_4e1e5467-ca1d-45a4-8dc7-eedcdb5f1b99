package com.xtc.marketing.adapterservice.shop.converter;

import com.google.common.collect.Maps;
import com.google.gson.JsonObject;
import com.pdd.pop.sdk.http.api.pop.response.*;
import com.xtc.marketing.adapterservice.invoice.enums.InvoiceCreateType;
import com.xtc.marketing.adapterservice.rpc.pdd.PddRpc;
import com.xtc.marketing.adapterservice.rpc.pdd.pdddto.PddInvoiceDetailUploadRequest;
import com.xtc.marketing.adapterservice.rpc.pdd.pdddto.PddWaybillGetRequest;
import com.xtc.marketing.adapterservice.rpc.pdd.pdddto.enums.PddLogisticsCompany;
import com.xtc.marketing.adapterservice.shop.constant.ShopOrderConstant;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.InvoiceItemCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.InvoiceUploadCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsCargoCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsOrderCreateCmd;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTitleTypeEnum;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTypeEnum;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.MoneyUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

@Mapper(
        componentModel = "spring",
        uses = {BaseShopConverter.class},
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface PddShopConverter {

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "orderSn")
    @Mapping(target = "orderType", source = "tradeType")
    @Mapping(target = "orderState", expression = "java(convertOrderState(source.getOrderStatus(), source.getRefundStatus()))")
    @Mapping(target = "stepOrderState", source = "stepOrderInfo.stepTradeStatus")
    @Mapping(target = "riskState", source = "riskControlStatus", qualifiedByName = "toRiskState")
    @Mapping(target = "sellerMemo", source = "remark", qualifiedByName = "blankToNull")
    @Mapping(target = "buyerId", source = "receiverName", qualifiedByName = "blankToNull")
    @Mapping(target = "buyerName", source = "receiverNameMask", qualifiedByName = "blankToNull")
    @Mapping(target = "buyerMemo", source = "buyerMemo", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverName", source = "receiverName", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverMobile", source = "receiverPhone", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverProvince", source = "province", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverCity", source = "city", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverDistrict", source = "town", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverAddress", source = "receiverAddress", qualifiedByName = "blankToNull")
    // 金额和时间
    @Mapping(target = "priceTotal", source = "goodsAmount", qualifiedByName = "yuanToCent")
    @Mapping(target = "payment", expression = "java(sumAmount(source.getPayAmount(), source.getPlatformDiscount()))")
    @Mapping(target = "shippingPayment", source = "postage", qualifiedByName = "yuanToCent")
    @Mapping(target = "discount", expression = "java(sumAmount(source.getPlatformDiscount(), source.getDuoDuoPayReduction()))")
    @Mapping(target = "updateTime", source = "updatedAt", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "orderTime", source = "createdTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "payTime", source = "payTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "latestShippingTime", source = "lastShipTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "shippingTime", source = "shippingTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "completedTime", source = "confirmTime", qualifiedByName = "toLocalDateTime")
    // 子对象转换
    @Mapping(target = "items", source = "itemList")
    OrderDTO toAdapterOrderDTO(PddOrderNumberListIncrementGetResponse.OrderSnIncrementGetResponseOrderSnListItem source);

    /**
     * 转换统一订单明细
     *
     * @param source 平台订单明细
     * @return 统一订单明细
     */
    @Mapping(target = "productId", source = "goodsId")
    @Mapping(target = "productName", source = "goodsName")
    @Mapping(target = "productErpCode", source = "outerGoodsId")
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "skuName", source = "goodsSpec")
    @Mapping(target = "skuErpCode", source = "outerId")
    @Mapping(target = "num", source = "goodsCount")
    @Mapping(target = "unitPrice", source = "goodsPrice", qualifiedByName = "yuanToCent")
    @Mapping(target = "priceTotal", expression = "java(yuanToCent(source.getGoodsPrice() * source.getGoodsCount()))")
    @Mapping(target = "payment", expression = "java(yuanToCent(source.getGoodsPrice() * source.getGoodsCount()))")
    OrderItemDTO toAdapterOrderItemDTO(PddOrderNumberListIncrementGetResponse.OrderSnIncrementGetResponseOrderSnListItemItemListItem source);

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "orderSn")
    @Mapping(target = "orderType", source = "tradeType")
    @Mapping(target = "orderState", expression = "java(convertOrderState(source.getOrderStatus(), source.getRefundStatus()))")
    @Mapping(target = "stepOrderState", source = "stepOrderInfo.stepTradeStatus")
    @Mapping(target = "riskState", source = "riskControlStatus", qualifiedByName = "toRiskState")
    @Mapping(target = "sellerMemo", source = "remark", qualifiedByName = "blankToNull")
    @Mapping(target = "buyerId", source = "receiverName", qualifiedByName = "blankToNull")
    @Mapping(target = "buyerName", source = "receiverNameMask", qualifiedByName = "blankToNull")
    @Mapping(target = "buyerMemo", source = "buyerMemo", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverName", source = "receiverName", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverMobile", source = "receiverPhone", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverProvince", source = "province", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverCity", source = "city", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverDistrict", source = "town", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverAddress", source = "receiverAddress", qualifiedByName = "blankToNull")
    // 金额和时间
    @Mapping(target = "priceTotal", source = "goodsAmount", qualifiedByName = "yuanToCent")
    @Mapping(target = "payment", expression = "java(sumAmount(source.getPayAmount(), source.getPlatformDiscount()))")
    @Mapping(target = "shippingPayment", source = "postage", qualifiedByName = "yuanToCent")
    @Mapping(target = "discount", expression = "java(sumAmount(source.getPlatformDiscount(), source.getDuoDuoPayReduction()))")
    @Mapping(target = "updateTime", source = "updatedAt", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "orderTime", source = "createdTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "payTime", source = "payTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "latestShippingTime", source = "lastShipTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "shippingTime", source = "shippingTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "completedTime", source = "confirmTime", qualifiedByName = "toLocalDateTime")
    // 子对象转换
    @Mapping(target = "items", source = "itemList")
    OrderDTO toAdapterOrderDTO(PddOrderInformationGetResponse.OrderInfoGetResponseOrderInfo source);

    /**
     * 转换统一订单明细
     *
     * @param source 平台订单明细
     * @return 统一订单明细
     */
    @Mapping(target = "productId", source = "goodsId")
    @Mapping(target = "productName", source = "goodsName")
    @Mapping(target = "productErpCode", source = "outerGoodsId")
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "skuName", source = "goodsSpec")
    @Mapping(target = "skuErpCode", source = "outerId")
    @Mapping(target = "num", source = "goodsCount")
    @Mapping(target = "unitPrice", source = "goodsPrice", qualifiedByName = "yuanToCent")
    @Mapping(target = "priceTotal", expression = "java(yuanToCent(source.getGoodsPrice() * source.getGoodsCount()))")
    @Mapping(target = "payment", expression = "java(yuanToCent(source.getGoodsPrice() * source.getGoodsCount()))")
    OrderItemDTO toAdapterOrderItemDTO(PddOrderInformationGetResponse.OrderInfoGetResponseOrderInfoItemListItem source);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    List<RefundDTO> toAdapterRefundDTO(List<PddRefundListIncrementGetResponse.RefundIncrementGetResponseRefundListItem> source);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "orderSn")
    @Mapping(target = "serviceNo", source = "id")
    @Mapping(target = "serviceType", source = "afterSalesType")
    @Mapping(target = "serviceState", source = "afterSalesStatus")
    @Mapping(target = "applyReason", source = "afterSaleReason")
    @Mapping(target = "returnExpressCompany", source = "shippingName")
    @Mapping(target = "returnWaybillNo", source = "trackingNumber")
    @Mapping(target = "productId", source = "goodsId")
    @Mapping(target = "productName", source = "goodsName")
    @Mapping(target = "skuId", source = "outerId")
    @Mapping(target = "num", source = "goodsNumber")
    // 金额和时间
    @Mapping(target = "refundApplyAmount", source = "refundAmount", qualifiedByName = "yuanStrToCent")
    @Mapping(target = "refundAmount", source = "refundAmount", qualifiedByName = "yuanStrToCent")
    @Mapping(target = "unitPrice", source = "goodsPrice", qualifiedByName = "yuanStrToCent")
    @Mapping(target = "payment", source = "orderAmount", qualifiedByName = "yuanStrToCent")
    @Mapping(target = "updateTime", source = "updatedTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "createTime", source = "createdTime", qualifiedByName = "toLocalDateTime")
    RefundDTO toAdapterRefundDTO(PddRefundListIncrementGetResponse.RefundIncrementGetResponseRefundListItem source);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "orderSn")
    @Mapping(target = "serviceNo", source = "id")
    @Mapping(target = "serviceType", source = "afterSalesType")
    @Mapping(target = "serviceState", source = "afterSalesStatus")
    @Mapping(target = "applyReason", source = "afterSalesReason")
    @Mapping(target = "returnExpressCompany", source = "shippingName")
    @Mapping(target = "returnWaybillNo", source = "expressNo")
    @Mapping(target = "productId", source = "skuId")
    @Mapping(target = "productName", source = "skuId")
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "num", source = "goodsNumber")
    // 金额和时间
    @Mapping(target = "refundApplyAmount", source = "refundAmount")
    @Mapping(target = "refundAmount", source = "refundAmount")
    @Mapping(target = "unitPrice", source = "goodsPrice")
    @Mapping(target = "payment", source = "orderAmount")
    @Mapping(target = "updateTime", source = "updatedTime", qualifiedByName = "longToLocalDateTime")
    @Mapping(target = "createTime", source = "recreatedAt", qualifiedByName = "longToLocalDateTime")
    @Mapping(target = "originData", expression = "java(com.xtc.marketing.adapterservice.util.GsonUtil.objectToJson(source))")
    RefundDTO toAdapterRefundDTO(PddRefundInformationGetResponse source);

    /**
     * 转换统一发票申请数据
     *
     * @param source 平台发票申请数据
     * @return 统一发票申请数据
     */
    @Mapping(target = "orderNo", source = "orderSn")
    @Mapping(target = "invoiceType", source = "invoiceKind", qualifiedByName = "toAdapterInvoiceTypeEnum")
    @Mapping(target = "invoiceTitleType", source = "businessType", qualifiedByName = "toAdapterInvoiceTitleTypeEnum")
    @Mapping(target = "invoiceTitle", source = "payerName")
    @Mapping(target = "taxNo", source = "payerRegisterNo")
    @Mapping(target = "bankName", source = "payerBank")
    @Mapping(target = "bankAccount", source = "payerAccount")
    @Mapping(target = "companyMobile", source = "payerPhone")
    @Mapping(target = "companyAddress", source = "payerAddress")
    @Mapping(target = "buyerMemo", source = "memo")
    @Mapping(target = "invoiceAmount", source = "invoiceAmount")
    InvoiceApplyDTO toAdapterInvoiceApplyDTO(PddInvoiceApplicationQueryResponse.InvoiceApplicationQueryResponseInvoiceApplicationListItem source);

    /**
     * 转换拼多多平台发票上传数据
     *
     * @param source 参数
     * @return 拼多多平台发票上传数据
     */
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "orderSn", source = "orderNo")
    @Mapping(target = "payerName", source = "invoiceTitle")
    @Mapping(target = "payerBank", source = "payerBank")
    @Mapping(target = "payerAccount", source = "payerAccount")
    @Mapping(target = "payerAddress", source = "payerAddress")
    @Mapping(target = "payerPhone", source = "payerPhone", conditionExpression = "java(\"COMPANY\".equals(source.getInvoiceTitleType().name()))")
    @Mapping(target = "payerRegisterNo", source = "taxNo", conditionExpression = "java(\"COMPANY\".equals(source.getInvoiceTitleType().name()))")
    @Mapping(target = "sumTax", source = "tax")
    @Mapping(target = "taxRate", source = "taxRate")
    @Mapping(target = "payeeOperator", source = "payeeOperator")
    @Mapping(target = "invoiceKind", source = "invoiceType", qualifiedByName = "toPddInvoiceType")
    @Mapping(target = "businessType", source = "invoiceTitleType", qualifiedByName = "toPddInvoiceTitleType")
    @Mapping(target = "invoiceType", source = "createType", qualifiedByName = "toPddInvoiceCreateType")
    @Mapping(target = "sumPrice", source = "sumPrice", qualifiedByName = "toPddPrice")
    @Mapping(target = "invoiceTime", source = "blueTime", qualifiedByName = "toPddInvoiceTime")
    @Mapping(target = "sellerName", source = "payeeName")
    @Mapping(target = "sellerRegisterNo", source = "payeeRegisterNo")
    @Mapping(target = "invoiceCategory", expression = "java(\"COMPANY\".equals(source.getInvoiceTitleType().name()) ? 6 : 5)")
    @Mapping(target = "invoiceItemList", source = "invoiceItems")
    PddInvoiceDetailUploadRequest toPddInvoiceDetailUploadRequest(InvoiceUploadCmd source);

    /**
     * 转换拼多多平台发票上传明细
     *
     * @param source 参数
     * @return 拼多多平台发票上传明细
     */
    @Mapping(target = "productName", source = "source.goodsName")
    @Mapping(target = "specification", source = "source.specification")
    @Mapping(target = "quantity", source = "source.num")
    @Mapping(target = "unit", source = "source.unit")
    @Mapping(target = "invoiceFileContent", source = "source.invoiceFileContent")
    @Mapping(target = "invoiceNo", source = "invoiceNo")
    PddInvoiceDetailUploadRequest.InvoiceItemListItem toPddInvoiceDetailUploadRequestItem(InvoiceItemCmd source);

    /**
     * 转换统一发票申请数据
     *
     * @param source 平台发票申请数据
     * @return 统一发票申请数据
     */
    List<InvoiceApplyDTO> toAdapterInvoiceApplyDTO(List<PddInvoiceApplicationQueryResponse.InvoiceApplicationQueryResponseInvoiceApplicationListItem> source);

    /**
     * 转换统一解密数据
     *
     * @param source 平台解密数据
     * @return 统一解密数据
     */
    OrderDecryptDTO toAdapterOrderDecryptDTO(PddOpenDecryptBatchResponse.OpenDecryptBatchResponse source);

    /**
     * 转换平台电子面单参数
     *
     * @param source 统一电子面单参数
     * @param order  平台电子面单，订单参数
     * @return 平台电子面单参数
     */
    @Mapping(target = "paramWaybillCloudPrintApplyNewRequest.needEncrypt", constant = "false")
    @Mapping(target = "paramWaybillCloudPrintApplyNewRequest.wpCode", source = "source.logisticsCompany")
    @Mapping(target = "paramWaybillCloudPrintApplyNewRequest.sender.name", source = "source.senderName")
    @Mapping(target = "paramWaybillCloudPrintApplyNewRequest.sender.phone", source = "source.senderPhone")
    @Mapping(target = "paramWaybillCloudPrintApplyNewRequest.sender.address.province", source = "source.senderProvince")
    @Mapping(target = "paramWaybillCloudPrintApplyNewRequest.sender.address.city", source = "source.senderCity")
    @Mapping(target = "paramWaybillCloudPrintApplyNewRequest.sender.address.district", source = "source.senderDistrict")
    @Mapping(target = "paramWaybillCloudPrintApplyNewRequest.sender.address.detail", source = "source.senderAddress")
    @Mapping(target = "paramWaybillCloudPrintApplyNewRequest.tradeOrderInfoDtos", source = "order", qualifiedByName = "newSingletonList")
    PddWaybillGetRequest toPddWaybillGetRequest(ShopLogisticsOrderCreateCmd source,
                                                PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItem order);

    /**
     * 转换平台电子面单参数
     *
     * @param shop             店铺
     * @param source           统一电子面单参数
     * @param logisticsCompany 快递公司
     * @return 平台电子面单参数
     */
    @Mapping(target = "objectId", source = "source.logisticsOrderId", defaultExpression = "java(java.util.UUID.randomUUID().toString().replace(\"-\", \"\"))")
    @Mapping(target = "userId", source = "shop.shopId")
    @Mapping(target = "orderInfo.orderChannelsType", constant = "PDD")
    @Mapping(target = "orderInfo.tradeOrderList", source = "source.shopOrderNo", qualifiedByName = "newSingletonList")
    @Mapping(target = "recipient.name", source = "source.receiverName")
    @Mapping(target = "recipient.mobile", source = "source.receiverMobile")
    @Mapping(target = "recipient.address.province", source = "source.receiverProvince")
    @Mapping(target = "recipient.address.city", source = "source.receiverCity")
    @Mapping(target = "recipient.address.district", source = "source.receiverDistrict")
    @Mapping(target = "recipient.address.detail", source = "source.receiverAddress")
    @Mapping(target = "packageInfo.goodsDescription", source = "source.mainCargoName")
    @Mapping(target = "packageInfo.items", source = "source.cargos")
    @Mapping(target = "templateUrl", source = "logisticsCompany.templateUrl")
    @Mapping(target = "logisticsServices", expression = "java(toPddWaybillGetRequestLogisticsServices(source, logisticsCompany))")
    PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItem toPddWaybillGetRequestOrder(
            ShopLogisticsOrderCreateCmd source, ShopDO shop, PddLogisticsCompany logisticsCompany);

    /**
     * 转换平台电子面单参数
     *
     * @param source 统一电子面单参数
     * @return 平台电子面单参数
     */
    @Mapping(target = "name", source = "name")
    @Mapping(target = "count", source = "quantity")
    PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItemPackageInfoItemsItem toPddWaybillGetRequestCargo(
            ShopLogisticsCargoCmd source);

    /**
     * 转换统一订单数据时，设置特殊参数
     *
     * @param order         统一订单数据
     * @param platformOrder 平台订单数据
     */
    @AfterMapping
    default void setOrderParam(@MappingTarget OrderDTO order, PddOrderNumberListIncrementGetResponse.OrderSnIncrementGetResponseOrderSnListItem platformOrder) {
        if (order == null) {
            return;
        }
        // 计算商家优惠金额
        if (CollectionUtils.isNotEmpty(platformOrder.getPromotionDetailList())) {
            int sellerDiscount = platformOrder.getPromotionDetailList().stream()
                    .filter(promotionDetail -> PddRpc.isSellerPromotionType(promotionDetail.getPromotionType()))
                    .map(PddOrderNumberListIncrementGetResponse.OrderSnIncrementGetResponseOrderSnListItemPromotionDetailListItem::getDiscountAmount)
                    .map(MoneyUtil::yuanToCent)
                    .reduce(Integer::sum)
                    .orElse(0);
            // 折扣金额需要减去商家优惠金额，不属于店铺的实收
            order.setDiscount(order.getDiscount() - sellerDiscount);
        }
        // 识别国家补贴订单
        if (CollectionUtils.isNotEmpty(platformOrder.getOrderTagList())) {
            boolean isNationalSubsidy = platformOrder.getOrderTagList().stream()
                    .filter(tag -> "trade_in_national_subsidy".equals(tag.getName()))
                    .map(PddOrderNumberListIncrementGetResponse.OrderSnIncrementGetResponseOrderSnListItemOrderTagListItem::getValue)
                    .anyMatch(value -> value == 1);
            if (isNationalSubsidy) {
                order.setOrderType(ShopOrderConstant.ORDER_TYPE_NATIONAL_SUBSIDY);
                order.setOrderTypeDesc(ShopOrderConstant.ORDER_TYPE_DESC_NATIONAL_SUBSIDY);
            }
        }
    }

    /**
     * 转换统一订单数据时，设置特殊参数
     *
     * @param order         统一订单数据
     * @param platformOrder 平台订单数据
     */
    @AfterMapping
    default void setOrderParam(@MappingTarget OrderDTO order, PddOrderInformationGetResponse.OrderInfoGetResponseOrderInfo platformOrder) {
        if (order == null) {
            return;
        }
        // 计算商家优惠金额
        if (CollectionUtils.isNotEmpty(platformOrder.getPromotionDetailList())) {
            int sellerDiscount = platformOrder.getPromotionDetailList().stream()
                    .filter(promotionDetail -> PddRpc.isSellerPromotionType(promotionDetail.getPromotionType()))
                    .map(PddOrderInformationGetResponse.OrderInfoGetResponseOrderInfoPromotionDetailListItem::getDiscountAmount)
                    .map(MoneyUtil::yuanToCent)
                    .reduce(Integer::sum)
                    .orElse(0);
            // 折扣金额需要减去商家优惠金额，不属于店铺的实收
            order.setDiscount(order.getDiscount() - sellerDiscount);
        }
        // 识别国家补贴订单
        if (CollectionUtils.isNotEmpty(platformOrder.getOrderTagList())) {
            boolean isNationalSubsidy = platformOrder.getOrderTagList().stream()
                    .filter(tag -> "trade_in_national_subsidy".equals(tag.getName()))
                    .map(PddOrderInformationGetResponse.OrderInfoGetResponseOrderInfoOrderTagListItem::getValue)
                    .anyMatch(value -> value == 1);
            if (isNationalSubsidy) {
                order.setOrderType(ShopOrderConstant.ORDER_TYPE_NATIONAL_SUBSIDY);
                order.setOrderTypeDesc(ShopOrderConstant.ORDER_TYPE_DESC_NATIONAL_SUBSIDY);
            }
        }
    }

    /**
     * 转换统一解密数据时，设置对应的解密字段
     *
     * @param decrypt         统一解密数据
     * @param platformDecrypt 平台解密数据
     */
    @AfterMapping
    default void setDecrypt(@MappingTarget OrderDecryptDTO decrypt,
                            PddOpenDecryptBatchResponse.OpenDecryptBatchResponse platformDecrypt) {
        if (CollectionUtils.isEmpty(platformDecrypt.getDataDecryptList())) {
            return;
        }
        // 初始化未知类型的解密集合
        decrypt.setDecryptTexts(Maps.newHashMapWithExpectedSize(platformDecrypt.getDataDecryptList().size()));
        // 根据类型判断解密字段
        platformDecrypt.getDataDecryptList().forEach(decryptInfo -> {
            String decryptText = decryptInfo.getDecryptedData();
            // 解密字段为空时，放到未知类型的解密集合
            if (StringUtils.isBlank(decryptText)) {
                decrypt.getDecryptTexts().put(decryptInfo.getEncryptedData(), decryptInfo.getErrorMsg());
                return;
            }
            // 加密类型 5：收件人（末尾包含虚拟分机号）；6：收件人手机号；7：收件人完整地址（末尾包含虚拟分机号）；
            if (decryptInfo.getDataType() == 5) {
                decrypt.setName(decryptText);
            } else if (decryptInfo.getDataType() == 6) {
                String mobile = Optional.ofNullable(decryptInfo.getVirtualIdentifyNumber())
                        .filter(StringUtils::isNotBlank)
                        .map(virtualIdentifyNumber -> decryptText + "-" + virtualIdentifyNumber)
                        .orElse(decryptText);
                decrypt.setMobile(mobile);
            } else if (decryptInfo.getDataType() == 7) {
                decrypt.setAddress(decryptText);
            }
        });
    }

    /**
     * 转换物流服务配置
     *
     * @param source           统一电子面单参数
     * @param logisticsCompany 快递公司
     * @return 物流服务配置
     */
    @Named("toPddWaybillGetRequestLogisticsServices")
    default String toPddWaybillGetRequestLogisticsServices(ShopLogisticsOrderCreateCmd source, PddLogisticsCompany logisticsCompany) {
        if (StringUtils.isAnyBlank(source.getLogisticsType(), logisticsCompany.getLogisticsService())) {
            return null;
        }
        return logisticsCompany.getLogisticsService(source.getLogisticsType());
    }

    /**
     * 转换统一风险状态
     *
     * @param riskControlStatus 平台风险状态
     * @return 统一风险状态
     */
    @Named("toRiskState")
    default boolean toRiskState(Integer riskControlStatus) {
        return riskControlStatus != null && riskControlStatus != 0;
    }

    /**
     * 转换订单状态
     *
     * @param orderStatus  订单状态
     * @param refundStatus 退款状态
     * @return 订单状态
     */
    @Named("convertOrderState")
    default String convertOrderState(Integer orderStatus, Integer refundStatus) {
        // 默认使用 orderState 当作订单状态，如果退款成功，则使用退款状态
        if (refundStatus != null && refundStatus == 4) {
            return refundStatus.toString();
        }
        return orderStatus != null ? orderStatus.toString() : null;
    }

    /**
     * 转换发票类型
     *
     * @param invoiceType 发票类型
     * @return 发票类型枚举
     */
    @Named("toAdapterInvoiceTypeEnum")
    default InvoiceTypeEnum toAdapterInvoiceTypeEnum(Long invoiceType) {
        // 默认值为【电子发票】，发票种类：0=电子发票，1=纸质发票，2=专票
        if (invoiceType == 1) {
            return InvoiceTypeEnum.NORMAL;
        }
        if (invoiceType == 2) {
            return InvoiceTypeEnum.SPECIAL;
        }
        return InvoiceTypeEnum.ELECTRONIC;
    }

    /**
     * 转换发票类型
     *
     * @param invoiceType 发票类型
     * @return 发票类型枚举
     */
    @Named("toPddInvoiceType")
    default Integer toPddInvoiceType(InvoiceTypeEnum invoiceType) {
        // 默认值为【电子发票】，发票种类：0=电子发票，1=纸质发票，2=专票
        if (invoiceType == InvoiceTypeEnum.NORMAL) {
            return 1;
        }
        if (invoiceType == InvoiceTypeEnum.SPECIAL) {
            return 2;
        }
        return 0;
    }

    /**
     * 转换发票抬头类型
     *
     * @param invoiceTitleType 发票抬头类型
     * @return 发票抬头类型枚举
     */
    @Named("toAdapterInvoiceTitleTypeEnum")
    default InvoiceTitleTypeEnum toAdapterInvoiceTitleTypeEnum(Integer invoiceTitleType) {
        // 默认值为【个人】，发票抬头类型：0=个人，1=企业
        if (invoiceTitleType == 1) {
            return InvoiceTitleTypeEnum.COMPANY;
        }
        return InvoiceTitleTypeEnum.PERSONAL;
    }

    /**
     * 转换发票抬头类型
     *
     * @param invoiceTitleType 发票抬头类型
     * @return 发票抬头类型枚举
     */
    @Named("toPddInvoiceTitleType")
    default Integer toPddInvoiceTitleType(InvoiceTitleTypeEnum invoiceTitleType) {
        // 默认值为【个人】，发票抬头类型：0=个人，1=企业
        if (invoiceTitleType == InvoiceTitleTypeEnum.COMPANY) {
            return 1;
        }
        return 0;
    }

    /**
     * 转换发票创建类型
     *
     * @param createType 发票创建类型
     * @return 发票创建类型
     */
    @Named("toPddInvoiceCreateType")
    default Integer toPddInvoiceCreateType(InvoiceCreateType createType) {
        // 开票类型：0-蓝票，1-红票；目前 只支持0
        if (InvoiceCreateType.BLUE.equals(createType)) {
            return 0;
        }
        return 1;
    }

    /**
     * 转换发票时间
     *
     * @param invoiceTime 发票时间
     * @return 发票时间
     */
    @Named("toPddInvoiceTime")
    default long toPddInvoiceTime(LocalDateTime invoiceTime) {
        return DateUtil.toEpochMilli(invoiceTime);
    }

    /**
     * 转换发票总价
     *
     * @param price 发票总价
     * @return 发票创建类型
     */
    @Named("toPddPrice")
    default Long toPddPrice(Integer price) {
        return Long.valueOf(price);
    }

    /**
     * 生成单例集合
     *
     * @param obj 对象
     * @return 单例集合
     */
    @Named("newSingletonList")
    default <T> List<T> newSingletonList(T obj) {
        return Collections.singletonList(obj);
    }

    /**
     * 生成收件人密文 json
     *
     * @param receiverName    收件人姓名
     * @param receiverMobile  收件人手机号
     * @param receiverAddress 收件人地址
     * @return 收件人密文 json
     */
    @Named("receiverCipherJson")
    default List<String> receiverCipherJson(String receiverName, String receiverMobile, String receiverAddress) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("receiverName", receiverName);
        jsonObject.addProperty("receiverMobile", receiverMobile);
        jsonObject.addProperty("receiverAddress", receiverAddress);
        return Collections.singletonList(jsonObject.toString());
    }

    /**
     * 空字符串转换为 null
     *
     * @param str 字符串
     * @return 转换后的字符串
     */
    @Named("blankToNull")
    default String blankToNull(String str) {
        return StringUtils.defaultIfBlank(str, null);
    }

    /**
     * 汇总金额
     *
     * @param amountArr 金额集合
     * @return 总金额
     */
    @Named("sumAmount")
    default int sumAmount(Double... amountArr) {
        return Stream.of(amountArr)
                .filter(Objects::nonNull)
                .map(this::yuanToCent)
                .reduce(Integer::sum)
                .orElse(0);
    }

    /**
     * 元转分
     *
     * @param yuan 元
     * @return 分
     */
    @Named("yuanToCent")
    default Integer yuanToCent(Double yuan) {
        return MoneyUtil.yuanToCent(yuan);
    }

    /**
     * 元转分
     *
     * @param yuan 元
     * @return 分
     */
    @Named("yuanStrToCent")
    default Integer yuanStrToCent(String yuan) {
        return MoneyUtil.yuanToCent(yuan);
    }

    /**
     * 时间转换
     *
     * @param time 时间
     * @return LocalDateTime
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(String time) {
        if (StringUtils.isBlank(time)) {
            return null;
        }
        return DateUtil.toLocalDateTime(time);
    }

    /**
     * 时间转换
     *
     * @param time 时间
     * @return LocalDateTime
     */
    @Named("longToLocalDateTime")
    default LocalDateTime longToLocalDateTime(String time) {
        if (StringUtils.isBlank(time)) {
            return null;
        }
        return DateUtil.toLocalDateTime(Long.valueOf(time));
    }

}
