package com.xtc.marketing.adapterservice.notify.executor.extension;

import com.alibaba.cola.extension.Extensions;
import com.google.common.collect.ImmutableMap;
import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.notify.dataobject.ReceiveLogDO;
import com.xtc.marketing.adapterservice.notify.dto.command.NotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.dto.command.WechatChannelsShopNotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.enums.NotifyEnum;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.notify.WechatChannelsShopNotify;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.notify.WechatChannelsShopRefundNotifyDTO;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;

import java.util.Map;

/**
 * 通知扩展点 - 微信视频号小店消息通知
 */
@Slf4j
@RequiredArgsConstructor
@Extensions(
        bizId = NotifyExtConstant.BIZ_ID_WECHAT_CHANNELS_SHOP,
        useCase = NotifyExtConstant.USE_CASE_SHOP,
        scenario = {NotifyExtConstant.SCENARIO_NOTIFY, NotifyExtConstant.SCENARIO_REFUND}
)
public class WechatChannelsShopNotifyExt implements NotifyExtPt {

    /**
     * 店铺与OMS平台ID的对应关系
     */
    private static final Map<String, String> OMS_PLATFORM_MAP = ImmutableMap.of(
            "gh_4fda64b8edde", "2090",
            "gh_6398c1d7ac08", "2089",
            "gh_23a27da36d7b", "2124"
    );
    /**
     * 消息事件类型与通知扩展点的对应关系
     */
    private static final Map<String, String> EVENT_SCENARIO_MAP = ImmutableMap.of(
            "channels_ec_aftersale_update", NotifyExtConstant.SCENARIO_REFUND
    );

    @Override
    public ReceiveLogDO createReceiveLog(NotifyEnum notify, NotifyReceiveCmd cmd) {
        // 初始化
        Map<String, String> dataMap = GsonUtil.jsonToMapString(cmd.getData());
        String toUserName = dataMap.get("toUserName");
        if (StringUtils.isBlank(toUserName)) {
            throw BizException.of("微信视频号小店消息通知，店铺配置不存在 " + toUserName);
        }
        // 生成消息处理类
        WechatChannelsShopNotifyReceiveCmd body = GsonUtil.jsonToBean(dataMap.get("body"), WechatChannelsShopNotifyReceiveCmd.class);
        WechatChannelsShopNotify wechatChannelsShopNotify = new WechatChannelsShopNotify(toUserName,
                body.getEncrypt(), dataMap.get("msgSignature"));
        // 验证消息合法
        String timestamp = dataMap.get("timestamp");
        String nonce = dataMap.get("nonce");
        String signature = dataMap.get("signature");
        this.verifyNotify(wechatChannelsShopNotify, timestamp, nonce, signature);
        // 消息密文解密
        String decryptMessage = wechatChannelsShopNotify.decryptMessage();
        log.info("微信视频号小店消息通知 decryptMessage: {}", decryptMessage);
        // 验证消息匹配
        WechatChannelsShopRefundNotifyDTO message = GsonUtil.jsonToBean(decryptMessage, WechatChannelsShopRefundNotifyDTO.class);
        boolean verifyMessage = this.verifyMessage(wechatChannelsShopNotify, message);
        if (BooleanUtils.isFalse(verifyMessage)) {
            log.info("微信视频号小店消息通知，不匹配的事件不做处理 {}", message.getEvent());
            return null;
        }
        // 生成接收记录
        JsonObject rawData = new JsonObject();
        rawData.addProperty("data", decryptMessage);
        rawData.addProperty("omsPlatformId", OMS_PLATFORM_MAP.get(toUserName));
        return ReceiveLogDO.builder()
                .scenarioCode(EVENT_SCENARIO_MAP.getOrDefault(message.getEvent(), null))
                .dataId(message.getData().getRefundId())
                .rawData(rawData.toString())
                .responseStr("success")
                .build();
    }

    @Override
    public ResponseEntity<String> notifyResponse(String responseStr) {
        return ResponseEntity.ok().body(responseStr);
    }

    @Override
    public Object convertToNotifyBean(String data) {
        // 解析数据
        Map<String, String> dataMap = GsonUtil.jsonToMapString(data);
        WechatChannelsShopRefundNotifyDTO message = GsonUtil.jsonToBean(dataMap.get("data"), WechatChannelsShopRefundNotifyDTO.class);
        // 转换为推送 body
        JsonObject pushBody = new JsonObject();
        pushBody.addProperty("platformId", dataMap.get("omsPlatformId"));
        pushBody.addProperty("refundId", message.getData().getRefundId());
        pushBody.addProperty("tradeId", message.getData().getOrderNo());
        return pushBody.toString();
    }

    /**
     * 验证通知合法
     *
     * @param wechatChannelsShopNotify 消息处理类
     * @param timestamp                时间戳（秒）
     * @param nonce                    随机数
     * @param signature                url签名
     */
    private void verifyNotify(WechatChannelsShopNotify wechatChannelsShopNotify,
                              String timestamp, String nonce, String signature) {
        boolean verifyNotifyServer = wechatChannelsShopNotify.verifyNotifyServer(timestamp, nonce, signature);
        if (BooleanUtils.isFalse(verifyNotifyServer)) {
            this.throwVerifyException(wechatChannelsShopNotify, "验证消息推送服务器未通过");
        }
        boolean verifyNotifyMessage = wechatChannelsShopNotify.verifyNotifyMessage(timestamp, nonce);
        if (BooleanUtils.isFalse(verifyNotifyMessage)) {
            this.throwVerifyException(wechatChannelsShopNotify, "验证消息推送未通过");
        }
    }

    /**
     * 验证消息匹配
     *
     * @param wechatChannelsShopNotify 消息处理类
     * @param message                  消息内容
     * @return 执行结果
     */
    private boolean verifyMessage(WechatChannelsShopNotify wechatChannelsShopNotify, WechatChannelsShopRefundNotifyDTO message) {
        boolean verifyMsgAppid = wechatChannelsShopNotify.verifyMsgAppid();
        if (BooleanUtils.isFalse(verifyMsgAppid)) {
            this.throwVerifyException(wechatChannelsShopNotify, "验证消息的appid未通过");
        }
        if (StringUtils.isBlank(message.getEvent())) {
            this.throwVerifyException(wechatChannelsShopNotify, "消息事件不存在");
        }
        return EVENT_SCENARIO_MAP.containsKey(message.getEvent());
    }

    /**
     * 抛出消息验证异常
     *
     * @param wechatChannelsShopNotify 消息处理类
     * @param message                  异常信息
     */
    private void throwVerifyException(WechatChannelsShopNotify wechatChannelsShopNotify, String message) {
        log.info("微信视频号小店消息通知 {}", wechatChannelsShopNotify);
        throw BizException.of(message);
    }

}
