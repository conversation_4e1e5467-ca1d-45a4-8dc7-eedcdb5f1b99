package com.xtc.marketing.adapterservice.notify.converter;

import com.xtc.marketing.adapterservice.notify.dto.WarehouseInboundNotifyCargoDTO;
import com.xtc.marketing.adapterservice.notify.dto.WarehouseInboundNotifyDTO;
import com.xtc.marketing.adapterservice.notify.dto.WarehouseOutboundNotifyCargoDTO;
import com.xtc.marketing.adapterservice.notify.dto.WarehouseOutboundNotifyDTO;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.SfInboundNotifyXmlDTO;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.SfOutboundNotifyXmlDTO;
import com.xtc.marketing.adapterservice.util.DateUtil;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(
        componentModel = "spring",
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface SfWarehouseNotifyConverter {

    /**
     * 转换统一仓库入库通知数据
     *
     * @param source 顺丰仓库入库通知数据
     * @return 统一仓库入库通知数据
     */
    @Mapping(target = "warehouseCode", source = "warehouseCode")
    @Mapping(target = "orderId", source = "orderId")
    @Mapping(target = "receiptId", source = "receiptId")
    @Mapping(target = "orderType", source = "orderType")
    @Mapping(target = "receiptOrderType", source = "receiptOrderType")
    @Mapping(target = "orderStatus", expression = "java(source.getOrderStatusText())")
    @Mapping(target = "cargos", source = "items")
    WarehouseInboundNotifyDTO toWarehouseInboundNotifyDTO(SfInboundNotifyXmlDTO source);

    /**
     * 转换统一仓库入库货物数据
     *
     * @param source 顺丰仓库入库货物数据
     * @return 统一仓库入库货物数据
     */
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "skuName", source = "skuName")
    @Mapping(target = "receiptTime", source = "receiptTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "planQuantity", source = "planQuantity")
    @Mapping(target = "actualQuantity", source = "actualQuantity")
    @Mapping(target = "rejectionQuantity", source = "rejectionQuantity")
    @Mapping(target = "barcodes", source = "serialNumbers.serialNumber")
    WarehouseInboundNotifyCargoDTO toWarehouseInboundNotifyCargoDTO(SfInboundNotifyXmlDTO.Item source);

    /**
     * 转换统一仓库出库通知数据
     *
     * @param source 顺丰仓库出库通知数据
     * @return 统一仓库出库通知数据
     */
    @Mapping(target = "warehouseCode", source = "warehouseCode")
    @Mapping(target = "orderId", source = "orderId")
    @Mapping(target = "shipmentId", source = "shipmentId")
    @Mapping(target = "waybillNo", source = "waybillNo")
    @Mapping(target = "orderType", source = "orderType")
    @Mapping(target = "shipmentOrderType", source = "shipmentOrderType")
    @Mapping(target = "orderStatus", expression = "java(source.getOrderStatusText())")
    @Mapping(target = "shippingTime", source = "actualShipTime", qualifiedByName = "toLocalDateTime")
    WarehouseOutboundNotifyDTO toWarehouseOutboundNotifyDTO(SfOutboundNotifyXmlDTO source);

    /**
     * 转换统一仓库出库货物
     *
     * @param source 顺丰仓库出库通知数据
     * @return 统一仓库出库货物
     */
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "quantity", source = "actualQuantity")
    @Mapping(target = "inventoryStatus", source = "inventoryStatus")
    @Mapping(target = "barcodes", source = "serialNumbers.serialNumber")
    WarehouseOutboundNotifyCargoDTO toWarehouseOutboundNotifyCargo(SfOutboundNotifyXmlDTO.ContainerItem source);

    /**
     * 转换统一仓库出库货物数据
     *
     * @param source 顺丰仓库出库货物数据
     */
    @AfterMapping
    default void toWarehouseOutboundNotifyCargoDTO(@MappingTarget WarehouseOutboundNotifyDTO target, SfOutboundNotifyXmlDTO source) {
        if (target == null || source == null || source.getContainers() == null) {
            return;
        }
        // 转换仓库出库货物数据
        List<WarehouseOutboundNotifyCargoDTO> cargos = source.getContainers().stream()
                .map(SfOutboundNotifyXmlDTO.Container::getContainerItems)
                .flatMap(java.util.Collection::stream)
                .map(this::toWarehouseOutboundNotifyCargo)
                .collect(Collectors.toList());
        target.setCargos(cargos);
    }

    /**
     * 转换 LocalDateTime
     *
     * @param str 时间字符串，格式：yyyy-MM-dd HH:mm:ss
     * @return LocalDateTime
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(String str) {
        return DateUtil.toLocalDateTime(str);
    }

}
