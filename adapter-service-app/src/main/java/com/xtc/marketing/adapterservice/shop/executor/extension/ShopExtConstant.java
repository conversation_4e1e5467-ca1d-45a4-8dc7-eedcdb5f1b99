package com.xtc.marketing.adapterservice.shop.executor.extension;

/**
 * 店铺扩展点常量
 */
public class ShopExtConstant {

    private ShopExtConstant() {
    }

    /**
     * 用例：店铺
     */
    public static final String USE_CASE = "shop";

    /**
     * 场景：官方商城、会员商城
     */
    public static final String SCENARIO_XTC = "XTC";
    /**
     * 场景：内部购机、内销测试机
     */
    public static final String SCENARIO_INTERNAL_SHOP = "INTERNAL_SHOP";
    /**
     * 场景：天猫
     */
    public static final String SCENARIO_TMALL = "TMALL";
    /**
     * 场景：京东
     */
    public static final String SCENARIO_JD = "JD";
    /**
     * 场景：抖音
     */
    public static final String SCENARIO_TIKTOK = "TIKTOK";
    /**
     * 场景：抖音代发
     */
    public static final String SCENARIO_TIKTOK_DISTR = "TIKTOK_DISTR";
    /**
     * 场景：拼多多
     */
    public static final String SCENARIO_PDD = "PDD";
    /**
     * 场景：拼多多代发
     */
    public static final String SCENARIO_PDD_DISTR = "PDD_DISTR";
    /**
     * 场景：微信视频号
     */
    public static final String SCENARIO_WECHAT_CHANNELS_SHOP = "WECHAT_CHANNELS_SHOP";
    /**
     * 场景：快手
     */
    public static final String SCENARIO_KUAISHOU = "KUAISHOU";
    /**
     * 场景：快手代发
     */
    public static final String SCENARIO_KUAISHOU_DISTR = "KUAISHOU_DISTR";
    /**
     * 场景：小红书
     */
    public static final String SCENARIO_XIAOHONGSHU = "XIAOHONGSHU";

}
