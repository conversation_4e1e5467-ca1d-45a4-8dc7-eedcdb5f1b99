package com.xtc.marketing.adapterservice.logistics.executor.extension;

/**
 * 物流扩展点常量
 */
public class LogisticsExtConstant {

    private LogisticsExtConstant() {
    }

    /**
     * 用例：物流
     */
    public static final String USE_CASE = "logistics";

    /**
     * 场景：顺丰
     */
    public static final String SCENARIO_SF = "SF";
    /**
     * 场景：圆通
     */
    public static final String SCENARIO_YTO = "YTO";
    /**
     * 场景：EMS
     */
    public static final String SCENARIO_EMS = "EMS";
    /**
     * 场景：京东平台物流
     */
    public static final String SCENARIO_JD = "JD";
    /**
     * 场景：极兔快递
     */
    public static final String SCENARIO_JT = "JT";
    /**
     * 场景：京东快递
     */
    public static final String SCENARIO_JD_EXPRESS = "JD_EXPRESS";
    /**
     * 场景：申通快递
     */
    public static final String SCENARIO_STO = "STO";

}
