package com.xtc.marketing.adapterservice.notify.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.adapterservice.notify.converter.SfWarehouseNotifyConverter;
import com.xtc.marketing.adapterservice.notify.dataobject.ReceiveLogDO;
import com.xtc.marketing.adapterservice.notify.dto.command.NotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.enums.NotifyEnum;
import com.xtc.marketing.adapterservice.rpc.sf.SfWarehouseXmlRpc;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.SfInboundNotifyXmlDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

/**
 * 通知扩展点 - 顺丰仓库入库
 */
@Slf4j
@RequiredArgsConstructor
@Extension(bizId = NotifyExtConstant.BIZ_ID_SF,
        useCase = NotifyExtConstant.USE_CASE_WAREHOUSE, scenario = NotifyExtConstant.SCENARIO_INBOUND)
public class SfWarehouseInboundNotifyExt implements NotifyExtPt {

    private final SfWarehouseXmlRpc sfWarehouseXmlRpc;
    private final SfWarehouseNotifyConverter sfWarehouseNotifyConverter;

    @Override
    public ReceiveLogDO createReceiveLog(NotifyEnum notify, NotifyReceiveCmd cmd) {
        SfInboundNotifyXmlDTO notifyDTO = sfWarehouseXmlRpc.convertToNotifyBean(cmd.getData(), SfInboundNotifyXmlDTO.class);
        return ReceiveLogDO.builder()
                .dataId(notifyDTO.getOrderId())
                .rawData(cmd.getData())
                .responseStr(notifyDTO.getNotifyResponse())
                .build();
    }

    @Override
    public ResponseEntity<String> notifyResponse(String responseStr) {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_XML).body(responseStr);
    }

    @Override
    public Object convertToNotifyBean(String data) {
        SfInboundNotifyXmlDTO notifyDTO = sfWarehouseXmlRpc.convertToNotifyBean(data, SfInboundNotifyXmlDTO.class);
        return sfWarehouseNotifyConverter.toWarehouseInboundNotifyDTO(notifyDTO);
    }

}
