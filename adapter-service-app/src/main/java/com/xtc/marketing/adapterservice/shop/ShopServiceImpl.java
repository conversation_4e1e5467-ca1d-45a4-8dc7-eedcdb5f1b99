package com.xtc.marketing.adapterservice.shop;

import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.dto.query.*;
import com.xtc.marketing.adapterservice.shop.enums.ShopTypeEnum;
import com.xtc.marketing.adapterservice.shop.executor.command.RefreshTokenCmdExe;
import com.xtc.marketing.adapterservice.shop.executor.extension.ShopExtConstant;
import com.xtc.marketing.adapterservice.shop.executor.extension.ShopExtPt;
import com.xtc.marketing.adapterservice.shop.executor.query.ShopGetQryExe;
import com.xtc.marketing.adapterservice.util.BeanCopier;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;

@RequiredArgsConstructor
@Service
public class ShopServiceImpl implements ShopService {

    private final ShopGetQryExe shopGetQryExe;
    private final ExtensionExecutor extensionExecutor;
    private final RefreshTokenCmdExe refreshTokenCmdExe;

    @Override
    public ShopAccessTokenDTO getAccessToken(String shopCode) {
        ShopDO shop = shopGetQryExe.execute(shopCode);
        if (shop.getShopType() == ShopTypeEnum.AGENT) {
            // 代理店铺通过 shopId 查询关联的自营店铺
            shop = shopGetQryExe.execute(shop.getShopId());
        }
        return BeanCopier.copy(shop, ShopAccessTokenDTO::new);
    }

    @Override
    public void refreshToken() {
        refreshTokenCmdExe.execute();
    }

    @Override
    public PageResponse<OrderDTO> pageOrders(OrderPageQry qry) {
        ShopDO shop = shopGetQryExe.execute(qry.getShopCode());
        PageResponse<OrderDTO> response = extensionExecute(shop, executor -> executor.pageOrders(shop, qry));
        response.getData().forEach(order -> {
            order.setPlatformCode(shop.getPlatformCode());
            order.setPlatformName(shop.getPlatformName());
            order.setShopCode(StringUtils.defaultIfBlank(order.getShopCode(), shop.getShopCode()));
        });
        return response;
    }

    @Override
    public OrderDTO getOrder(OrderGetQry qry) {
        ShopDO shop = shopGetQryExe.execute(qry.getShopCode());
        OrderDTO orderDTO = extensionExecute(shop, executor -> executor.getOrder(shop, qry));
        Optional.ofNullable(orderDTO).ifPresent(order -> {
            order.setPlatformCode(shop.getPlatformCode());
            order.setPlatformName(shop.getPlatformName());
            order.setShopCode(StringUtils.defaultIfBlank(order.getShopCode(), shop.getShopCode()));
        });
        return orderDTO;
    }

    @Override
    public boolean orderShipping(OrderShippingCmd cmd) {
        ShopDO shop = shopGetQryExe.execute(cmd.getShopCode());
        return extensionExecute(shop, executor -> executor.orderShipping(shop, cmd));
    }

    @Override
    public boolean orderShippingCancel(OrderShippingCancelCmd cmd) {
        ShopDO shop = shopGetQryExe.execute(cmd.getShopCode());
        return extensionExecute(shop, executor -> executor.orderShippingCancel(shop, cmd));
    }

    @Override
    public boolean orderDummyShipping(OrderDummyShippingCmd cmd) {
        ShopDO shop = shopGetQryExe.execute(cmd.getShopCode());
        return extensionExecute(shop, executor -> executor.orderDummyShipping(shop, cmd));
    }

    @Override
    public boolean orderRemark(OrderRemarkCmd cmd) {
        ShopDO shop = shopGetQryExe.execute(cmd.getShopCode());
        return extensionExecute(shop, executor -> executor.orderRemark(shop, cmd));
    }

    @Override
    public OrderDecryptDTO orderDecrypt(OrderDecryptCmd cmd) {
        ShopDO shop = shopGetQryExe.execute(cmd.getShopCode());
        return extensionExecute(shop, executor -> executor.orderDecrypt(shop, cmd));
    }

    @Override
    public PageResponse<InvoiceApplyDTO> pageInvoiceApply(InvoiceApplyPageQry qry) {
        ShopDO shop = shopGetQryExe.execute(qry.getShopCode());
        return extensionExecute(shop, executor -> executor.pageInvoiceApply(shop, qry));
    }

    @Override
    public InvoiceApplyDTO getInvoiceApply(InvoiceApplyGetQry qry) {
        ShopDO shop = shopGetQryExe.execute(qry.getShopCode());
        return extensionExecute(shop, executor -> executor.getInvoiceApply(shop, qry));
    }

    @Override
    public boolean uploadInvoiceFile(OrderUploadInvoiceCmd cmd, MultipartFile invoiceFile) {
        ShopDO shop = shopGetQryExe.execute(cmd.getShopCode());
        return extensionExecute(shop, executor -> executor.uploadInvoiceFile(shop, cmd, invoiceFile));
    }

    @Override
    public boolean uploadInvoiceBase64(InvoiceUploadCmd cmd) {
        ShopDO shop = shopGetQryExe.execute(cmd.getShopCode());
        return extensionExecute(shop, executor -> executor.uploadInvoiceBase64(shop, cmd));
    }

    @Override
    public InvoiceAmountDTO getInvoiceAmount(InvoiceAmountGetQry qry) {
        ShopDO shop = shopGetQryExe.execute(qry.getShopCode());
        return extensionExecute(shop, executor -> executor.getInvoiceAmount(shop, qry.getOrderNo()));
    }

    @Override
    public PageResponse<CommentDTO> pageComments(CommentPageQry qry) {
        ShopDO shop = shopGetQryExe.execute(qry.getShopCode());
        return extensionExecute(shop, executor -> executor.pageComments(shop, qry));
    }

    @Override
    public PageResponse<RefundDTO> pageRefunds(RefundPageQry qry) {
        if (ObjectUtils.allNull(qry.getUpdateTimeStart(), qry.getUpdateTimeEnd(), qry.getOrderNo())) {
            throw BizException.of("更新时间范围和订单号必须填一项");
        }
        ShopDO shop = shopGetQryExe.execute(qry.getShopCode());
        PageResponse<RefundDTO> response = extensionExecute(shop, executor -> executor.pageRefunds(shop, qry));
        response.getData().forEach(refund -> {
            refund.setPlatformCode(shop.getPlatformCode());
            refund.setPlatformName(shop.getPlatformName());
        });
        return response;
    }

    @Override
    public RefundDTO getRefund(RefundGetQry qry) {
        ShopDO shop = shopGetQryExe.execute(qry.getShopCode());
        RefundDTO refundDTO = extensionExecute(shop, executor -> executor.getRefund(shop, qry));
        Optional.ofNullable(refundDTO).ifPresent(refund -> {
            refund.setPlatformCode(shop.getPlatformCode());
            refund.setPlatformName(shop.getPlatformName());
        });
        return refundDTO;
    }

    @Override
    public ShopLogisticsOrderDTO createLogisticsOrder(ShopLogisticsOrderCreateCmd cmd) {
        ShopDO shop = shopGetQryExe.execute(cmd.getShopCode());
        return extensionExecute(shop, executor -> executor.createLogisticsOrder(shop, cmd));
    }

    @Override
    public boolean cancelLogisticsOrder(ShopLogisticsOrderCancelCmd cmd) {
        ShopDO shop = shopGetQryExe.execute(cmd.getShopCode());
        return extensionExecute(shop, executor -> executor.cancelLogisticsOrder(shop, cmd));
    }

    @Override
    public void refundGoodsToWarehouse(RefundGoodsToWarehouseCmd cmd) {
        ShopDO shop = shopGetQryExe.execute(cmd.getShopCode());
        extensionExecuteVoid(shop, executor -> executor.refundGoodsToWarehouse(shop, cmd));
    }

    @Override
    public void barcodeUpload(BarcodeUploadCmd cmd) {
        ShopDO shop = shopGetQryExe.execute(cmd.getShopCode());
        extensionExecuteVoid(shop, executor -> executor.barcodeUpload(shop, cmd));
    }

    /**
     * 执行扩展点
     *
     * @param shop     店铺
     * @param function 扩展点方法
     * @param <R>      返回值类型
     * @return 返回值
     */
    private <R> R extensionExecute(ShopDO shop, Function<ShopExtPt, R> function) {
        BizScenario bizScenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID,
                ShopExtConstant.USE_CASE, shop.getPlatformCode());
        return extensionExecutor.execute(ShopExtPt.class, bizScenario, function);
    }

    /**
     * 执行扩展点
     *
     * @param shop     店铺
     * @param consumer 扩展点方法
     */
    private void extensionExecuteVoid(ShopDO shop, Consumer<ShopExtPt> consumer) {
        BizScenario bizScenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID,
                ShopExtConstant.USE_CASE, shop.getPlatformCode());
        extensionExecutor.executeVoid(ShopExtPt.class, bizScenario, consumer);
    }

}
