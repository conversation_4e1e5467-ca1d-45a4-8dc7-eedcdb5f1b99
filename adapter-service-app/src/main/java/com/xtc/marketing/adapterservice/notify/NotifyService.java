package com.xtc.marketing.adapterservice.notify;

import com.xtc.marketing.adapterservice.notify.dto.command.NotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.enums.NotifyEnum;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletRequest;

public interface NotifyService {

    /**
     * 推送消息
     *
     * @param shardIndex 分片索引
     */
    void notifyPush(int shardIndex);

    /**
     * 接收通知数据
     *
     * @param notify 通知
     * @param cmd   数据
     * @return 响应结果
     */
    ResponseEntity<String> receiveData(NotifyEnum notify, NotifyReceiveCmd cmd);

}
