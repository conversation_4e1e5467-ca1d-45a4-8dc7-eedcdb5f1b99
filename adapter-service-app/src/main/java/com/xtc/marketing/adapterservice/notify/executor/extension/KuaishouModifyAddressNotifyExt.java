package com.xtc.marketing.adapterservice.notify.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.google.common.collect.ImmutableMap;
import com.kuaishou.merchant.open.api.domain.order.OrderDetail;
import com.xtc.marketing.adapterservice.constant.SystemConstant;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.notify.converter.KuaishouNotifyConverter;
import com.xtc.marketing.adapterservice.notify.dataobject.ReceiveLogDO;
import com.xtc.marketing.adapterservice.notify.dto.command.NotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.enums.NotifyEnum;
import com.xtc.marketing.adapterservice.rpc.kuaishou.KuaishouRpc;
import com.xtc.marketing.adapterservice.rpc.kuaishou.kuaishoudto.KuaishouMessageDTO;
import com.xtc.marketing.adapterservice.rpc.kuaishou.kuaishoudto.KuaishouModifyAddressNotifyDTO;
import com.xtc.marketing.adapterservice.rpc.oms.OmsRpc;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.command.OmsModifyAddressNotifyCmd;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.command.SyncTradeCmd;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.executor.query.ShopGetQryExe;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.Map;

/**
 * 通知扩展点 - 快手修改地址
 * <br><a href="https://open.kwaixiaodian.com/zone/new/docs/msg?name=kwaishop_order_addressUpdateAudit&version=1">官方文档</a>
 */
@Slf4j
@RequiredArgsConstructor
@Extension(bizId = NotifyExtConstant.BIZ_ID_KUAISHOU,
        useCase = NotifyExtConstant.USE_CASE_SHOP, scenario = NotifyExtConstant.SCENARIO_MODIFY_ADDRESS)
public class KuaishouModifyAddressNotifyExt implements NotifyExtPt {

    private final ShopGetQryExe shopGetQryExe;
    private final OmsRpc omsRpc;
    private final KuaishouRpc kuaishouRpc;
    private final KuaishouNotifyConverter kuaishouNotifyConverter;

    /**
     * 订单地址变更消息
     */
    private static final String ORDER_ADDRESSCHANGE = "kwaishop_order_addressChange";
    /**
     * 买家修改订单地址消息
     */
    private static final String ORDER_ADDRESSUPDATEAUDIT = "kwaishop_order_addressUpdateAudit";
    /**
     * 店铺与OMS平台ID的对应关系
     */
    private static final Map<String, String> OMS_PLATFORM_MAP = ImmutableMap.of(
            "ks704813343878184375", "2025",
            "ks654429323735557328", "2026"
    );

    @Override
    public ReceiveLogDO createReceiveLog(NotifyEnum notify, NotifyReceiveCmd cmd) {
        // 解析消息并验签
        KuaishouMessageDTO messageDto;
        try {
            messageDto = kuaishouRpc.parseMessage(cmd.getData());
        } catch (Exception e) {
            log.warn("快手通知验签、解密或解析失败: {} {}", cmd.getData(), e.getMessage(), e);
            return ReceiveLogDO.builder().responseStr(this.responseSignCheckFailure()).build();
        }
        // 校验消息基本信息
        log.info("快手地址修改通知，解析成功 {}", messageDto);
        if (StringUtils.isBlank(messageDto.getInfo()) || StringUtils.isBlank(messageDto.getAppKey())) {
            return ReceiveLogDO.builder().responseStr(this.responseSuccess()).build();
        }
        // 解析业务数据
        String rawData = GsonUtil.objectToJson(messageDto);
        KuaishouModifyAddressNotifyDTO notifyData = GsonUtil.jsonToBean(messageDto.getInfo(), KuaishouModifyAddressNotifyDTO.class);
        if (notifyData == null || notifyData.getOid() == null) {
            return ReceiveLogDO.builder().rawData(rawData).responseStr(this.responseSuccess()).build();
        }
        // 获取店铺信息
        ShopDO shop = shopGetQryExe.getByAppKey(messageDto.getAppKey());
        String orderId = notifyData.getOrderId();
        // 订单地址变更消息
        if (ORDER_ADDRESSCHANGE.equals(messageDto.getEvent())) {
            return handleAddressChangeEvent(messageDto, orderId, rawData);
        }
        // 买家修改订单地址消息
        if (ORDER_ADDRESSUPDATEAUDIT.equals(messageDto.getEvent())) {
            return handleAddressAuditEvent(shop, notifyData, orderId, rawData);
        }
        return ReceiveLogDO.builder()
                .dataId(orderId)
                .rawData(rawData)
                .responseStr(this.responseSuccess())
                .build();
    }

    @Override
    public ResponseEntity<String> notifyResponse(String responseStr) {
        return ResponseEntity.ok().body(responseStr);
    }

    @Override
    public Object convertToNotifyBean(String data) {
        return data;
    }

    /**
     * 处理地址变更消息
     *
     * @param messageDto 消息
     * @param orderId    订单号
     * @param rawData    原始数据
     * @return 记录
     */
    private ReceiveLogDO handleAddressChangeEvent(KuaishouMessageDTO messageDto, String orderId, String rawData) {
        String platformId = OMS_PLATFORM_MAP.get(messageDto.getAppKey());
        SyncTradeCmd syncTradeCmd = SyncTradeCmd.builder()
                .processTrade(true)
                .event(SystemConstant.SYSTEM_NAME)
                .platformId(platformId)
                .tradeIds(Collections.singletonList(orderId))
                .build();
        omsRpc.syncTrade(syncTradeCmd);
        return ReceiveLogDO.builder()
                .dataId(orderId)
                .rawData(rawData)
                .responseStr(this.responseSuccess())
                .build();
    }

    /**
     * 处理订单地址审核消息
     *
     * @param shop       店铺
     * @param notifyData 地址
     * @param orderId    订单号
     * @param rawData    原始数据
     */
    private ReceiveLogDO handleAddressAuditEvent(ShopDO shop, KuaishouModifyAddressNotifyDTO notifyData,
                                                 String orderId, String rawData) {
        Integer auditStatus = notifyData.getAuditStatus();
        // 非审核中或已通过状态，直接返回成功
        if (auditStatus == null || (auditStatus != 10 && auditStatus != 20)) {
            log.info("订单地址审核消息，无需处理 {} 审核状态 [{}]", orderId, auditStatus);
            return ReceiveLogDO.builder()
                    .dataId(orderId)
                    .rawData(rawData)
                    .responseStr(this.responseSuccess())
                    .build();
        }
        // 标记审核拒绝
        boolean reject = false;
        try {
            OrderDetail orderDetail = kuaishouRpc.getOrder(shop, orderId);
            if (orderDetail == null || orderDetail.getAddressUpdateAuditInfo() == null) {
                throw BizException.of("获取订单详情失败");
            }
            // 通知数据转换并推送OMS
            OmsModifyAddressNotifyCmd omsCmd = kuaishouNotifyConverter.toOmsModifyAddressNotifyCmd(orderDetail);
            omsRpc.notifyModifyAddress(omsCmd);
            // 审核中状态自动同意
            kuaishouRpc.approveAddressAudit(shop, orderId);
            log.info("订单地址审核消息，审核同意 {}", orderId);
        } catch (Exception e) {
            log.warn("订单地址审核消息，审核同意失败 {} {}", orderId, e.getMessage(), e);
            reject = true;
        }
        // 审核拒绝
        if (reject) {
            try {
                kuaishouRpc.rejectAddressAudit(shop, orderId);
                log.info("订单地址审核消息，审核拒绝 {}", orderId);
            } catch (Exception e) {
                log.warn("订单地址审核消息，审核拒绝失败 {} {}", orderId, e.getMessage(), e);
            }
        }
        return ReceiveLogDO.builder()
                .dataId(orderId)
                .rawData(rawData)
                .responseStr(this.responseSuccess())
                .build();
    }

    /**
     * 返回快手通知的成功响应字符串。
     *
     * @return 成功响应JSON字符串
     */
    private String responseSuccess() {
        return buildResponse(1, "");
    }

    /**
     * 返回快手通知的验签失败响应字符串。
     *
     * @return 验签失败响应JSON字符串
     */
    private String responseSignCheckFailure() {
        return buildResponse(0, "验签失败");
    }

    /**
     * 构建响应JSON字符串。
     *
     * @param result   响应结果代码
     * @param errorMsg 错误消息
     * @return JSON响应字符串
     */
    private String buildResponse(int result, String errorMsg) {
        return String.format("{\"result\": %d, \"error_msg\": \"%s\"}", result, errorMsg);
    }

}
