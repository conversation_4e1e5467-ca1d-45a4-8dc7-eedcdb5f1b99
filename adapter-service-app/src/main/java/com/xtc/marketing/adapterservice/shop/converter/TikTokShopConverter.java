package com.xtc.marketing.adapterservice.shop.converter;

import com.doudian.open.api.afterSale_Detail.data.AfterSaleDetailData;
import com.doudian.open.api.afterSale_Detail.data.AfterSaleShopRemarksItem;
import com.doudian.open.api.afterSale_Detail.data.ProcessInfo;
import com.doudian.open.api.afterSale_Detail.data.SkuOrderInfosItem;
import com.doudian.open.api.afterSale_List.data.ItemsItem;
import com.doudian.open.api.afterSale_List.data.OrderInfo;
import com.doudian.open.api.afterSale_List.data.RelatedOrderInfoItem;
import com.doudian.open.api.afterSale_List.data.SkuSpecItem;
import com.doudian.open.api.logistics_newCreateOrder.param.LogisticsNewCreateOrderParam;
import com.doudian.open.api.order_batchDecrypt.data.OrderBatchDecryptData;
import com.doudian.open.api.order_invoiceList.data.InvoiceListItem;
import com.doudian.open.api.order_logisticsAdd.param.OrderLogisticsAddParam;
import com.doudian.open.api.order_logisticsAddMultiPack.param.OrderLogisticsAddMultiPackParam;
import com.doudian.open.api.order_logisticsAddMultiPack.param.PackListItem;
import com.doudian.open.api.order_logisticsAddMultiPack.param.ShippedOrderInfoItem;
import com.doudian.open.api.order_orderDetail.data.ShopOrderDetail;
import com.doudian.open.api.order_searchList.data.ShopOrderListItem;
import com.doudian.open.api.order_searchList.data.SkuOrderListItem;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.xtc.marketing.adapterservice.rpc.tiktok.tiktokdto.enums.TikTokLogisticsCompany;
import com.xtc.marketing.adapterservice.shop.constant.ShopOrderConstant;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.OrderShippingCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsCargoCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsOrderCreateCmd;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTitleTypeEnum;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Mapper(
        componentModel = "spring",
        uses = {BaseShopConverter.class},
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface TikTokShopConverter {

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "orderId")
    @Mapping(target = "orderType", source = "orderType")
    @Mapping(target = "orderTypeDesc", source = "orderTypeDesc")
    @Mapping(target = "orderState", source = "mainStatus")
    @Mapping(target = "orderStateDesc", source = "mainStatusDesc")
    @Mapping(target = "stepOrderState", source = "orderStatus")
    @Mapping(target = "stepOrderStateDesc", source = "orderStatusDesc")
    @Mapping(target = "riskState", source = "skuOrderList", qualifiedByName = "orderListRiskState")
    @Mapping(target = "sellerId", source = "shopId")
    @Mapping(target = "sellerName", source = "shopName")
    @Mapping(target = "sellerMemo", source = "sellerWords")
    @Mapping(target = "buyerId", source = "doudianOpenId")
    @Mapping(target = "buyerName", source = "maskPostReceiver")
    @Mapping(target = "buyerMemo", source = "buyerWords")
    @Mapping(target = "receiverName", source = "maskPostReceiver")
    @Mapping(target = "receiverMobile", source = "maskPostTel")
    @Mapping(target = "receiverProvince", source = "maskPostAddr.province.name")
    @Mapping(target = "receiverCity", source = "maskPostAddr.city.name")
    @Mapping(target = "receiverDistrict", source = "maskPostAddr.town.name")
    @Mapping(target = "receiverAddress", source = "maskPostAddr.detail")
    // 金额和时间
    @Mapping(target = "priceTotal", source = "orderAmount")
    // 付款金额需要加上平台优惠金额，属于店铺的实收
    @Mapping(target = "payment", expression = "java(sumAmount(source.getPayAmount(), source.getPromotionPlatformAmount()))")
    @Mapping(target = "shippingPayment", source = "postAmount")
    // 合计平台优惠金额
    @Mapping(target = "discount", expression = "java(sumAmount(source.getPromotionPayAmount(), source.getPromotionPlatformAmount(), source.getPromotionTalentAmount()))")
    @Mapping(target = "updateTime", source = "updateTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "orderTime", source = "createTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "payTime", source = "payTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "shippingTime", source = "shipTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "completedTime", source = "finishTime", qualifiedByName = "toLocalDateTime")
    // 子对象转换
    @Mapping(target = "items", source = "skuOrderList")
    @Mapping(target = "cipherTexts", expression = "java(java.util.Arrays.asList(source.getEncryptPostReceiver(), source.getEncryptPostTel(), source.getPostAddr().getEncryptDetail()))")
    OrderDTO toAdapterOrderDTO(ShopOrderListItem source);

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    @Mapping(target = "orderNo", source = "orderId")
    @Mapping(target = "orderType", source = "orderType")
    @Mapping(target = "orderTypeDesc", source = "orderTypeDesc")
    @Mapping(target = "orderState", source = "mainStatus")
    @Mapping(target = "orderStateDesc", source = "mainStatusDesc")
    @Mapping(target = "stepOrderState", source = "orderStatus")
    @Mapping(target = "stepOrderStateDesc", source = "orderStatusDesc")
    @Mapping(target = "riskState", source = "skuOrderList", qualifiedByName = "orderDetailRiskState")
    @Mapping(target = "sellerId", source = "shopId")
    @Mapping(target = "sellerName", source = "shopName")
    @Mapping(target = "sellerMemo", source = "sellerWords")
    @Mapping(target = "buyerId", source = "doudianOpenId")
    @Mapping(target = "buyerName", source = "maskPostReceiver")
    @Mapping(target = "buyerMemo", source = "buyerWords")
    @Mapping(target = "receiverName", source = "maskPostReceiver")
    @Mapping(target = "receiverMobile", source = "maskPostTel")
    @Mapping(target = "receiverProvince", source = "maskPostAddr.province.name")
    @Mapping(target = "receiverCity", source = "maskPostAddr.city.name")
    @Mapping(target = "receiverDistrict", source = "maskPostAddr.town.name")
    @Mapping(target = "receiverAddress", source = "maskPostAddr.detail")
    // 金额和时间
    @Mapping(target = "priceTotal", source = "orderAmount")
    // 付款金额需要加上平台优惠金额，属于店铺的实收
    @Mapping(target = "payment", expression = "java(sumAmount(source.getPayAmount(), source.getPromotionPlatformAmount()))")
    @Mapping(target = "shippingPayment", source = "postAmount")
    // 合计平台优惠金额
    @Mapping(target = "discount", expression = "java(sumAmount(source.getPromotionPayAmount(), source.getPromotionPlatformAmount(), source.getPromotionTalentAmount()))")
    @Mapping(target = "updateTime", source = "updateTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "orderTime", source = "createTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "payTime", source = "payTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "shippingTime", source = "shipTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "completedTime", source = "finishTime", qualifiedByName = "toLocalDateTime")
    // 子对象转换
    @Mapping(target = "items", source = "skuOrderList")
    // 按收件人、电话、地址排序，不可改变顺序
    @Mapping(target = "cipherTexts", expression = "java(java.util.Arrays.asList(source.getEncryptPostReceiver(), source.getEncryptPostTel(), source.getPostAddr().getEncryptDetail()))")
    OrderDTO toAdapterOrderDTO(ShopOrderDetail source);

    /**
     * 转换统一订单明细
     *
     * @param source 平台订单明细
     * @return 统一订单明细
     */
    @Mapping(target = "orderNo", source = "parentOrderId")
    @Mapping(target = "itemNo", source = "orderId")
    @Mapping(target = "itemType", source = "orderTypeDesc")
    @Mapping(target = "productId", source = "productId")
    @Mapping(target = "productName", source = "productName")
    @Mapping(target = "productErpCode", source = "outProductId")
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "skuName", source = "productName")
    @Mapping(target = "skuErpCode", source = "code")
    @Mapping(target = "num", source = "itemNum")
    @Mapping(target = "unitPrice", source = "goodsPrice")
    @Mapping(target = "priceTotal", source = "orderAmount")
    @Mapping(target = "discount", source = "promotionAmount")
    @Mapping(target = "payment", source = "payAmount")
    @Mapping(target = "authorId", source = "authorId")
    @Mapping(target = "authorName", source = "authorName")
    OrderItemDTO toAdapterOrderItemDTO(SkuOrderListItem source);

    /**
     * 转换统一订单明细
     *
     * @param source 平台订单明细
     * @return 统一订单明细
     */
    @Mapping(target = "orderNo", source = "parentOrderId")
    @Mapping(target = "itemNo", source = "orderId")
    @Mapping(target = "itemType", source = "orderTypeDesc")
    @Mapping(target = "itemState", source = "orderStatus")
    @Mapping(target = "refundState", source = "afterSaleInfo.afterSaleStatus")
    @Mapping(target = "productId", source = "productId")
    @Mapping(target = "productName", source = "productName")
    @Mapping(target = "productErpCode", source = "outProductId")
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "skuName", source = "productName")
    @Mapping(target = "skuErpCode", source = "code")
    @Mapping(target = "num", source = "itemNum")
    @Mapping(target = "unitPrice", source = "goodsPrice")
    @Mapping(target = "priceTotal", source = "orderAmount")
    @Mapping(target = "discount", source = "promotionAmount")
    @Mapping(target = "payment", source = "payAmount")
    @Mapping(target = "authorId", source = "authorId")
    @Mapping(target = "authorName", source = "authorName")
    OrderItemDTO toAdapterOrderDetailItemDTO(com.doudian.open.api.order_orderDetail.data.SkuOrderListItem source);

    /**
     * 转换统一发票申请数据
     *
     * @param source 平台发票申请数据
     * @return 统一发票申请数据
     */
    List<InvoiceApplyDTO> toAdapterInvoiceApplyDTO(List<InvoiceListItem> source);

    /**
     * 转换统一发票申请数据
     *
     * @param source 平台发票申请数据
     * @return 统一发票申请数据
     */
    @Mapping(target = "orderNo", source = "shopOrderId")
    @Mapping(target = "invoiceType", constant = "ELECTRONIC")
    @Mapping(target = "invoiceTitleType", source = "titleType", qualifiedByName = "toAdapterInvoiceTitleTypeEnum")
    @Mapping(target = "invoiceTitle", source = "title")
    @Mapping(target = "invoiceUrl", source = "tosUrl")
    @Mapping(target = "taxNo", source = "taxNo")
    @Mapping(target = "bankName", source = "bankName")
    @Mapping(target = "bankAccount", source = "bankNo")
    @Mapping(target = "companyMobile", source = "companyMobile")
    @Mapping(target = "companyAddress", source = "companyAddress")
    @Mapping(target = "invoiceAmount", source = "invoiceAmount")
    @Mapping(target = "sellerName", source = "source", qualifiedByName = "toSellerName")
    @Mapping(target = "invoiceMemo", source = "source.invoiceDetailExtra.governmentSubsidyInvoiceRemark")
    InvoiceApplyDTO toAdapterInvoiceApplyDTO(InvoiceListItem source);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    List<RefundDTO> toAdapterRefundDTO(List<ItemsItem> source);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "orderInfo.shopOrderId")
    @Mapping(target = "serviceNo", source = "aftersaleInfo.aftersaleId")
    @Mapping(target = "serviceType", source = "aftersaleInfo.aftersaleType")
    @Mapping(target = "serviceTypeDesc", source = "textPart.aftersaleTypeText")
    @Mapping(target = "serviceState", source = "aftersaleInfo.aftersaleStatus")
    @Mapping(target = "serviceStateDesc", source = "textPart.aftersaleStatusText")
    @Mapping(target = "refundState", source = "aftersaleInfo.refundStatus")
    @Mapping(target = "applyReason", source = "textPart.reasonText")
    @Mapping(target = "sellerId", source = "aftersaleInfo.storeId")
    @Mapping(target = "sellerName", source = "aftersaleInfo.storeName")
    @Mapping(target = "sellerMemo", source = "aftersaleInfo.remark")
    @Mapping(target = "returnExpressCompany", source = "aftersaleInfo.returnLogisticsCompanyName")
    @Mapping(target = "returnWaybillNo", source = "aftersaleInfo.returnLogisticsCode")
    // 金额和时间
    @Mapping(target = "refundApplyAmount", source = "aftersaleInfo.refundAmount")
    @Mapping(target = "refundAmount", source = "aftersaleInfo.refundAmount")
    @Mapping(target = "refundTime", source = "aftersaleInfo.aftersaleStatusToFinalTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "returnTime", source = "aftersaleInfo.aftersaleStatusToFinalTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "updateTime", source = "aftersaleInfo.updateTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "createTime", source = "aftersaleInfo.applyTime", qualifiedByName = "toLocalDateTime")
    RefundDTO toAdapterRefundDTO(ItemsItem source);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "orderInfo.shopOrderId")
    @Mapping(target = "serviceNo", source = "processInfo.afterSaleInfo.afterSaleId")
    @Mapping(target = "serviceType", source = "processInfo.afterSaleInfo.afterSaleType")
    @Mapping(target = "serviceTypeDesc", source = "processInfo.afterSaleInfo.afterSaleTypeText")
    @Mapping(target = "serviceState", source = "processInfo.afterSaleInfo.afterSaleStatus")
    @Mapping(target = "serviceStateDesc", source = "processInfo.afterSaleInfo.afterSaleStatusDesc")
    @Mapping(target = "refundState", source = "processInfo.afterSaleInfo.refundStatus")
    @Mapping(target = "applyReason", source = "processInfo.afterSaleInfo.reason")
    @Mapping(target = "sellerId", source = "processInfo.afterSaleInfo.storeId")
    @Mapping(target = "sellerName", source = "processInfo.afterSaleInfo.storeName")
    @Mapping(target = "returnExpressCompany", source = "processInfo.logisticsInfo._return.companyName")
    @Mapping(target = "returnWaybillNo", source = "processInfo.logisticsInfo._return.trackingNo")
    // 金额和时间
    @Mapping(target = "refundApplyAmount", source = "processInfo.afterSaleInfo.realRefundAmount")
    @Mapping(target = "refundAmount", source = "processInfo.afterSaleInfo.realRefundAmount")
    @Mapping(target = "refundTime", source = "processInfo.afterSaleInfo.refundTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "returnTime", source = "processInfo.afterSaleInfo.aftersaleStatusToFinalTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "updateTime", source = "processInfo.afterSaleInfo.updateTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "createTime", source = "processInfo.afterSaleInfo.applyTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "originData", expression = "java(com.xtc.marketing.adapterservice.util.GsonUtil.objectToJson(source))")
    RefundDTO toAdapterRefundDTO(AfterSaleDetailData source);

    /**
     * 转换统一解密数据
     *
     * @param source 平台解密数据
     * @return 统一解密数据
     */
    @Mapping(target = "orderNo", expression = "java(source.getDecryptInfos().get(0).getAuthId())")
    OrderDecryptDTO toAdapterOrderDecryptDTO(OrderBatchDecryptData source);

    /**
     * 转换平台发货参数
     *
     * @param cmd 发货参数
     * @return 平台发货参数
     */
    @Mapping(target = "orderId", source = "cmd.orderNo")
    @Mapping(target = "isRefundReject", constant = "true")
    @Mapping(target = "companyCode", source = "logisticsCompany.code")
    @Mapping(target = "logisticsCode", source = "cmd.waybillNo")
    OrderLogisticsAddParam toOrderLogisticsAddParam(OrderShippingCmd cmd, TikTokLogisticsCompany logisticsCompany);

    /**
     * 转换平台发货参数
     *
     * @param orderShippingCmd 发货参数
     * @return 平台发货参数
     */
    @Mapping(target = "orderId", source = "orderShippingCmd.orderNo")
    @Mapping(target = "requestId", expression = "java(java.util.UUID.randomUUID().toString().replace(\"-\", \"\"))")
    @Mapping(target = "isRefundReject", constant = "true")
    @Mapping(target = "packList", expression = "java(java.util.Collections.singletonList(toPackListItem(orderShippingCmd, logisticsCompany, skuList)))")
    OrderLogisticsAddMultiPackParam toOrderLogisticsAddMultiPackParam(
            OrderShippingCmd orderShippingCmd,
            TikTokLogisticsCompany logisticsCompany,
            List<com.doudian.open.api.order_orderDetail.data.SkuOrderListItem> skuList
    );

    /**
     * 转换平台发货参数
     *
     * @param orderShippingCmd 统一发货参数
     * @param logisticsCompany 物流公司
     * @return 平台发货参数
     */
    @Mapping(target = "logisticsCode", source = "orderShippingCmd.waybillNo")
    @Mapping(target = "companyCode", source = "logisticsCompany.code")
    @Mapping(target = "shippedOrderInfo", source = "skuList")
    PackListItem toPackListItem(OrderShippingCmd orderShippingCmd, TikTokLogisticsCompany logisticsCompany,
                                List<com.doudian.open.api.order_orderDetail.data.SkuOrderListItem> skuList);

    /**
     * 转换平台发货参数
     *
     * @param source 平台发货参数
     * @return 平台发货参数
     */
    @Mapping(target = "shippedOrderId", source = "orderId")
    @Mapping(target = "shippedNum", source = "itemNum")
    ShippedOrderInfoItem toShippedOrderInfoItem(com.doudian.open.api.order_orderDetail.data.SkuOrderListItem source);

    /**
     * 转换平台电子面单参数
     *
     * @param shopLogisticsOrderCreateCmd 统一电子面单参数
     * @param shop                        店铺
     * @param logisticsCompany            物流公司
     * @return 平台电子面单参数
     */
    @Mapping(target = "logisticsCode", source = "logisticsCompany.code")
    @Mapping(target = "senderInfo.contact.name", source = "shopLogisticsOrderCreateCmd.senderName")
    @Mapping(target = "senderInfo.contact.mobile", source = "shopLogisticsOrderCreateCmd.senderPhone")
    @Mapping(target = "senderInfo.address.countryCode", constant = "CHN")
    @Mapping(target = "senderInfo.address.provinceName", source = "shopLogisticsOrderCreateCmd.senderProvince")
    @Mapping(target = "senderInfo.address.cityName", source = "shopLogisticsOrderCreateCmd.senderCity")
    @Mapping(target = "senderInfo.address.districtName", expression = "java(\"东莞市\".equals(shopLogisticsOrderCreateCmd.getSenderCity()) ? \"东莞市\" : shopLogisticsOrderCreateCmd.getSenderDistrict())")
    @Mapping(target = "senderInfo.address.streetName", expression = "java(\"东莞市\".equals(shopLogisticsOrderCreateCmd.getSenderCity()) ? shopLogisticsOrderCreateCmd.getSenderDistrict() : shopLogisticsOrderCreateCmd.getSenderTown())")
    @Mapping(target = "senderInfo.address.detailAddress", source = "shopLogisticsOrderCreateCmd.senderAddress")
    @Mapping(target = "orderInfos", expression = "java(java.util.Collections.singletonList(toOrderInfosItem(shopLogisticsOrderCreateCmd, shop, logisticsCompany)))")
    LogisticsNewCreateOrderParam toLogisticsNewCreateOrderParam(
            ShopLogisticsOrderCreateCmd shopLogisticsOrderCreateCmd,
            ShopDO shop,
            TikTokLogisticsCompany logisticsCompany
    );

    /**
     * 转换平台电子面单参数
     *
     * @param shopLogisticsOrderCreateCmd 统一电子面单参数
     * @param shop                        店铺
     * @param logisticsCompany            物流公司
     * @return 平台电子面单参数
     */
    @Mapping(target = "productType", source = "logisticsCompany.type")
    @Mapping(target = "orderId", source = "shopLogisticsOrderCreateCmd.shopOrderNo")
    @Mapping(target = "receiverInfo.address.provinceName", source = "shopLogisticsOrderCreateCmd.receiverProvince")
    @Mapping(target = "receiverInfo.address.cityName", source = "shopLogisticsOrderCreateCmd.receiverCity")
    @Mapping(target = "receiverInfo.address.districtName", source = "shopLogisticsOrderCreateCmd.receiverDistrict")
    @Mapping(target = "receiverInfo.address.detailAddress", source = "shopLogisticsOrderCreateCmd.receiverAddress")
    @Mapping(target = "receiverInfo.address.countryCode", constant = "CHN")
    @Mapping(target = "receiverInfo.contact.name", source = "shopLogisticsOrderCreateCmd.receiverName")
    @Mapping(target = "receiverInfo.contact.mobile", source = "shopLogisticsOrderCreateCmd.receiverMobile")
    @Mapping(target = "items", source = "shopLogisticsOrderCreateCmd.cargos")
    com.doudian.open.api.logistics_newCreateOrder.param.OrderInfosItem toOrderInfosItem(
            ShopLogisticsOrderCreateCmd shopLogisticsOrderCreateCmd,
            ShopDO shop,
            TikTokLogisticsCompany logisticsCompany
    );

    /**
     * 转换平台电子面单参数
     *
     * @param cmd 统一电子面单参数
     * @return 平台电子面单参数
     */
    @Mapping(target = "itemName", source = "name")
    @Mapping(target = "itemCount", source = "quantity")
    com.doudian.open.api.logistics_newCreateOrder.param.ItemsItem toItemsItem(ShopLogisticsCargoCmd cmd);

    /**
     * 转换统一退款数据时，设置商品数据
     *
     * @param refund         统一退款数据
     * @param platformRefund 平台退款数据
     */
    @AfterMapping
    default void setRefund(@MappingTarget RefundDTO refund, ItemsItem platformRefund) {
        if (refund == null || platformRefund == null) {
            return;
        }

        // 取金额最大的订单明细
        Optional.ofNullable(platformRefund.getOrderInfo())
                .map(OrderInfo::getRelatedOrderInfo)
                .flatMap(list -> list.stream().max(Comparator.comparing(RelatedOrderInfoItem::getPrice)))
                .ifPresent(item -> {
                    refund.setOrderItemNo(item.getSkuOrderId());
                    refund.setUnitPrice(item.getPrice().intValue());
                    refund.setPayment(item.getPayAmount().intValue());
                    refund.setNum(item.getAftersaleItemNum().intValue());
                    refund.setProductId(item.getProductId().toString());
                    refund.setProductName(item.getProductName());
                    refund.setSkuId(item.getShopSkuCode());

                    Optional.ofNullable(item.getSkuSpec())
                            .map(specs -> specs.stream().collect(Collectors.toMap(SkuSpecItem::getName, SkuSpecItem::getValue)))
                            .map(Joiner.on(";").withKeyValueSeparator(":")::join)
                            .ifPresent(refund::setSkuName);
                });
    }

    /**
     * 转换统一退款数据时，设置商品数据
     *
     * @param refund         统一退款数据
     * @param platformRefund 平台退款数据
     */
    @AfterMapping
    default void setRefund(@MappingTarget RefundDTO refund, AfterSaleDetailData platformRefund) {
        if (refund == null || platformRefund == null) {
            return;
        }

        // 取金额最大的订单明细
        Optional.ofNullable(platformRefund.getOrderInfo())
                .map(com.doudian.open.api.afterSale_Detail.data.OrderInfo::getSkuOrderInfos)
                .flatMap(list -> list.stream().max(Comparator.comparing(SkuOrderInfosItem::getItemSumAmount)))
                .ifPresent(item -> {
                    refund.setOrderItemNo(item.getSkuOrderId().toString());
                    refund.setUnitPrice(item.getItemSumAmount().intValue());
                    refund.setPayment(item.getPayAmount().intValue());
                    refund.setNum(item.getAfterSaleItemCount().intValue());
                    refund.setProductId(item.getProductId().toString());
                    refund.setProductName(item.getProductName());
                    refund.setSkuId(item.getShopSkuCode());

                    Optional.ofNullable(item.getSkuSpec())
                            .map(specs -> specs.stream().collect(Collectors.toMap(com.doudian.open.api.afterSale_Detail.data.SkuSpecItem::getName,
                                    com.doudian.open.api.afterSale_Detail.data.SkuSpecItem::getValue)))
                            .map(Joiner.on(";").withKeyValueSeparator(":")::join)
                            .ifPresent(refund::setSkuName);
                });

        // 取最新的商户备注
        Optional.ofNullable(platformRefund.getProcessInfo())
                .map(ProcessInfo::getAfterSaleShopRemarks)
                .flatMap(list -> list.stream().max(Comparator.comparing(AfterSaleShopRemarksItem::getCreateTime)))
                .map(AfterSaleShopRemarksItem::getRemark)
                .ifPresent(refund::setSellerMemo);
    }

    /**
     * 转换统一解密数据时，设置对应的解密字段
     *
     * @param decrypt         统一解密数据
     * @param platformDecrypt 平台解密数据
     */
    @AfterMapping
    default void setDecrypt(@MappingTarget OrderDecryptDTO decrypt, OrderBatchDecryptData platformDecrypt) {
        if (platformDecrypt == null || platformDecrypt.getDecryptInfos() == null) {
            return;
        }
        // 初始化未知类型的解密集合
        decrypt.setDecryptTexts(Maps.newHashMapWithExpectedSize(platformDecrypt.getDecryptInfos().size()));
        // 根据类型判断解密字段
        platformDecrypt.getDecryptInfos().forEach(decryptInfo -> {
            String decryptText = decryptInfo.getDecryptText();
            // 解密字段为空时，放到未知类型的解密集合
            if (StringUtils.isBlank(decryptText)) {
                decrypt.getDecryptTexts().put(decryptInfo.getCipherText(), decryptInfo.getErrMsg());
                return;
            }
            // 加密类型 1、 地址加密类型 2、 姓名加密类型 3、 手机号加密类型 4、身份证类型 5、手机号加密类型(不会返回虚拟号)
            if (decryptInfo.getDataType() == 1) {
                decrypt.setAddress(decryptText);
            } else if (decryptInfo.getDataType() == 2) {
                decrypt.setName(decryptText);
            } else if (decryptInfo.getDataType() == 3 || decryptInfo.getDataType() == 5) {
                decrypt.setMobile(decryptText);
                Optional.ofNullable(decryptInfo.getExpireTime())
                        .filter(expireTime -> expireTime > 0)
                        .map(DateUtil::toLocalDateTime)
                        .ifPresent(decrypt::setMobileExpireTime);
            }
        });
    }

    /**
     * 设置默认达人信息
     *
     * @param order 订单
     */
    @AfterMapping
    default void setAuthor(@MappingTarget OrderDTO order) {
        if (order == null) {
            return;
        }
        boolean xtcShop = "17794271".equals(order.getSellerId());
        String authorId = xtcShop ? "84341552641" : "79714315521";
        String authorName = xtcShop ? "小天才官方旗舰店" : "步步高官方旗舰店";
        order.getItems().stream()
                .filter(item -> StringUtils.isBlank(item.getAuthorId()) || "0".equals(item.getAuthorId()))
                .forEach(item -> {
                    item.setAuthorId(authorId);
                    item.setAuthorName(authorName);
                });
    }

    /**
     * 转换统一订单数据时，处理国家补贴订单
     *
     * @param order  统一订单数据
     * @param source 平台订单数据
     */
    @AfterMapping
    default void setNationalSubsidy(@MappingTarget OrderDTO order, ShopOrderListItem source) {
        if (CollectionUtils.isEmpty(source.getSkuOrderList())) {
            return;
        }
        // 识别国家补贴订单
        boolean isNationalSubsidy = source.getSkuOrderList().stream()
                .anyMatch(sku -> sku.getSerialNoInfo() != null && sku.getSerialNoInfo().getShippingNeedCheckCodes() != null);
        if (isNationalSubsidy) {
            order.setOrderType(ShopOrderConstant.ORDER_TYPE_NATIONAL_SUBSIDY);
            order.setOrderTypeDesc(ShopOrderConstant.ORDER_TYPE_DESC_NATIONAL_SUBSIDY);
        }
    }

    /**
     * 转换统一订单数据时，处理国家补贴订单
     *
     * @param order  统一订单数据
     * @param source 平台订单数据
     */
    @AfterMapping
    default void setNationalSubsidy(@MappingTarget OrderDTO order, ShopOrderDetail source) {
        if (CollectionUtils.isEmpty(source.getSkuOrderList())) {
            return;
        }
        // 识别国家补贴订单
        boolean isNationalSubsidy = source.getSkuOrderList().stream()
                .anyMatch(sku -> sku.getSerialNoInfo() != null && sku.getSerialNoInfo().getShippingNeedCheckCodes() != null);
        if (isNationalSubsidy) {
            order.setOrderType(ShopOrderConstant.ORDER_TYPE_NATIONAL_SUBSIDY);
            order.setOrderTypeDesc(ShopOrderConstant.ORDER_TYPE_DESC_NATIONAL_SUBSIDY);
        }
    }

    /**
     * 转换统一风险状态
     *
     * @param skuOrderList 平台sku列表
     * @return 统一风险状态
     */
    @Named("orderListRiskState")
    default boolean orderListRiskState(List<SkuOrderListItem> skuOrderList) {
        // sku_order_tag_ui下返回风控标签，对应标签是key=risk_processing
        return CollectionUtils.emptyIfNull(skuOrderList).stream()
                .map(SkuOrderListItem::getSkuOrderTagUi)
                .flatMap(Collection::stream)
                .anyMatch(tag -> "risk_processing".equals(tag.getKey()));
    }

    /**
     * 转换统一风险状态
     *
     * @param skuOrderList 平台sku列表
     * @return 统一风险状态
     */
    @Named("orderDetailRiskState")
    default boolean orderDetailRiskState(List<com.doudian.open.api.order_orderDetail.data.SkuOrderListItem> skuOrderList) {
        // sku_order_tag_ui下返回风控标签，对应标签是key=risk_processing
        return CollectionUtils.emptyIfNull(skuOrderList).stream()
                .map(com.doudian.open.api.order_orderDetail.data.SkuOrderListItem::getSkuOrderTagUi)
                .flatMap(Collection::stream)
                .anyMatch(tag -> "risk_processing".equals(tag.getKey()));
    }

    /**
     * 汇总金额
     *
     * @param amountArr 金额集合
     * @return 总金额
     */
    @Named("sumAmount")
    default int sumAmount(Long... amountArr) {
        return Stream.of(amountArr)
                .filter(Objects::nonNull)
                .map(Long::intValue)
                .reduce(Integer::sum)
                .orElse(0);
    }

    /**
     * 转换发票抬头类型
     *
     * @param invoiceTitleType 发票抬头类型
     * @return 发票抬头类型枚举
     */
    @Named("toAdapterInvoiceTitleTypeEnum")
    default InvoiceTitleTypeEnum toAdapterInvoiceTitleTypeEnum(Integer invoiceTitleType) {
        // 默认值为【个人】，发票抬头类型：1=个人，2=企业
        if (Objects.equals(invoiceTitleType, 2)) {
            return InvoiceTitleTypeEnum.COMPANY;
        }
        return InvoiceTitleTypeEnum.PERSONAL;
    }

    /**
     * 时间转换
     *
     * @param epochSecond 秒
     * @return LocalDateTime
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(Long epochSecond) {
        if (epochSecond == null || epochSecond < 1) {
            return null;
        }
        return DateUtil.toLocalDateTime(epochSecond);
    }

    /**
     * 转换销售公司
     *
     * @param source 销售公司
     * @return 销售公司
     */
    @Named("toSellerName")
    default String toSellerName(InvoiceListItem source) {
        if (source.getInvoiceDetail() == null) {
            return null;
        }
        // {"6940576931626488973":{"GovSubsidyMultis":{"SupplierShopId":"*********","SupplierShopName":"南京谷澜信息科技有限公司"}}}
        return GsonUtil.getAsString(source.getInvoiceDetail(), source.getShopOrderId(), "GovSubsidyMultis", "SupplierShopName");
    }

}