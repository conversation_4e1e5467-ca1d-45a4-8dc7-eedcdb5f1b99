package com.xtc.marketing.adapterservice.shop.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.adapterservice.exception.BizErrorCode;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.internalshop.InternalShopRpc;
import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.InternalShopOrderDTO;
import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.InternalShopOrderItemDTO;
import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.InternalShopPageResponse;
import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.InternalShopRefundDTO;
import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.command.InternalShopShippingCmd;
import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.enums.InternalShopLogisticsCompany;
import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.enums.InternalShopOrderStatus;
import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.query.InternalShopPageQry;
import com.xtc.marketing.adapterservice.shop.converter.InternalShopConverter;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.dto.query.*;
import com.xtc.marketing.adapterservice.shop.util.SkuUtil;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = ShopExtConstant.USE_CASE, scenario = ShopExtConstant.SCENARIO_INTERNAL_SHOP)
public class InternalShopExt implements ShopExtPt {

    private final InternalShopRpc internalShopRpc;
    private final InternalShopConverter internalShopConverter;

    @Override
    public ShopDO getShopToken(ShopDO shop) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<OrderDTO> pageOrders(ShopDO shop, OrderPageQry qry) {
        InternalShopPageQry rpcQry = InternalShopPageQry.builder()
                .pageNo(qry.getPageIndex())
                .pageSize(qry.getPageSize())
                .updateTimeEnd(DateUtil.toEpochSecond(qry.getUpdateTimeEnd()))
                .updateTimeStart(DateUtil.toEpochSecond(qry.getUpdateTimeStart()))
                .build();
        InternalShopPageResponse<InternalShopOrderDTO> pageResponse = internalShopRpc.pageOrders(shop, rpcQry);

        List<OrderDTO> orders = pageResponse.getData().stream()
                .map(this::splitSkuIdsAndConvertToAdapterOrderDTO)
                .collect(Collectors.toList());
        return PageResponse.of(orders, pageResponse.getTotal(), pageResponse.getPageSize(), pageResponse.getPageNo());
    }

    @Override
    public OrderDTO getOrder(ShopDO shop, OrderGetQry qry) {
        InternalShopOrderDTO order = internalShopRpc.getOrder(shop, qry.getOrderNo());
        return Optional.ofNullable(order)
                .map(originOrder -> {
                    OrderDTO orderDTO = this.splitSkuIdsAndConvertToAdapterOrderDTO(originOrder);
                    orderDTO.setOriginOrderData(GsonUtil.objectToJson(originOrder));
                    return orderDTO;
                })
                .orElse(null);
    }

    @Override
    public boolean orderShipping(ShopDO shop, OrderShippingCmd cmd) {
        boolean hasShipping = this.checkOrderHasShipping(shop, cmd.getOrderNo());
        if (hasShipping) {
            return true;
        }
        InternalShopShippingCmd shippingCmd = InternalShopShippingCmd.builder()
                .orderNo(cmd.getOrderNo())
                .waybillNo(cmd.getWaybillNo())
                .logisticsCompany(InternalShopLogisticsCompany.valueOf(cmd.getLogisticsCompany().name()))
                .build();
        internalShopRpc.shipping(shop, shippingCmd);
        return true;
    }

    @Override
    public boolean orderShippingCancel(ShopDO shop, OrderShippingCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean orderDummyShipping(ShopDO shop, OrderDummyShippingCmd cmd) {
        boolean hasShipping = this.checkOrderHasShipping(shop, cmd.getOrderNo());
        if (hasShipping) {
            return true;
        }
        internalShopRpc.dummyShipping(shop, cmd.getOrderNo());
        return true;
    }

    @Override
    public boolean orderRemark(ShopDO shop, OrderRemarkCmd cmd) {
        internalShopRpc.sellerMemo(cmd.getOrderNo(), cmd.getRemark());
        return true;
    }

    @Override
    public OrderDecryptDTO orderDecrypt(ShopDO shop, OrderDecryptCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<InvoiceApplyDTO> pageInvoiceApply(ShopDO shop, InvoiceApplyPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public InvoiceApplyDTO getInvoiceApply(ShopDO shop, InvoiceApplyGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceFile(ShopDO shop, OrderUploadInvoiceCmd cmd, MultipartFile invoiceFile) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceBase64(ShopDO shop, InvoiceUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public InvoiceAmountDTO getInvoiceAmount(ShopDO shop, String orderNo) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<CommentDTO> pageComments(ShopDO shop, CommentPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<RefundDTO> pageRefunds(ShopDO shop, RefundPageQry qry) {
        InternalShopPageQry pageQry = InternalShopPageQry.builder()
                .pageNo(qry.getPageIndex())
                .pageSize(qry.getPageSize())
                .updateTimeEnd(DateUtil.toEpochSecond(qry.getUpdateTimeEnd()))
                .updateTimeStart(DateUtil.toEpochSecond(qry.getUpdateTimeStart()))
                .orderSn(qry.getOrderNo())
                .build();

        InternalShopPageResponse<InternalShopRefundDTO> response = internalShopRpc.pageRefunds(shop, pageQry);
        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }
        List<RefundDTO> refunds = response.getData().stream()
                .map(refund -> internalShopConverter.toAdapterRefundDTO(refund.getRefund()))
                .collect(Collectors.toList());
        return PageResponse.of(refunds, response.getTotal(), qry.getPageSize(), qry.getPageIndex());
    }

    @Override
    public RefundDTO getRefund(ShopDO shop, RefundGetQry qry) {
        InternalShopRefundDTO platformRefund = internalShopRpc.getRefund(shop, qry.getRefundId());
        if (platformRefund == null) {
            throw BizException.of("退款单不存在");
        }
        RefundDTO refundDTO = internalShopConverter.toAdapterRefundDTO(platformRefund.getRefund());
        refundDTO.setOriginData(GsonUtil.objectToJson(platformRefund));
        return refundDTO;
    }

    @Override
    public ShopLogisticsOrderDTO createLogisticsOrder(ShopDO shop, ShopLogisticsOrderCreateCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean cancelLogisticsOrder(ShopDO shop, ShopLogisticsOrderCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void refundGoodsToWarehouse(ShopDO shop, RefundGoodsToWarehouseCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void barcodeUpload(ShopDO shop, BarcodeUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    /**
     * 根据订单明细里的 skuIds 并且转换成统一订单数据
     *
     * @param platformOrder 平台订单数据
     * @return 统一订单数据
     */
    private OrderDTO splitSkuIdsAndConvertToAdapterOrderDTO(InternalShopOrderDTO platformOrder) {
        // 拆分平台维护的 skuId
        List<InternalShopOrderItemDTO> splitSkuItems = SkuUtil.splitSkuIdsAndCloneItem(platformOrder.getItems(),
                InternalShopOrderItemDTO::getSkuSn, InternalShopOrderItemDTO::setSkuSn);
        platformOrder.setItems(splitSkuItems);
        // 转换统一的数据结构
        return internalShopConverter.toAdapterOrderDTO(platformOrder);
    }

    /**
     * 检查订单已发货
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 执行结果
     */
    private boolean checkOrderHasShipping(ShopDO shop, String orderNo) {
        InternalShopOrderDTO order = internalShopRpc.getOrder(shop, orderNo);
        // 判断订单状态已发货，无需重复发货
        if (order.getOrderStatus() == InternalShopOrderStatus.SHIPPED
                || order.getOrderStatus() == InternalShopOrderStatus.ROG
                || order.getOrderStatus() == InternalShopOrderStatus.COMPLETE) {
            log.warn("订单已发货，无需重复发货 {} {}", orderNo, order.getOrderStatus());
            return true;
        }
        // 判断订单状态不是待发货，抛异常
        if (order.getOrderStatus() != InternalShopOrderStatus.PAID_OFF) {
            // 例：平台的订单状态不符合推送发货状态的条件 277901543019 orderState: WAIT_BUYER_PAY
            String msg = String.format("%s %s orderState: %s（%s）",
                    BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrDesc(),
                    orderNo, order.getOrderStatus(), order.getOrderStatusText());
            throw new BizException(BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrCode(), msg);
        }
        return false;
    }

}
