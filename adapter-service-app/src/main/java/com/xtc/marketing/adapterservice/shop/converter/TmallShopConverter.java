package com.xtc.marketing.adapterservice.shop.converter;

import com.google.common.base.Splitter;
import com.taobao.api.domain.Order;
import com.taobao.api.domain.Refund;
import com.taobao.api.domain.Trade;
import com.taobao.api.request.AlibabaEinvoiceDetailUploadRequest;
import com.taobao.api.request.CainiaoWaybillIiGetRequest;
import com.taobao.api.response.AlibabaEinvoiceApplyGetResponse;
import com.taobao.api.response.TopOaidDecryptResponse;
import com.taobao.api.response.TraderatesGetResponse;
import com.xtc.marketing.adapterservice.rpc.tmall.tmalldto.enums.TmallLogisticsCompany;
import com.xtc.marketing.adapterservice.shop.constant.ShopOrderConstant;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.InvoiceItemCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.InvoiceUploadCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsCargoCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsOrderCreateCmd;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceItemTypeEnum;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTitleTypeEnum;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTypeEnum;
import com.xtc.marketing.adapterservice.shop.util.StringJoiner;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.MoneyUtil;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Stream;

@Mapper(
        componentModel = "spring",
        uses = {BaseShopConverter.class},
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface TmallShopConverter {

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "tid")
    @Mapping(target = "orderType", source = "type")
    @Mapping(target = "orderState", source = "status")
    @Mapping(target = "stepOrderState", source = "stepTradeStatus")
    @Mapping(target = "riskState", expression = "java(\"PAID_FORBID_CONSIGN\".equals(source.getStatus()))")
    @Mapping(target = "sellerId", source = "nrShopId")
    @Mapping(target = "sellerName", source = "sellerNick")
    @Mapping(target = "sellerMemo", source = "sellerMemo")
    @Mapping(target = "buyerId", source = "buyerOpenUid")
    @Mapping(target = "buyerName", source = "buyerNick")
    @Mapping(target = "buyerMemo", expression = "java(stringConcat(source.getBuyerMemo(), source.getBuyerMessage()))")
    @Mapping(target = "receiverName", source = "receiverName")
    @Mapping(target = "receiverMobile", source = "receiverMobile")
    @Mapping(target = "receiverProvince", source = "receiverState")
    @Mapping(target = "receiverCity", source = "receiverCity")
    @Mapping(target = "receiverDistrict", source = "receiverDistrict")
    @Mapping(target = "receiverAddress", source = "receiverAddress")
    // 金额和时间
    @Mapping(target = "priceTotal", source = "totalFee", qualifiedByName = "yuanToCent")
    @Mapping(target = "payment", source = "payment", qualifiedByName = "yuanToCent")
    @Mapping(target = "shippingPayment", source = "postFee", qualifiedByName = "yuanToCent")
    @Mapping(target = "discount", expression = "java(sumAmount(source.getCouponFee(), source.getPointFee()))")
    @Mapping(target = "updateTime", source = "modified", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "orderTime", source = "created", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "payTime", source = "payTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "latestShippingTime", expression = "java(buildLatestShippingTime(source.getSendTime(), source.getPayTime()))")
    @Mapping(target = "shippingTime", source = "consignTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "completedTime", source = "endTime", qualifiedByName = "toLocalDateTime")
    // 子对象转换
    @Mapping(target = "items", source = "orders")
    @Mapping(target = "cipherTexts", expression = "java(java.util.Collections.singletonList(source.getOaid()))")
    OrderDTO toAdapterOrderDTO(Trade source);

    /**
     * 转换统一订单明细
     *
     * @param source 平台订单明细
     * @return 统一订单明细
     */
    @Mapping(target = "itemNo", source = "oid")
    @Mapping(target = "itemType", source = "type")
    @Mapping(target = "itemState", source = "status")
    @Mapping(target = "refundState", source = "refundStatus")
    @Mapping(target = "productId", source = "numIid")
    @Mapping(target = "productName", source = "title")
    @Mapping(target = "productErpCode", source = "outerIid")
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "skuName", source = "skuPropertiesName", defaultExpression = "java(source.getTitle())")
    // 物料代码可能存在多个字段 outerIid outerSkuId，并且 outerSkuId 优先级最高
    @Mapping(target = "skuErpCode", source = "outerSkuId", defaultExpression = "java(source.getOuterIid())")
    @Mapping(target = "num", source = "num")
    @Mapping(target = "unitPrice", source = "price", qualifiedByName = "yuanToCent")
    @Mapping(target = "priceTotal", source = "totalFee", qualifiedByName = "yuanToCent")
    @Mapping(target = "discount", source = "discountFee", qualifiedByName = "yuanToCent")
    @Mapping(target = "payment", source = "payment", qualifiedByName = "yuanToCent")
    OrderItemDTO toAdapterOrderItemDTO(Order source);

    /**
     * 转换统一发票申请数据
     *
     * @param source 平台发票申请数据
     * @return 统一发票申请数据
     */
    @Mapping(target = "orderNo", source = "platformTid")
    @Mapping(target = "invoiceType", source = "invoiceKind", qualifiedByName = "toAdapterInvoiceTypeEnum")
    @Mapping(target = "invoiceTitleType", source = "businessType", qualifiedByName = "toAdapterInvoiceTitleTypeEnum")
    @Mapping(target = "invoiceTitle", source = "payerName")
    @Mapping(target = "taxNo", source = "payerRegisterNo")
    @Mapping(target = "bankName", source = "payerBank")
    @Mapping(target = "bankAccount", source = "payerBankaccount")
    @Mapping(target = "companyMobile", source = "payerPhone")
    @Mapping(target = "companyAddress", source = "payerAddress")
    @Mapping(target = "buyerMemo", source = "memo")
    @Mapping(target = "invoiceAmount", source = "invoiceAmount", qualifiedByName = "yuanToCent")
    @Mapping(target = "invoiceMemo", source = "extendProps", qualifiedByName = "toInvoiceMemo")
    @Mapping(target = "sellerIdentifyNo", source = "extendProps", qualifiedByName = "toSellerIdentifyNo")
    InvoiceApplyDTO toAdapterInvoiceApplyDTO(AlibabaEinvoiceApplyGetResponse.Apply source);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    List<RefundDTO> toAdapterRefundDTO(List<Refund> source);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "tid")
    @Mapping(target = "orderItemNo", source = "oid")
    @Mapping(target = "serviceNo", source = "refundId")
    @Mapping(target = "serviceType", source = "disputeType")
    @Mapping(target = "serviceState", source = "status")
    @Mapping(target = "applyReason", expression = "java(stringConcat(\"【\" + source.getReason() + \"】\", source.getDesc()))")
    @Mapping(target = "sellerName", source = "sellerNick")
    @Mapping(target = "buyerId", source = "buyerOpenUid")
    @Mapping(target = "buyerName", source = "buyerNick")
    @Mapping(target = "returnWaybillNo", source = "sid")
    @Mapping(target = "returnExpressCompany", source = "companyName")
    @Mapping(target = "productId", source = "outerId")
    @Mapping(target = "productName", source = "title")
    @Mapping(target = "skuId", source = "sku")
    @Mapping(target = "num", source = "num")
    // 金额和时间
    @Mapping(target = "refundApplyAmount", source = "refundFee", qualifiedByName = "yuanToCent")
    @Mapping(target = "refundAmount", source = "refundFee", qualifiedByName = "yuanToCent")
    @Mapping(target = "payment", source = "payment", qualifiedByName = "yuanToCent")
    @Mapping(target = "refundTime", source = "modified", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "returnTime", source = "goodReturnTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "updateTime", source = "modified", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "createTime", source = "created", qualifiedByName = "toLocalDateTime")
    RefundDTO toAdapterRefundDTO(Refund source);

    /**
     * 转换统一评价数据
     *
     * @param source 平台评价数据
     * @return 统一评价数据
     */
    List<CommentDTO> toAdapterCommentDTO(List<TraderatesGetResponse.Results> source);

    /**
     * 转换统一评价数据
     *
     * @param source 平台评价数据
     * @return 统一评价数据
     */
    @Mapping(target = "orderNo", source = "tid")
    @Mapping(target = "buyerName", source = "nick")
    @Mapping(target = "skuId", source = "numIid")
    @Mapping(target = "skuName", source = "itemTitle")
    @Mapping(target = "content", source = "content")
    @Mapping(target = "isGoodRating", expression = "java(\"good\".equals(source.getResult()))")
    @Mapping(target = "createTime", source = "created", qualifiedByName = "toLocalDateTime")
    CommentDTO toAdapterCommentDTO(TraderatesGetResponse.Results source);

    /**
     * 转换统一解密数据
     *
     * @param source 平台解密数据
     * @return 统一解密数据
     */
    @Mapping(target = "orderNo", source = "tid")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "mobile", source = "mobile")
    @Mapping(target = "phone", source = "phone")
    @Mapping(target = "province", source = "state")
    @Mapping(target = "city", source = "city")
    @Mapping(target = "district", source = "district")
    @Mapping(target = "town", source = "town")
    @Mapping(target = "address", source = "addressDetail")
    @Mapping(target = "mobileExpireTime", source = "secretNoExpireTime", qualifiedByName = "toLocalDateTime")
    OrderDecryptDTO toAdapterOrderDecryptDTO(TopOaidDecryptResponse.Receiver source);

    /**
     * 转换平台电子面单参数
     *
     * @param shopLogisticsOrderCreateCmd 统一电子面单参数
     * @param logisticsCompany            快递公司信息
     * @param order                       平台电子面单，订单参数
     * @return 平台电子面单参数
     */
    @Mapping(target = "needEncrypt", constant = "true")
    @Mapping(target = "cpCode", source = "logisticsCompany.code")
    @Mapping(target = "brandCode", source = "logisticsCompany.brandCode")
    @Mapping(target = "productCode", source = "logisticsCompany.productCode")
    @Mapping(target = "sender.name", source = "shopLogisticsOrderCreateCmd.senderName")
    @Mapping(target = "sender.mobile", source = "shopLogisticsOrderCreateCmd.senderPhone")
    @Mapping(target = "sender.address.province", source = "shopLogisticsOrderCreateCmd.senderProvince")
    @Mapping(target = "sender.address.city", source = "shopLogisticsOrderCreateCmd.senderCity")
    @Mapping(target = "sender.address.detail", expression = "java(shopLogisticsOrderCreateCmd.getSenderDistrict() + shopLogisticsOrderCreateCmd.getSenderAddress())")
    @Mapping(target = "tradeOrderInfoDtos", source = "order", qualifiedByName = "newSingletonList")
    @Mapping(target = "callDoorPickUp", expression = "java(shopLogisticsOrderCreateCmd.getReserve() != null)")
    @Mapping(target = "customerCode", source = "shopLogisticsOrderCreateCmd.reserve.bizAccount")
    @Mapping(target = "doorPickUpTime", source = "shopLogisticsOrderCreateCmd.reserve.pickUpTimeStart", qualifiedByName = "localDateTimeToString")
    @Mapping(target = "doorPickUpEndTime", source = "shopLogisticsOrderCreateCmd.reserve.pickUpTimeEnd", qualifiedByName = "localDateTimeToString")
    CainiaoWaybillIiGetRequest.WaybillCloudPrintApplyNewRequest toTmallWaybillCloudPrintApplyNewRequest(
            ShopLogisticsOrderCreateCmd shopLogisticsOrderCreateCmd,
            TmallLogisticsCompany logisticsCompany,
            CainiaoWaybillIiGetRequest.TradeOrderInfoDto order
    );

    /**
     * 转换平台电子面单参数
     *
     * @param shopLogisticsOrderCreateCmd 统一电子面单参数
     * @param shop                        店铺
     * @param logisticsCompany            快递公司信息
     * @return 平台电子面单参数
     */
    @Mapping(target = "objectId", source = "shopLogisticsOrderCreateCmd.logisticsOrderId", defaultExpression = "java(java.util.UUID.randomUUID().toString().replace(\"-\", \"\"))")
    @Mapping(target = "userId", expression = "java(java.lang.Long.valueOf(shop.getShopId()))")
    @Mapping(target = "templateUrl", source = "logisticsCompany.templateUrl")
    @Mapping(target = "recipient.name", source = "shopLogisticsOrderCreateCmd.receiverName")
    @Mapping(target = "recipient.address.province", source = "shopLogisticsOrderCreateCmd.receiverProvince")
    @Mapping(target = "recipient.address.city", source = "shopLogisticsOrderCreateCmd.receiverCity")
    @Mapping(target = "recipient.address.district", source = "shopLogisticsOrderCreateCmd.receiverDistrict")
    @Mapping(target = "recipient.address.detail", source = "shopLogisticsOrderCreateCmd.receiverAddress")
    @Mapping(target = "recipient.caid", source = "shopLogisticsOrderCreateCmd.receiverOaid")
    @Mapping(target = "recipient.oaid", source = "shopLogisticsOrderCreateCmd.receiverOaid")
    @Mapping(target = "recipient.tid", source = "shopLogisticsOrderCreateCmd.shopOrderNo")
    @Mapping(target = "orderInfo.tradeOrderList", source = "shopLogisticsOrderCreateCmd.shopOrderNo", qualifiedByName = "newSingletonList")
    @Mapping(target = "orderInfo.orderChannelsType", constant = "TM")
    @Mapping(target = "packageInfo.items", source = "shopLogisticsOrderCreateCmd.cargos")
    // good_description 包裹货品描述支持最大长度 20 个字符
    @Mapping(target = "packageInfo.goodsDescription", expression = "java(org.apache.commons.lang3.StringUtils.left(shopLogisticsOrderCreateCmd.getMainCargoName(), 20))")
    CainiaoWaybillIiGetRequest.TradeOrderInfoDto toTmallWaybillCloudPrintApplyNewRequest(
            ShopLogisticsOrderCreateCmd shopLogisticsOrderCreateCmd,
            ShopDO shop,
            TmallLogisticsCompany logisticsCompany
    );

    /**
     * 转换平台电子面单参数
     *
     * @param source 统一电子面单参数
     * @return 平台电子面单参数
     */
    @Mapping(target = "count", source = "quantity")
    @Mapping(target = "name", source = "name")
    CainiaoWaybillIiGetRequest.Item toTmallWaybillCloudPrintApplyNewRequest(ShopLogisticsCargoCmd source);

    /**
     * 转换平台发票上传参数
     *
     * @param source 统一发票上传参数
     * @return 平台发票上传参数
     */
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "invoiceType", constant = "blue")
    @Mapping(target = "invoiceNo", source = "source.blueInvoiceNo")
    @Mapping(target = "payeeRegisterNo", source = "source.payeeRegisterNo")
    @Mapping(target = "invoiceAmount", source = "source.invoiceAmount", qualifiedByName = "centToYuan")
    @Mapping(target = "invoiceDate", source = "source.blueTime", qualifiedByName = "toInvoiceDate")
    @Mapping(target = "platformTid", source = "source.orderNo")
    @Mapping(target = "payerName", source = "source.payerName")
    @Mapping(target = "invoiceKind", source = "source.invoiceType", qualifiedByName = "toInvoiceKind")
    @Mapping(target = "businessType", source = "source.invoiceTitleType", qualifiedByName = "toBusinessType")
    @Mapping(target = "sumPrice", source = "source.sumPrice", qualifiedByName = "centToYuan")
    @Mapping(target = "sumTax", source = "source.tax", qualifiedByName = "centToYuan")
    @Mapping(target = "platformCode", constant = "TM")
    @Mapping(target = "payerRegisterNo", source = "source.payeeRegisterNo")
    @Mapping(target = "payerPhone", source = "source.payerPhone")
    @Mapping(target = "payerAddress", source = "source.payerAddress")
    @Mapping(target = "payerBankaccount", source = "source.payerBank")
    AlibabaEinvoiceDetailUploadRequest toAlibabaEinvoiceDetailUploadRequest(InvoiceUploadCmd source);

    List<AlibabaEinvoiceDetailUploadRequest.InvoiceItem> toInvoiceItem(List<InvoiceItemCmd> source);

    /**
     * 转换平台发票上传参数
     *
     * @param source 统一发票上传参数
     * @return 平台发票上传参数
     */
    @Mapping(target = "taxRate", source = "taxRate")
    @Mapping(target = "itemName", source = "goodsName")
    @Mapping(target = "price", source = "price")
    @Mapping(target = "quantity", source = "num")
    @Mapping(target = "rowType", source = "itemType", qualifiedByName = "toRowType")
    @Mapping(target = "sumPrice", source = "priceAmount")
    @Mapping(target = "tax", source = "tax")
    @Mapping(target = "itemNo", source = "taxClassificationCode")
    @Mapping(target = "amount", source = "taxAmount")
    AlibabaEinvoiceDetailUploadRequest.InvoiceItem toInvoiceItem(InvoiceItemCmd source);

    /**
     * 转换统一订单数据时，处理国家补贴订单
     *
     * @param order         统一订单数据
     * @param platformOrder 平台订单数据
     */
    @AfterMapping
    default void setNationalSubsidy(@MappingTarget OrderDTO order, Trade platformOrder) {
        // 识别国家补贴订单：字段 govSnCheck 不为空
        if (StringUtils.isBlank(platformOrder.getGovSnCheck())) {
            return;
        }
        order.setOrderType(StringJoiner.concatWithSplit(order.getOrderType(), ShopOrderConstant.ORDER_TYPE_NATIONAL_SUBSIDY));
        order.setOrderTypeDesc(StringJoiner.concatWithSplit(order.getOrderTypeDesc(), ShopOrderConstant.ORDER_TYPE_DESC_NATIONAL_SUBSIDY));
    }

    /**
     * 转换统一订单数据时，处理全渠道类型的2.0订单
     *
     * @param order         统一订单数据
     * @param platformOrder 平台订单数据
     */
    @AfterMapping
    default void setOmnichannelOrderV2(@MappingTarget OrderDTO order, Trade platformOrder) {
        String tradeAttr = platformOrder.getTradeAttr();
        if (StringUtils.isBlank(tradeAttr)) {
            return;
        }
        // 设置订单类型：全渠道（OMNICHANNEL）
        String scenarioGroup = GsonUtil.getAsString(tradeAttr, "scenarioGroup");
        if ("XSDBC".equals(scenarioGroup)) {
            order.setOrderType(StringJoiner.concatWithSplit(order.getOrderType(), ShopOrderConstant.OMNICHANNEL_TYPE));
            order.setOrderTypeDesc(StringJoiner.concatWithSplit(order.getOrderTypeDesc(), "全渠道"));
            // 设置订单状态：全渠道已发货
            if ("TRADE_FINISHED,WAIT_BUYER_CONFIRM_GOODS,TRADE_BUYER_SIGNED".contains(platformOrder.getStatus())) {
                order.setStepOrderState(ShopOrderConstant.OMNICHANNEL_FINISHED_STATE);
                order.setStepOrderStateDesc("全渠道订单已发货");
            }
        }
    }

    /**
     * 转换统一订单数据时，处理全渠道类型的1.0订单
     *
     * @param order         统一订单数据
     * @param platformOrder 平台订单数据
     */
    @AfterMapping
    default void setOmnichannelOrderV1(@MappingTarget OrderDTO order, Trade platformOrder) {
        // 优先处理全渠道类型的2.0订单
        if (StringUtils.isNotBlank(platformOrder.getTradeAttr())) {
            return;
        }
        String originParam = platformOrder.getOmnichannelParam();
        if (StringUtils.isBlank(originParam)) {
            return;
        }
        // 解析 omnichannel_param 字段的 kv 格式，先以 [;] 拆分列表，然后取第一个数据，最后以 [,] 作为参数的分隔符
        // orderType:STORE_DELIVER,everStoreAllocated:0,allocationCode:926180398,targetCode:DGBBK0001,subOrderCode:,targetType:WAREHOUSE,orderState:X_OTHER_ALLOCATION_NOTIFIED;orderType:STORE_DELIVER,everStoreAllocated:0,allocationCode:926180398,targetCode:DGBBK0001,subOrderCode:,targetType:WAREHOUSE,orderState:X_OTHER_ALLOCATION_NOTIFIED;
        List<String> omnichannelParams = Splitter.on(";").trimResults().omitEmptyStrings().splitToList(originParam);
        if (omnichannelParams.isEmpty()) {
            return;
        }
        // 格式化无法使用 [:] 拆分的属性，将时间值从 2024/10/21-18:26:51 格式化为 2024/10/21-18/26/51
        String reformatRegex = "(\\d{4}/\\d{2}/\\d{2})-(\\d{2}):(\\d{2}):(\\d{2})";
        String replacement = "$1-$2/$3/$4";
        String reformatParam = omnichannelParams.get(0).replaceAll(reformatRegex, replacement);
        Map<String, String> omnichannelParam = Splitter.on(",").trimResults().omitEmptyStrings()
                .withKeyValueSeparator(":").split(reformatParam);
        // 设置订单类型：全渠道（OMNICHANNEL）
        String orderType = omnichannelParam.get("orderType");
        String targetType = omnichannelParam.get("targetType");
        if ("STORE_DELIVER,STORE_COLLECT".contains(orderType) && "STORE".equals(targetType)) {
            order.setOrderType(StringJoiner.concatWithSplit(order.getOrderType(), ShopOrderConstant.OMNICHANNEL_TYPE));
            order.setOrderTypeDesc(StringJoiner.concatWithSplit(order.getOrderTypeDesc(), "全渠道"));
            // 设置订单状态：全渠道已发货
            String orderState = omnichannelParam.get("orderState");
            if ("X_SHOP_HANDLED,X_STORE_COLLECT_CONSUMED".contains(orderState)) {
                order.setStepOrderState(ShopOrderConstant.OMNICHANNEL_FINISHED_STATE);
                order.setStepOrderStateDesc("门店已发货，或门店自提已核销");
            }
        }
    }

    /**
     * 转换统一退款数据时，设置 sku 数据
     *
     * @param refund 统一退款数据
     */
    @AfterMapping
    default void setRefundSku(@MappingTarget RefundDTO refund) {
        if (StringUtils.isBlank(refund.getSkuId())) {
            return;
        }
        // 设置 sku 数据（例：30004447689|颜色分类:军绿色;尺码:XS）
        List<String> sku = Splitter.on("|").omitEmptyStrings().trimResults().splitToList(refund.getSkuId());
        if (!sku.isEmpty()) {
            refund.setSkuId(sku.get(0));
        }
        if (sku.size() > 1) {
            refund.setSkuName(sku.get(1));
        }
    }

    /**
     * 汇总金额
     *
     * @param amountArr 金额集合
     * @return 总金额
     */
    @Named("sumAmount")
    default int sumAmount(Long... amountArr) {
        return Stream.of(amountArr)
                .filter(Objects::nonNull)
                .map(Long::intValue)
                .reduce(Integer::sum)
                .orElse(0);
    }

    /**
     * 转换发票类型
     *
     * @param invoiceType 发票类型
     * @return 发票类型枚举
     */
    @Named("toAdapterInvoiceTypeEnum")
    default InvoiceTypeEnum toAdapterInvoiceTypeEnum(Long invoiceType) {
        // 默认值为【电子发票】，发票种类：0=电子发票，1=纸质发票，2=专票
        if (invoiceType == 1) {
            return InvoiceTypeEnum.NORMAL;
        }
        if (invoiceType == 2) {
            return InvoiceTypeEnum.SPECIAL;
        }
        return InvoiceTypeEnum.ELECTRONIC;
    }

    /**
     * 转换发票抬头类型
     *
     * @param invoiceTitleType 发票抬头类型
     * @return 发票抬头类型枚举
     */
    @Named("toAdapterInvoiceTitleTypeEnum")
    default InvoiceTitleTypeEnum toAdapterInvoiceTitleTypeEnum(Long invoiceTitleType) {
        // 默认值为【个人】，发票抬头类型：0=个人，1=企业
        if (invoiceTitleType == 1) {
            return InvoiceTitleTypeEnum.COMPANY;
        }
        return InvoiceTitleTypeEnum.PERSONAL;
    }

    /**
     * 转换发票备注
     *
     * @param extendProps 扩展属性
     * @return 发票备注
     */
    @Named("toInvoiceMemo")
    default String toInvoiceMemo(String extendProps) {
        if (StringUtils.isBlank(extendProps)) {
            return null;
        }
        // "extendProps": "{\"channel\":\"zfbtYpddNotUpload\",\"zfbt_invoice_amount\":\"230.10\",\"invoice_memo\":\"淘宝订单号:4211130854835351403;政府补贴金额:230.10元;69码:6953281007760;SN码:60ND74B0AB81M;IMEI码:868459079174677;\"}"
        return GsonUtil.getAsString(extendProps, "invoice_memo");
    }

    /**
     * 构建最晚发货时间
     *
     * @param sendTime 最晚发货时间
     * @param payTime  支付时间
     * @return 最晚发货时间
     */
    @Named("buildLatestShippingTime")
    default LocalDateTime buildLatestShippingTime(String sendTime, Date payTime) {
        if (sendTime == null || payTime == null) {
            return null;
        }
        LocalDate sendLocalDateTime = DateUtil.toLocalDate(sendTime);
        LocalDateTime payLocalDateTime = DateUtil.toLocalDateTime(payTime);
        return LocalDateTime.of(sendLocalDateTime, payLocalDateTime.toLocalTime());
    }

    /**
     * 字符串拼接
     *
     * @param prefix 前缀
     * @param suffix 后缀
     * @return 拼接后的字符串
     */
    @Named("stringConcat")
    default String stringConcat(String prefix, String suffix) {
        String prefixNoNull = StringUtils.defaultIfBlank(prefix, "");
        String suffixNoNull = StringUtils.defaultIfBlank(suffix, "");
        return prefixNoNull.concat(suffixNoNull);
    }

    /**
     * 元转分
     *
     * @param yuan 元
     * @return 分
     */
    @Named("yuanToCent")
    default Integer yuanToCent(String yuan) {
        return MoneyUtil.yuanToCent(yuan);
    }

    /**
     * 分转分
     *
     * @param yuan 分
     * @return 元
     */
    @Named("centToYuan")
    default String centToYuan(Integer yuan) {
        return MoneyUtil.centToYuan(yuan);
    }

    /**
     * 时间转换
     *
     * @param time 时间
     * @return LocalDateTime
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(Date time) {
        if (time == null) {
            return null;
        }
        return DateUtil.toLocalDateTime(time);
    }

    /**
     * 生成单例集合
     *
     * @param obj 对象
     * @return 单例集合
     */
    @Named("newSingletonList")
    default <T> List<T> newSingletonList(T obj) {
        return Collections.singletonList(obj);
    }

    /**
     * 发票类型
     *
     * @param invoiceType 发票类型
     * @return 发票类型
     */
    @Named("toInvoiceKind")
    default long toInvoiceKind(InvoiceTypeEnum invoiceType) {
        return InvoiceTypeEnum.ELECTRONIC == invoiceType ? 5L : 4L;
    }

    /**
     * 发票抬头类型
     *
     * @param invoiceTitleType 发票抬头类型
     * @return 发票抬头类型
     */
    @Named("toBusinessType")
    default long toBusinessType(InvoiceTitleTypeEnum invoiceTitleType) {
        return invoiceTitleType == InvoiceTitleTypeEnum.COMPANY ? 1L : 0L;
    }

    /**
     * 时间转换
     *
     * @param blueTime 开票时间
     * @return 开票时间
     */
    @Named("toInvoiceDate")
    default String toInvoiceDate(LocalDateTime blueTime) {
        return DateUtil.toString(blueTime, "yyyy-MM-dd");
    }

    /**
     * 时间转换
     *
     * @param time 时间
     * @return 时间字符串
     */
    @Named("localDateTimeToString")
    default String localDateTimeToString(LocalDateTime time) {
        return DateUtil.toString(time);
    }

    /**
     * 发票项目类型
     *
     * @param invoiceItemType 发票项目类型
     * @return 发票项目类型
     */
    @Named("toRowType")
    default String toRowType(InvoiceItemTypeEnum invoiceItemType) {
        if (InvoiceItemTypeEnum.NORMAL == invoiceItemType) {
            return "0";
        }
        if (InvoiceItemTypeEnum.DISCOUNT == invoiceItemType) {
            return "1";
        }
        if (InvoiceItemTypeEnum.DISCOUNTED == invoiceItemType) {
            return "2";
        }
        return null;
    }

    /**
     * 销售方税号
     *
     * @param extendProps 扩展字段
     * @return 销售方税号
     */
    @Named("toSellerIdentifyNo")
    default String toSellerIdentifyNo(String extendProps) {
        return GsonUtil.getAsString(extendProps, "ypddPayeeRegisterNo");
    }

}
