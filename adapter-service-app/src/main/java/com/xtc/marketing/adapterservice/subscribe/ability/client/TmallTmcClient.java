package com.xtc.marketing.adapterservice.subscribe.ability.client;

import com.taobao.api.internal.tmc.Message;
import com.taobao.api.internal.tmc.TmcClient;
import com.taobao.api.internal.tmc.TmcClientBuilder;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.subscribe.ability.domainservice.SubscribeLogDomainService;
import com.xtc.marketing.adapterservice.subscribe.ability.domainservice.SubscribePushOmsDomainService;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * 天猫消息订阅 TmcClient
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class TmallTmcClient {

    private final SubscribePushOmsDomainService subscribePushOmsDomainService;

    /**
     * 连接地址
     */
    public static final String CONNECT_URL = "ws://mc.api.taobao.com";
    /**
     * TMC 客户端缓存
     */
    private static final Map<String, TmcClient> TMC_CLIENT = new ConcurrentHashMap<>();

    /**
     * 清除 TmcClient 缓存
     *
     * @param shop 店铺
     * @return 被清除的 TmcClient 为 null 说明没有缓存
     */
    public TmcClient removeTmcClient(ShopDO shop) {
        return TMC_CLIENT.remove(shop.getShopCode());
    }

    /**
     * 获取消息订阅 TmcClient
     *
     * @param shop     店铺
     * @param bizParam 业务参数
     * @return TmcClient
     */
    public TmcClient getTmcClient(ShopDO shop, String bizParam) {
        return TMC_CLIENT.computeIfAbsent(shop.getShopCode(), k -> newTmcClient(shop, bizParam));
    }

    /**
     * 生成 TmcClient
     *
     * @param shop     店铺
     * @param bizParam 业务参数
     * @return TmcClient
     */
    private TmcClient newTmcClient(ShopDO shop, String bizParam) {
        TmcClient client = new TmcClientBuilder()
                .setAppKey(shop.getAppKey())
                .setAppSecret(shop.getAppSecret())
                .setGroupName("default") // 关于default参考消息分组说明
                .setPullMode(true) // 拉模式 - true，推模式 - false
                .build();
        client.setQueueSize(1000);  // 本地消息缓冲队列，不要设置太大，避免堆积过久，导致未即时处理、雪崩重发
        client.setThreadCount(2); // 处理消息线程数

        // 消息处理逻辑
        client.setMessageHandler((message, status) -> {
            // 注意消息处理耗时，收到消息后若阻塞达分钟级将导致确认超时，此时服务端会重发消息，请根据处理tps合理设置分组流控！
            // 注意日志级别，或主动打点日志，避免收到消息而不能断定是否收到了消息
            SubscribeLogDomainService.logTraceId();
            log.info("TmallTmcClient - message: {}", GsonUtil.objectToJson(message));
            try {
                // 过滤无需消费的消息
                if (this.filterNotConsumerMessage(message)) {
                    return;
                }
                // 消息处理
                Consumer<SubscribePushOmsDomainService.BizData> dataConsumer = this.getDataConsumer(message.getTopic());
                if (dataConsumer != null) {
                    SubscribePushOmsDomainService.BizData bizData = this.buildBizData(message, bizParam);
                    dataConsumer.accept(bizData);
                } else {
                    // 不同的业务对应不同的数据id，例：订单号、退款单号
                    log.warn("TmallTmcClient - 未找到消息处理器 topic: {}, dataId: {}", message.getTopic(), message.getDataId());
                }
            } catch (Exception e) {
                // 消息处理失败回滚，服务端会延时约6分钟后重发
                // 重试注意：不是所有的异常都需要系统重试
                // 对于字段不全、主键冲突问题，导致写DB异常，不可重试，否则消息会一直重发
                // 对于，由于网络问题、权限问题导致的失败，可重试
                // 重试时间为6分钟左右，不要滥用，否则会引起雪崩
                log.warn("TmallTmcClient - 消息处理失败 {}", e.getMessage(), e);
                status.fail();
            }
        });
        return client;
    }

    /**
     * 获取数据处理器
     * <p>消息主题与消息处理器映射</p>
     * <pre>
     *     订单消息：taobao_trade
     *     退款单消息：taobao_refund
     *     发票消息：alibaba_invoice
     * </pre>
     *
     * @param topic 消息主题
     * @return 数据处理器
     */
    private Consumer<SubscribePushOmsDomainService.BizData> getDataConsumer(String topic) {
        if (topic.startsWith("taobao_trade")) {
            return subscribePushOmsDomainService::tradePushToOms;
        } else if (topic.startsWith("taobao_refund")) {
            return subscribePushOmsDomainService::refundPushToOms;
        } else if (topic.startsWith("alibaba_invoice")) {
            return subscribePushOmsDomainService::invoiceApplyPushToOms;
        }
        return null;
    }

    /**
     * 构建业务数据
     *
     * @param message    消息
     * @param platformId 平台id
     * @return 业务数据
     */
    private SubscribePushOmsDomainService.BizData buildBizData(Message message, String platformId) {
        String topic = message.getTopic();
        SubscribePushOmsDomainService.BizData bizData = SubscribePushOmsDomainService.BizData.builder().platformId(platformId).build();
        // 不同的业务对应不同的数据id，例：订单号、退款单号
        String dataId = StringUtils.defaultString(message.getDataId());
        if (topic.startsWith("taobao_trade") || topic.startsWith("alibaba_invoice")) {
            bizData.setTradeId(dataId);
        } else if (topic.startsWith("taobao_refund")) {
            bizData.setRefundId(dataId);
        }
        return bizData;
    }

    /**
     * 检查不消费的消息
     *
     * @param message 消息
     * @return 执行结果
     */
    private boolean filterNotConsumerMessage(Message message) {
        if (message.getTopic().startsWith("alibaba_invoice")) {
            String triggerStatus = GsonUtil.getAsString(message.getContent(), "trigger_status");
            if ("refund_seller_confirm".equals(triggerStatus)) {
                log.info("{} 发票申请无需同步，开票申请的触发类型为 {}", message.getDataId(), triggerStatus);
                return true;
            }
        }
        return false;
    }

}
