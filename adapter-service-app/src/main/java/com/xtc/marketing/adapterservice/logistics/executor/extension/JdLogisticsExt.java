package com.xtc.marketing.adapterservice.logistics.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.jd.open.api.sdk.domain.etms.TraceQueryJsf.response.get.TraceDTO;
import com.jd.open.api.sdk.domain.etms.WaybillJosService.response.receive.WaybillResultInfoDTO;
import com.jd.open.api.sdk.request.etms.LdopReceiveTraceGetRequest;
import com.jd.open.api.sdk.request.etms.LdopWaybillReceiveRequest;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.logistics.converter.JdLogisticsConverter;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsCloudPrintDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsOrderDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCancelOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCloudPrintCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsInterceptCmd;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsOrderGetQry;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsRouteListQry;
import com.xtc.marketing.adapterservice.rpc.jd.JdLogisticsRpc;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = LogisticsExtConstant.USE_CASE, scenario = LogisticsExtConstant.SCENARIO_JD)
public class JdLogisticsExt implements LogisticsExtPt {

    private final JdLogisticsRpc jdLogisticsRpc;
    private final JdLogisticsConverter jdLogisticsConverter;

    @Override
    public List<LogisticsRouteDTO> routes(LogisticsAccountDO account, LogisticsRouteListQry qry) {
        LdopReceiveTraceGetRequest request = new LdopReceiveTraceGetRequest();
        request.setWaybillCode(qry.getWaybillNo());
        request.setCustomerCode(qry.getBizAccount());
        List<TraceDTO> listRouteDTO = jdLogisticsRpc.searchRoutes(account, request);
        return jdLogisticsConverter.toAdapterRouteDTO(listRouteDTO);
    }

    @Override
    public LogisticsOrderDTO createOrder(LogisticsAccountDO account, LogisticsCreateOrderCmd cmd) {
        LdopWaybillReceiveRequest request = jdLogisticsConverter.toLdopWaybillReceiveRequest(cmd);
        WaybillResultInfoDTO waybillResultInfoDTO = jdLogisticsRpc.createOrder(account, request);
        String wayBillNo = Optional.ofNullable(waybillResultInfoDTO)
                .map(WaybillResultInfoDTO::getDeliveryId)
                .orElse(null);
        return LogisticsOrderDTO.builder()
                .wayBillNo(wayBillNo)
                .orderDetail(GsonUtil.objectToJson(waybillResultInfoDTO))
                .build();
    }

    @Override
    public String getOrder(LogisticsAccountDO account, LogisticsOrderGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public String getWaybillNo(LogisticsAccountDO account, LogisticsOrderGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean cancelOrder(LogisticsAccountDO account, LogisticsCancelOrderCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean intercept(LogisticsAccountDO account, LogisticsInterceptCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public LogisticsCloudPrintDTO cloudPrint(LogisticsAccountDO account, LogisticsCloudPrintCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

}
