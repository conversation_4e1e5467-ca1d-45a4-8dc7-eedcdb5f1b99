package com.xtc.marketing.adapterservice.warehouse;

import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.xtc.marketing.adapterservice.warehouse.dto.OutboundDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.OutboundDetailDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.WarehouseStockDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.command.InboundApplyCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.InboundCancelCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.OutboundApplyCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.OutboundCancelCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.query.OutboundDetailQry;
import com.xtc.marketing.adapterservice.warehouse.dto.query.OutboundPageQry;
import com.xtc.marketing.adapterservice.warehouse.dto.query.WarehouseStockQry;
import com.xtc.marketing.adapterservice.warehouse.enums.WarehouseEnum;
import com.xtc.marketing.adapterservice.warehouse.executor.extension.WarehouseExtConstant;
import com.xtc.marketing.adapterservice.warehouse.executor.extension.WarehouseExtPt;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

@RequiredArgsConstructor
@Service
public class WarehouseServiceImpl implements WarehouseService {

    private final ExtensionExecutor extensionExecutor;

    @Override
    public PageResponse<OutboundDTO> pageOutbound(OutboundPageQry qry) {
        return extensionExecutor(qry.getWarehouse(), executor -> executor.pageOutbound(qry));
    }

    @Override
    public OutboundDetailDTO getOutboundDetail(OutboundDetailQry qry) {
        return extensionExecutor(qry.getWarehouse(), executor -> executor.getOutboundDetail(qry));
    }

    @Override
    public List<WarehouseStockDTO> queryStocks(WarehouseStockQry qry) {
        return extensionExecutor(qry.getWarehouse(), executor -> executor.queryStocks(qry));
    }

    @Override
    public String applyInbound(InboundApplyCmd cmd) {
        return extensionExecutor(cmd.getWarehouse(), executor -> executor.applyInbound(cmd));
    }

    @Override
    public String cancelInbound(InboundCancelCmd cmd) {
        return extensionExecutor(cmd.getWarehouse(), executor -> executor.cancelInbound(cmd));
    }

    @Override
    public String applyOutbound(OutboundApplyCmd cmd) {
        return extensionExecutor(cmd.getWarehouse(), executor -> executor.applyOutbound(cmd));
    }

    @Override
    public String cancelOutbound(OutboundCancelCmd cmd) {
        return extensionExecutor(cmd.getWarehouse(), executor -> executor.cancelOutbound(cmd));
    }

    /**
     * 执行扩展点
     *
     * @param warehouseEnum 仓库
     * @param function      扩展点方法
     * @param <R>           返回值类型
     * @return 返回值
     */
    private <R> R extensionExecutor(WarehouseEnum warehouseEnum, Function<WarehouseExtPt, R> function) {
        BizScenario bizScenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID,
                WarehouseExtConstant.USE_CASE, warehouseEnum.name());
        return extensionExecutor.execute(WarehouseExtPt.class, bizScenario, function);
    }

    /**
     * 执行扩展点
     *
     * @param warehouseEnum 仓库
     * @param consumer      扩展点方法
     */
    private void extensionExecutorVoid(WarehouseEnum warehouseEnum, Consumer<WarehouseExtPt> consumer) {
        BizScenario bizScenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID,
                WarehouseExtConstant.USE_CASE, warehouseEnum.name());
        extensionExecutor.executeVoid(WarehouseExtPt.class, bizScenario, consumer);
    }

}
