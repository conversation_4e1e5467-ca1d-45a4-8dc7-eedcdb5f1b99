package com.xtc.marketing.adapterservice.notify.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.google.gson.JsonObject;
import com.taobao.api.internal.spi.CheckResult;
import com.taobao.api.internal.spi.SpiUtils;
import com.xtc.marketing.adapterservice.constant.SystemConstant;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.notify.dataobject.ReceiveLogDO;
import com.xtc.marketing.adapterservice.notify.dto.command.NotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.enums.NotifyEnum;
import com.xtc.marketing.adapterservice.rpc.oms.OmsRpc;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.command.OmsModifyAddressNotifyCmd;
import com.xtc.marketing.adapterservice.rpc.tmall.tmalldto.TmallModifyAddressNotifyDTO;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.executor.query.ShopGetQryExe;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletRequest;

/**
 * 通知扩展点 - 天猫修改地址
 */
@Slf4j
@RequiredArgsConstructor
@Extension(bizId = NotifyExtConstant.BIZ_ID_TMALL,
        useCase = NotifyExtConstant.USE_CASE_SHOP, scenario = NotifyExtConstant.SCENARIO_MODIFY_ADDRESS)
public class TmallModifyAddressNotifyExt implements NotifyExtPt {

    private final ShopGetQryExe shopGetQryExe;
    private final OmsRpc omsRpc;

    /**
     * 平台代码：天猫
     */
    private static final String PLATFORM_TMALL = "TMALL";

    @Override
    public ReceiveLogDO createReceiveLog(NotifyEnum notify, NotifyReceiveCmd cmd) {
        ShopDO shop = shopGetQryExe.execute(cmd.getShopCode());
        if (!PLATFORM_TMALL.equals(shop.getPlatformCode())) {
            throw BizException.of("不支持非天猫平台");
        }
        // 解析通知数据
        String notifyRawData;
        try {
            notifyRawData = this.parseNotifyRawData(shop, cmd.getRequest());
        } catch (Exception e) {
            log.warn("解析通知数据异常：{}", e.getMessage(), e);
            // 返回验签失败响应，不需要保存数据所以不设置 dataId
            return ReceiveLogDO.builder().responseStr(this.responseSignCheckFailure()).build();
        }
        // 校验核心数据
        TmallModifyAddressNotifyDTO notifyData = GsonUtil.jsonToBean(notifyRawData, TmallModifyAddressNotifyDTO.class);
        if (StringUtils.isBlank(notifyData.getBizOrderId())) {
            throw BizException.of("订单不存在");
        }
        // 初始化响应，默认不允许修改地址
        String responseStr = this.responseFailure();
        try {
            // 调用 OMS 系统接口修改地址
            this.omsNotifyModifyAddress(notifyData);
            // 响应成功
            responseStr = this.responseSuccess();
        } catch (Exception e) {
            log.warn("天猫修改地址通知 OMS 失败：{}", e.getMessage(), e);
        }
        return ReceiveLogDO.builder()
                .dataId(notifyData.getBizOrderId())
                .rawData(notifyRawData)
                .responseStr(responseStr)
                .build();
    }

    @Override
    public ResponseEntity<String> notifyResponse(String responseStr) {
        return ResponseEntity.ok().body(responseStr);
    }

    @Override
    public Object convertToNotifyBean(String data) {
        return GsonUtil.jsonToBean(data, TmallModifyAddressNotifyDTO.class);
    }

    /**
     * 调用 OMS 系统接口修改地址
     *
     * @param notifyData 通知数据
     */
    private void omsNotifyModifyAddress(TmallModifyAddressNotifyDTO notifyData) {
        OmsModifyAddressNotifyCmd cmd = OmsModifyAddressNotifyCmd.builder()
                .tradeId(notifyData.getBizOrderId())
                .receiverOaid(notifyData.getOaid())
                .receiverName(notifyData.getModifiedAddress().getName())
                .receiverMobile(notifyData.getModifiedAddress().getPhone())
                .receiverProvince(notifyData.getModifiedAddress().getProvince())
                .receiverCity(notifyData.getModifiedAddress().getCity())
                .receiverDistrict(notifyData.getModifiedAddress().getArea())
                .receiverTown(notifyData.getModifiedAddress().getTown())
                .receiverAddress(notifyData.getModifiedAddress().getAddressDetail())
                .event(SystemConstant.SYSTEM_NAME)
                .build();
        omsRpc.notifyModifyAddress(cmd);
    }

    /**
     * 解析通知数据
     *
     * @param shop    店铺信息
     * @param request 请求参数
     * @return 通知数据
     * @throws Exception 异常
     */
    private String parseNotifyRawData(ShopDO shop, HttpServletRequest request) throws Exception {
        CheckResult checkResult = SpiUtils.checkSign(request, shop.getAppSecret());
        log.info("解析通知数据 {}", GsonUtil.objectToJson(checkResult));
        if (BooleanUtils.isFalse(checkResult.isSuccess())) {
            throw BizException.of("数据校验不合法");
        }
        return checkResult.getRequestBody();
    }

    /**
     * 成功响应
     *
     * @return 成功响应
     */
    private String responseSuccess() {
        return this.buildResponse(true, null, null);
    }

    /**
     * 失败响应
     *
     * @return 失败响应
     */
    private String responseFailure() {
        return this.buildResponse(false, "3002", "系统异常 (平台不可重试)");
    }

    /**
     * 验签失败响应
     *
     * @return 验签失败响应
     */
    private String responseSignCheckFailure() {
        return this.buildResponse(false, "sign-check-failure", "Illegal request");
    }

    /**
     * 构建响应
     *
     * @param success   成功标识
     * @param errorCode 错误码
     * @param errorMsg  错误详细描述信息
     * @return 响应
     */
    private String buildResponse(boolean success, String errorCode, String errorMsg) {
        JsonObject result = new JsonObject();
        result.addProperty("success", success);
        result.addProperty("errorCode", errorCode);
        result.addProperty("errorMsg", errorMsg);
        JsonObject response = new JsonObject();
        response.add("result", result);
        return response.toString();
    }

}
