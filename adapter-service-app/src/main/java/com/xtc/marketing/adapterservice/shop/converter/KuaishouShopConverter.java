package com.xtc.marketing.adapterservice.shop.converter;

import com.kuaishou.merchant.open.api.domain.express.GetEbillOrderRequest;
import com.kuaishou.merchant.open.api.domain.express.ItemDTO;
import com.kuaishou.merchant.open.api.domain.order.*;
import com.kuaishou.merchant.open.api.domain.refund.MerchantRefundDetailDataView;
import com.kuaishou.merchant.open.api.domain.refund.MerchantRefundInfoView;
import com.kuaishou.merchant.open.api.request.express.OpenExpressEbillGetRequest;
import com.xtc.marketing.adapterservice.rpc.kuaishou.enums.KuaishouLogisticsCompany;
import com.xtc.marketing.adapterservice.shop.constant.ShopOrderConstant;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.OrderDTO;
import com.xtc.marketing.adapterservice.shop.dto.OrderItemDTO;
import com.xtc.marketing.adapterservice.shop.dto.RefundDTO;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsCargoCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsOrderCreateCmd;
import com.xtc.marketing.adapterservice.util.DateUtil;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Mapper(
        componentModel = "spring",
        uses = {BaseShopConverter.class},
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface KuaishouShopConverter {

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "orderBaseInfo.oid")
    @Mapping(target = "orderState", source = "orderBaseInfo.status")
    @Mapping(target = "stepOrderState", source = "orderStepInfo.orderPaymentStatus.orderPayStatus")
    @Mapping(target = "stepOrderStateDesc", source = "orderStepInfo.orderPaymentStatus.payStepType")
    @Mapping(target = "riskState", expression = "java(source.getOrderBaseInfo().getRiskCode() != null && source.getOrderBaseInfo().getRiskCode() == 10001)")
    @Mapping(target = "sellerId", source = "orderBaseInfo.sellerOpenId")
    @Mapping(target = "sellerName", source = "orderBaseInfo.sellerNick")
    @Mapping(target = "sellerMemo", source = "orderNote.orderNoteInfo", qualifiedByName = "toSellerMemo")
    @Mapping(target = "buyerId", source = "orderBaseInfo.buyerOpenId")
    @Mapping(target = "buyerName", source = "orderBaseInfo.buyerNick")
    @Mapping(target = "buyerMemo", source = "orderBaseInfo.remark")
    @Mapping(target = "receiverName", source = "orderAddress.desensitiseConsignee")
    @Mapping(target = "receiverMobile", source = "orderAddress.desensitiseMobile")
    @Mapping(target = "receiverProvince", source = "orderAddress.province")
    @Mapping(target = "receiverCity", source = "orderAddress.city")
    @Mapping(target = "receiverDistrict", source = "orderAddress.district")
    @Mapping(target = "receiverAddress", source = "orderAddress.desensitiseAddress")
    // 金额和时间
    @Mapping(target = "priceTotal", source = "orderBaseInfo.totalFee")
    @Mapping(target = "payment", source = "orderBaseInfo.totalFee")
    @Mapping(target = "shippingPayment", source = "orderBaseInfo.expressFee")
    @Mapping(target = "updateTime", source = "orderBaseInfo.updateTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "orderTime", source = "orderBaseInfo.createTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "payTime", source = "orderBaseInfo.payTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "latestShippingTime", source = "orderBaseInfo.validPromiseShipmentTimeStamp", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "shippingTime", source = "orderBaseInfo.sendTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "cipherTexts", expression = "java(java.util.Arrays.asList(source.getOrderAddress().getEncryptedConsignee(), source.getOrderAddress().getEncryptedMobile(), source.getOrderAddress().getEncryptedAddress()))")
    OrderDTO toAdapterOrderDTO(OrderList source);

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "orderBaseInfo.oid")
    @Mapping(target = "orderState", source = "orderBaseInfo.status")
    @Mapping(target = "stepOrderState", source = "orderStepInfo.orderPaymentStatus.orderPayStatus")
    @Mapping(target = "stepOrderStateDesc", source = "orderStepInfo.orderPaymentStatus.payStepType")
    @Mapping(target = "riskState", expression = "java(source.getOrderBaseInfo().getRiskCode() != null && source.getOrderBaseInfo().getRiskCode() == 10001)")
    @Mapping(target = "sellerId", source = "orderBaseInfo.sellerOpenId")
    @Mapping(target = "sellerName", source = "orderBaseInfo.sellerNick")
    @Mapping(target = "sellerMemo", source = "orderNote.orderNoteInfo", qualifiedByName = "toSellerMemo")
    @Mapping(target = "buyerId", source = "orderBaseInfo.buyerOpenId")
    @Mapping(target = "buyerName", source = "orderBaseInfo.buyerNick")
    @Mapping(target = "buyerMemo", source = "orderBaseInfo.remark")
    @Mapping(target = "receiverName", source = "orderAddress.desensitiseConsignee")
    @Mapping(target = "receiverMobile", source = "orderAddress.desensitiseMobile")
    @Mapping(target = "receiverProvince", source = "orderAddress.province")
    @Mapping(target = "receiverCity", source = "orderAddress.city")
    @Mapping(target = "receiverDistrict", source = "orderAddress.district")
    @Mapping(target = "receiverAddress", source = "orderAddress.desensitiseAddress")
    // 金额和时间
    @Mapping(target = "priceTotal", source = "orderBaseInfo.totalFee")
    @Mapping(target = "payment", source = "orderBaseInfo.totalFee")
    @Mapping(target = "shippingPayment", source = "orderBaseInfo.expressFee")
    @Mapping(target = "updateTime", source = "orderBaseInfo.updateTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "orderTime", source = "orderBaseInfo.createTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "payTime", source = "orderBaseInfo.payTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "latestShippingTime", source = "orderBaseInfo.validPromiseShipmentTimeStamp", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "shippingTime", source = "orderBaseInfo.sendTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "cipherTexts", expression = "java(java.util.Arrays.asList(source.getOrderAddress().getEncryptedConsignee(), source.getOrderAddress().getEncryptedMobile(), source.getOrderAddress().getEncryptedAddress()))")
    OrderDTO toAdapterOrderDTO(OrderDetail source);

    /**
     * 转换统一订单明细
     *
     * @param orderItemInfo 平台订单明细
     * @param orderNo       订单号
     * @param orderCpsInfo  平台达人信息
     * @return 统一订单明细
     */
    @Mapping(target = "orderNo", source = "orderNo")
    @Mapping(target = "itemNo", expression = "java(orderNo + \"-\" + orderItemInfo.getItemId())")
    @Mapping(target = "itemType", source = "orderItemInfo.itemType")
    @Mapping(target = "productId", source = "orderItemInfo.itemId")
    @Mapping(target = "productName", expression = "java(orderItemInfo.getItemTitle() + \"-\" + orderItemInfo.getSkuDesc())")
    @Mapping(target = "skuId", source = "orderItemInfo.skuId")
    @Mapping(target = "skuName", source = "orderItemInfo.skuDesc")
    @Mapping(target = "skuErpCode", source = "orderItemInfo.skuNick")
    @Mapping(target = "num", source = "orderItemInfo.num")
    // 金额
    @Mapping(target = "unitPrice", source = "orderItemInfo.price")
    @Mapping(target = "payment", expression = "java(toOrderItemPriceTotal(orderItemInfo))")
    @Mapping(target = "priceTotal", expression = "java(toOrderItemPriceTotal(orderItemInfo))")
    @Mapping(target = "authorId", source = "orderCpsInfo.distributorId")
    @Mapping(target = "authorName", source = "orderCpsInfo.distributorName")
    OrderItemDTO toAdapterOrderItemDTO(OrderItemInfo orderItemInfo, String orderNo, OrderCpsInfo orderCpsInfo);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "oid")
    @Mapping(target = "serviceNo", source = "refundId")
    @Mapping(target = "serviceState", source = "status")
    @Mapping(target = "serviceType", source = "refundType")
    @Mapping(target = "applyReason", source = "refundReasonDesc")
    @Mapping(target = "sellerId", source = "sellerId")
    @Mapping(target = "buyerId", source = "buyerId")
    @Mapping(target = "productId", source = "itemId")
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "skuErpCode", source = "skuNick")
    // 金额和时间
    @Mapping(target = "refundAmount", source = "refundFee")
    @Mapping(target = "payment", source = "refundFee")
    @Mapping(target = "updateTime", source = "updateTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "createTime", source = "createTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "refundTime", source = "endTime", qualifiedByName = "toLocalDateTime")
    RefundDTO toAdapterRefundDTO(MerchantRefundInfoView source);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "oid")
    @Mapping(target = "serviceNo", source = "refundId")
    @Mapping(target = "serviceState", source = "status")
    @Mapping(target = "serviceType", source = "refundType")
    @Mapping(target = "returnExpressCompany", source = "logisticsInfo.expressCode")
    @Mapping(target = "returnWaybillNo", source = "logisticsInfo.expressNo")
    @Mapping(target = "applyReason", source = "refundReasonDesc")
    @Mapping(target = "sellerId", source = "sellerId")
    @Mapping(target = "buyerId", source = "buyerOpenId")
    @Mapping(target = "productId", source = "itemId")
    @Mapping(target = "num", source = "productNum")
    // 金额和时间
    @Mapping(target = "refundAmount", source = "refundFee")
    @Mapping(target = "updateTime", source = "updateTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "createTime", source = "createTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "refundTime", source = "endTime", qualifiedByName = "toLocalDateTime")
    RefundDTO toAdapterRefundDTO(MerchantRefundDetailDataView source);

    /**
     * 转换平台电子面单参数
     *
     * @param cmd              统一电子面单参数
     * @param shop             店铺
     * @param logisticsCompany 物流公司
     * @return 平台电子面单参数
     */
    @Mapping(target = "getEbillOrderRequest", expression = "java(java.util.Collections.singletonList(toGetEbillOrderRequest(cmd, shop, logisticsCompany)))")
    OpenExpressEbillGetRequest toOpenExpressEbillGetRequest(ShopLogisticsOrderCreateCmd cmd, ShopDO shop, KuaishouLogisticsCompany logisticsCompany);

    /**
     * 转换平台电子面单参数
     *
     * @param cmd              统一电子面单参数
     * @param shop             店铺
     * @param logisticsCompany 物流公司
     * @return 平台电子面单参数
     */
    @Mapping(target = "senderContract.name", source = "cmd.senderName")
    @Mapping(target = "senderContract.mobile", source = "cmd.senderPhone")
    @Mapping(target = "senderAddress.provinceName", source = "cmd.senderProvince")
    @Mapping(target = "senderAddress.cityName", source = "cmd.senderCity")
    @Mapping(target = "senderAddress.districtName", source = "cmd.senderDistrict")
    @Mapping(target = "senderAddress.streetName", source = "cmd.senderTown")
    @Mapping(target = "senderAddress.detailAddress", source = "cmd.senderAddress")
    @Mapping(target = "receiverContract.name", source = "cmd.receiverName")
    @Mapping(target = "receiverContract.mobile", source = "cmd.receiverMobile")
    @Mapping(target = "receiverAddress.provinceName", source = "cmd.receiverProvince")
    @Mapping(target = "receiverAddress.cityName", source = "cmd.receiverCity")
    @Mapping(target = "receiverAddress.districtName", source = "cmd.receiverDistrict")
    @Mapping(target = "receiverAddress.streetName", source = "cmd.receiverTown")
    @Mapping(target = "receiverAddress.detailAddress", source = "cmd.receiverAddress")
    @Mapping(target = "orderChannel", constant = "KUAI_SHOU")
    @Mapping(target = "merchantCode", expression = "java(shop.getShopId().split(\"\\\\|\")[0])")
    @Mapping(target = "merchantName", expression = "java(shop.getShopId().split(\"\\\\|\")[1])")
    @Mapping(target = "tradeOrderCode", source = "cmd.shopOrderNo")
    @Mapping(target = "expressProductCode", source = "logisticsCompany.productCode")
    @Mapping(target = "expressCompanyCode", source = "logisticsCompany.name")
    @Mapping(target = "settleAccount", source = "logisticsCompany.account")
    @Mapping(target = "extData", source = "logisticsCompany.extData")
    @Mapping(target = "reserveTime", source = "logisticsCompany.reserveTime")
    @Mapping(target = "reserveEndTime", source = "logisticsCompany.reserveEndTime")
    @Mapping(target = "netSiteCode", source = "logisticsCompany.siteCode")
    @Mapping(target = "netSiteName", source = "logisticsCompany.siteName")
    @Mapping(target = "payMethod", constant = "1")
    @Mapping(target = "templateUrl", source = "logisticsCompany.templateUrl")
    @Mapping(target = "packageCode", expression = "java(toUniqueId())")
    @Mapping(target = "requestId", expression = "java(toUniqueId())")
    @Mapping(target = "totalPackageQuantity", constant = "1L")
    @Mapping(target = "totalPackageWeight", constant = "1.0")
    @Mapping(target = "itemList", source = "cmd.cargos")
    GetEbillOrderRequest toGetEbillOrderRequest(ShopLogisticsOrderCreateCmd cmd, ShopDO shop, KuaishouLogisticsCompany logisticsCompany);

    /**
     * 转换平台电子面单参数
     *
     * @param cmd 统一电子面单参数
     * @return 平台电子面单参数
     */
    @Mapping(target = "itemTitle", source = "name")
    @Mapping(target = "itemQuantity", source = "quantity")
    ItemDTO toItemDTO(ShopLogisticsCargoCmd cmd);

    /**
     * 转换统一订单数据时，设置特殊参数
     *
     * @param order         统一订单数据
     * @param platformOrder 平台订单数据
     */
    @AfterMapping
    default void setOrderParam(@MappingTarget OrderDTO order, OrderDetail platformOrder) {
        if (order == null) {
            return;
        }
        OrderDetailBaseInfo orderBaseInfo = platformOrder.getOrderBaseInfo();
        if (orderBaseInfo == null) {
            return;
        }
        // 识别国补订单 22：国补自销是本店铺销售的国补订单 21：国补供销是分单给供应商的国补订单
        boolean isNationalSubsidy = orderBaseInfo.getOrderLabels().stream().anyMatch(label -> label == 21 || label == 22);
        if (isNationalSubsidy) {
            order.setOrderType(ShopOrderConstant.ORDER_TYPE_NATIONAL_SUBSIDY);
            order.setOrderTypeDesc(ShopOrderConstant.ORDER_TYPE_DESC_NATIONAL_SUBSIDY);
        }
    }

    @AfterMapping
    default void setOrderParam(@MappingTarget OrderDTO order, OrderList platformOrder) {
        if (order == null) {
            return;
        }
        OrderBaseInfo orderBaseInfo = platformOrder.getOrderBaseInfo();
        if (orderBaseInfo == null) {
            return;
        }
        // 识别国补订单 22：国补自销是本店铺销售的国补订单 21：国补供销是分单给供应商的国补订单
        boolean isNationalSubsidy = orderBaseInfo.getOrderLabels().stream().anyMatch(label -> label == 21 || label == 22);
        if (isNationalSubsidy) {
            order.setOrderType(ShopOrderConstant.ORDER_TYPE_NATIONAL_SUBSIDY);
            order.setOrderTypeDesc(ShopOrderConstant.ORDER_TYPE_DESC_NATIONAL_SUBSIDY);
        }
    }

    /**
     * 时间转换
     *
     * @param time 时间戳(秒)
     * @return LocalDateTime
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(Long time) {
        if (time == null || time < 1) {
            return null;
        }
        return DateUtil.toLocalDateTime(time / 1000);
    }

    /**
     * 商家备注转换
     * <p>备注按创建时间倒序排列，取第一条数据的备注 note </p>
     *
     * @param orderNoteInfoList 商家备注
     * @return 备注信息
     */
    @Named("toSellerMemo")
    default String toSellerMemo(List<OrderNoteInfo> orderNoteInfoList) {
        return orderNoteInfoList.stream()
                .max(Comparator.comparing(OrderNoteInfo::getCreateTime))
                .map(OrderNoteInfo::getNote)
                .orElse(null);
    }

    /**
     * 计算商品金额
     *
     * @param orderItemInfo 商品信息
     * @return 商品金额
     */
    @Named("toOrderItemPriceTotal")
    default Integer toOrderItemPriceTotal(OrderItemInfo orderItemInfo) {
        int price = Optional.ofNullable(orderItemInfo.getPrice()).map(Long::intValue).orElse(0);
        int num = Optional.ofNullable(orderItemInfo.getNum()).orElse(0);
        return price * num;
    }

    /**
     * 生成唯一id
     *
     * @return 唯一id
     */
    @Named("toUniqueId")
    default String toUniqueId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

}
