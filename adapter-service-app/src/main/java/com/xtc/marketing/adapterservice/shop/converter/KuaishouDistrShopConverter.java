package com.xtc.marketing.adapterservice.shop.converter;

import com.kuaishou.merchant.open.api.domain.dropshipping.DsItemDTO;
import com.kuaishou.merchant.open.api.domain.dropshipping.DsOrderDTO;
import com.kuaishou.merchant.open.api.domain.dropshipping.DsOrderGetRequest;
import com.kuaishou.merchant.open.api.domain.dropshipping.ItemDTO;
import com.kuaishou.merchant.open.api.request.dropshipping.OpenDropshippingEbillBatchGetRequest;
import com.xtc.marketing.adapterservice.rpc.kuaishou.enums.KuaishouLogisticsCompany;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.OrderDTO;
import com.xtc.marketing.adapterservice.shop.dto.OrderItemDTO;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsCargoCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsOrderCreateCmd;
import com.xtc.marketing.adapterservice.util.DateUtil;
import org.mapstruct.*;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Mapper(
        componentModel = "spring",
        uses = {BaseShopConverter.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        builder = @Builder(disableBuilder = true),
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface KuaishouDistrShopConverter {

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    @Mapping(target = "orderNo", source = "allocateOrderCode")
    @Mapping(target = "orderState", source = "allocateStatus")
    @Mapping(target = "sellerId", source = "receiverId")
    @Mapping(target = "sellerName", source = "userName")
    @Mapping(target = "receiverName", source = "receiverId")
    @Mapping(target = "receiverMobile", source = "receiverId")
    @Mapping(target = "receiverProvince", source = "receiverAddress.provinceName")
    @Mapping(target = "receiverCity", source = "receiverAddress.cityName")
    @Mapping(target = "receiverDistrict", source = "receiverAddress.districtName")
    @Mapping(target = "receiverAddress", source = "receiverId")
    @Mapping(target = "priceTotal", constant = "-1")
    @Mapping(target = "payment", constant = "-1")
    @Mapping(target = "orderTime", source = "allocateTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "payTime", source = "allocateTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "buyerMemo", source = "orderNote.buyerRemark", qualifiedByName = "toRemark")
    OrderDTO toAdapterOrderDTO(DsOrderDTO source);

    /**
     * 转换统一订单明细
     *
     * @param orderId 订单号
     * @param source  平台订单明细
     * @return 统一订单明细
     */
    @Mapping(target = "orderNo", source = "orderId")
    @Mapping(target = "itemNo", expression = "java(orderId + \"-\" + source.getSkuId())")
    @Mapping(target = "productId", source = "source.skuId")
    @Mapping(target = "productName", source = "source.itemName")
    @Mapping(target = "skuName", source = "source.itemName")
    @Mapping(target = "skuErpCode", source = "source.skuNick")
    @Mapping(target = "num", source = "source.itemQuantity")
    @Mapping(target = "unitPrice", constant = "-1")
    @Mapping(target = "priceTotal", constant = "-1")
    @Mapping(target = "payment", constant = "-1")
    OrderItemDTO toAdapterOrderItemDTO(String orderId, DsItemDTO source);

    /**
     * 转换平台电子面单参数
     *
     * @param cmd              统一电子面单参数
     * @param shop             店铺
     * @param logisticsCompany 物流公司
     * @return 平台电子面单参数
     */
    @Mapping(target = "dsOrderGetReq", expression = "java(java.util.Collections.singletonList(toGetEbillOrderRequest(cmd, shop, logisticsCompany)))")
    OpenDropshippingEbillBatchGetRequest toOpenExpressEbillGetRequest(ShopLogisticsOrderCreateCmd cmd, ShopDO shop, KuaishouLogisticsCompany logisticsCompany);

    /**
     * 转换平台电子面单参数
     *
     * @param cmd 统一电子面单参数
     * @return 平台电子面单参数
     */
    @Mapping(target = "itemTitle", source = "name")
    @Mapping(target = "itemQuantity", source = "quantity")
    ItemDTO toItemDTO(ShopLogisticsCargoCmd cmd);

    /**
     * 时间转换
     *
     * @param epochSecond 秒
     * @return LocalDateTime
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(Long epochSecond) {
        if (epochSecond == null || epochSecond < 1) {
            return null;
        }
        return DateUtil.toLocalDateTimeFromEpochMilli(epochSecond);
    }

    /**
     * 转换平台电子面单参数
     *
     * @param cmd              统一电子面单参数
     * @param shop             店铺
     * @param logisticsCompany 物流公司
     * @return 平台电子面单参数
     */
    @Mapping(target = "allocateOrderCode", source = "cmd.shopOrderNo")
    @Mapping(target = "senderContract.name", source = "cmd.senderName")
    @Mapping(target = "senderContract.mobile", source = "cmd.senderPhone")
    @Mapping(target = "senderAddress.provinceName", source = "cmd.senderProvince")
    @Mapping(target = "senderAddress.cityName", source = "cmd.senderCity")
    @Mapping(target = "senderAddress.districtName", source = "cmd.senderDistrict")
    @Mapping(target = "senderAddress.streetName", source = "cmd.senderTown")
    @Mapping(target = "senderAddress.detailAddress", source = "cmd.senderAddress")
    @Mapping(target = "orderChannel", constant = "KUAI_SHOU")
    @Mapping(target = "userName", expression = "java(shop.getAgentCode().split(\"\\\\|\")[1])")
    @Mapping(target = "userCode", expression = "java(shop.getAgentCode().split(\"\\\\|\")[0])")
    @Mapping(target = "expressProductCode", source = "logisticsCompany.productCode")
    @Mapping(target = "expressCompanyCode", source = "logisticsCompany.name")
    @Mapping(target = "settleAccount", source = "logisticsCompany.account")
    @Mapping(target = "extData", source = "logisticsCompany.extData")
    @Mapping(target = "reserveTime", source = "logisticsCompany.reserveTime")
    @Mapping(target = "reserveEndTime", source = "logisticsCompany.reserveEndTime")
    @Mapping(target = "netSiteCode", source = "logisticsCompany.siteCode")
    @Mapping(target = "netSiteName", source = "logisticsCompany.siteName")
    @Mapping(target = "payMethod", constant = "1")
    @Mapping(target = "templateUrl", source = "logisticsCompany.templateUrl")
    @Mapping(target = "packageCode", expression = "java(toUniqueId())")
    @Mapping(target = "requestId", expression = "java(toUniqueId())")
    @Mapping(target = "totalPackageQuantity", constant = "1L")
    @Mapping(target = "totalPackageWeight", constant = "1.0")
    @Mapping(target = "itemList", source = "cmd.cargos")
    DsOrderGetRequest toGetEbillOrderRequest(ShopLogisticsOrderCreateCmd cmd, ShopDO shop, KuaishouLogisticsCompany logisticsCompany);

    /**
     * 时间转换
     *
     * @param remark 秒
     * @return LocalDateTime
     */
    @Named("toRemark")
    default String toRemark(List<String> remark) {
        if (CollectionUtils.isEmpty(remark)) {
            return null;
        }
        return String.join(",", remark);
    }

    /**
     * 生成唯一id
     *
     * @return 唯一id
     */
    @Named("toUniqueId")
    default String toUniqueId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

}