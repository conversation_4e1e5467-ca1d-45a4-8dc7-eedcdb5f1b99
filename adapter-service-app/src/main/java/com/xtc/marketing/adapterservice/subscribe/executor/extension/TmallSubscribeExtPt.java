package com.xtc.marketing.adapterservice.subscribe.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.taobao.api.internal.tmc.TmcClient;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.subscribe.ability.client.TmallTmcClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = SubscribeExtConstant.USE_CASE, scenario = SubscribeExtConstant.SCENARIO_TMALL)
public class TmallSubscribeExtPt implements SubscribeExtPt {

    private final TmallTmcClient tmallTmcClient;

    @Override
    public String connect(ShopDO shop, String bizParam) {
        TmcClient tmcClient = tmallTmcClient.getTmcClient(shop, bizParam);
        if (tmcClient == null) {
            throw BizException.of("tmc-client 创建失败");
        }

        // 未连接状态才能发起连接
        if (BooleanUtils.isNotTrue(tmcClient.isOnline())) {
            try {
                // "ws://premc.api.taobao.com/"    // 预发环境
                // "ws://mc.api.taobao.com/"       // 生产环境
                // 连接失败时会抛出异常，请不要捕获，避免未连接而不知晓，第一次启动成功后，后续断开会自动重连
                tmcClient.connect(TmallTmcClient.CONNECT_URL);
            } catch (Exception e) {
                throw BizException.of("tmc-client 连接失败 message: " + e.getMessage(), e);
            }
        }

        log.info("api调用地址 getApiUrl: {}", tmcClient.getApiUrl());
        log.info("开启自动确认 isUseDefaultConfirm: {}", tmcClient.isUseDefaultConfirm());
        return String.format("tmc-client 在线状态 isOnline: %s", tmcClient.isOnline());
    }

    @Override
    public String close(ShopDO shop) {
        TmcClient tmcClient = tmallTmcClient.removeTmcClient(shop);
        boolean isOnline = Optional.ofNullable(tmcClient).map(TmcClient::isOnline).orElse(false);
        if (isOnline) {
            tmcClient.close();
            isOnline = tmcClient.isOnline();
        }
        return String.format("tmc-client 在线状态 isOnline: %s", isOnline);
    }

}
