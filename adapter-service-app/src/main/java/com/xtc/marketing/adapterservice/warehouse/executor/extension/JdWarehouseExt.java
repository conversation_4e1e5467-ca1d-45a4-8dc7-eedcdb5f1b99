package com.xtc.marketing.adapterservice.warehouse.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.JdlOpenPlatformSoService.querySoOrder.SoQueryResponse;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.JdlOpenPlatformStockService.queryWarehouseStockMergeByWarehouse.StockSummaryRequest;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.JdlOpenPlatformStockService.queryWarehouseStockMergeByWarehouse.StockSummaryResult;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.JdlOpenPlatformStockService.searchShopStockFlow.JdlOpenPage;
import com.lop.open.api.sdk.request.IntegratedSupplyChain.IntegratedsupplychainOrderDeliveryQueryV1LopRequest;
import com.lop.open.api.sdk.request.IntegratedSupplyChain.IntegratedsupplychainStockFlowShopstockQueryV1LopRequest;
import com.lop.open.api.sdk.request.IntegratedSupplyChain.IntegratedsupplychainStockmergeQueryV1LopRequest;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.jd.JdWarehouseRpc;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.executor.query.ShopGetQryExe;
import com.xtc.marketing.adapterservice.warehouse.converter.JdWarehouseConverter;
import com.xtc.marketing.adapterservice.warehouse.dto.OutboundDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.OutboundDetailDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.WarehouseStockDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.command.InboundApplyCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.InboundCancelCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.OutboundApplyCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.OutboundCancelCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.query.OutboundDetailQry;
import com.xtc.marketing.adapterservice.warehouse.dto.query.OutboundPageQry;
import com.xtc.marketing.adapterservice.warehouse.dto.query.WarehouseStockQry;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 京东仓库扩展点
 */
@Slf4j
@RequiredArgsConstructor
@Extension(useCase = WarehouseExtConstant.USE_CASE, scenario = WarehouseExtConstant.SCENARIO_JD)
public class JdWarehouseExt implements WarehouseExtPt {

    private final JdWarehouseRpc jdWarehouseRpc;
    private final JdWarehouseConverter jdWarehouseConverter;
    private final ShopGetQryExe shopGetQryExe;

    @Override
    public PageResponse<OutboundDTO> pageOutbound(OutboundPageQry qry) {
        ShopDO shop = shopGetQryExe.execute(qry.getShopCode());
        IntegratedsupplychainStockFlowShopstockQueryV1LopRequest request =
                jdWarehouseConverter.toIntegratedsupplychainStockFlowShopstockQueryV1LopRequest(shop, qry);
        JdlOpenPage jdlOpenPage = jdWarehouseRpc.pageOutbound(shop, request);
        if (jdlOpenPage == null || jdlOpenPage.getResultList() == null) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }
        List<OutboundDTO> outbounds = jdWarehouseConverter.toOutboundDTO(jdlOpenPage.getResultList());
        return PageResponse.of(outbounds, jdlOpenPage.getTotalNum(), qry.getPageSize(), qry.getPageIndex());
    }

    @Override
    public OutboundDetailDTO getOutboundDetail(OutboundDetailQry qry) {
        ShopDO shop = shopGetQryExe.execute(qry.getShopCode());
        IntegratedsupplychainOrderDeliveryQueryV1LopRequest request =
                jdWarehouseConverter.toIntegratedsupplychainOrderDeliveryQueryV1LopRequest(shop, qry);
        SoQueryResponse soQueryResponse = jdWarehouseRpc.getOutboundDetail(shop, request);
        return jdWarehouseConverter.toOutboundDetails(soQueryResponse);
    }

    @Override
    public List<WarehouseStockDTO> queryStocks(WarehouseStockQry qry) {
        ShopDO shop = shopGetQryExe.execute(qry.getShopCode());
        IntegratedsupplychainStockmergeQueryV1LopRequest request = new IntegratedsupplychainStockmergeQueryV1LopRequest();
        StockSummaryRequest param = new StockSummaryRequest();
        param.setOwnerNo(qry.getWarehouseCode());
        // 商家 sku ，京东平台限制最多50
        param.setGoodsNoList(qry.getSkuIds());
        request.setRequest(param);
        StockSummaryResult result = jdWarehouseRpc.queryStocks(shop, request);
        if (result == null || CollectionUtils.isEmpty(result.getWarehouseStockList())) {
            return Collections.emptyList();
        }
        return jdWarehouseConverter.toWarehouseStockDTO(result.getWarehouseStockList());
    }

    @Override
    public String applyInbound(InboundApplyCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public String cancelInbound(InboundCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public String applyOutbound(OutboundApplyCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public String cancelOutbound(OutboundCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

}
