package com.xtc.marketing.adapterservice.subscribe.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.pdd.pop.sdk.message.WsClient;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.subscribe.ability.client.PddWsClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = SubscribeExtConstant.USE_CASE, scenario = SubscribeExtConstant.SCENARIO_PDD)
public class PddSubscribeExtPt implements SubscribeExtPt {

    private final PddWsClient pddWsClient;

    @Override
    public String connect(ShopDO shop, String bizParam) {
        WsClient client = pddWsClient.getWsClient(shop, bizParam);
        if (client == null) {
            throw BizException.of("WsClient 创建失败");
        }
        // 未连接状态才能发起连接
        if (BooleanUtils.isNotTrue(client.isOnline())) {
            try {
                client.connect();
            } catch (Exception e) {
                throw BizException.of("WsClient 连接失败 message: " + e.getMessage(), e);
            }
        }
        return String.format("WsClient 在线状态 isOnline: %s", client.isOnline());
    }

    @Override
    public String close(ShopDO shop) {
        WsClient client = pddWsClient.removeWsClient(shop);
        boolean isOnline = Optional.ofNullable(client).map(WsClient::isOnline).orElse(false);
        if (isOnline) {
            client.close();
            isOnline = client.isOnline();
        }
        return String.format("WsClient 在线状态 isOnline: %s", isOnline);
    }

}
