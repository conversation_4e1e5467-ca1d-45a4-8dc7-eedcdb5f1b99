package com.xtc.marketing.adapterservice.invoice;

import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.xtc.marketing.adapterservice.invoice.dto.InvoiceResultDTO;
import com.xtc.marketing.adapterservice.invoice.dto.command.InvoiceCreateCmd;
import com.xtc.marketing.adapterservice.invoice.dto.command.InvoiceCreateRedCmd;
import com.xtc.marketing.adapterservice.invoice.dto.command.InvoiceSerialNoCmd;
import com.xtc.marketing.adapterservice.invoice.dto.query.InvoiceResultQry;
import com.xtc.marketing.adapterservice.invoice.enums.InvoicePlatform;
import com.xtc.marketing.adapterservice.invoice.extension.InvoiceExtConstant;
import com.xtc.marketing.adapterservice.invoice.extension.InvoiceExtPt;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Function;

@RequiredArgsConstructor
@Service("invoiceService")
public class InvoiceServiceImpl implements InvoiceService {

    private final ExtensionExecutor extensionExecutor;

    @Override
    public boolean createInvoice(InvoiceCreateCmd cmd) {
        return extensionExecutor(cmd.getInvoicePlatform(), executor -> executor.createInvoice(cmd));
    }

    @Override
    public boolean createRedInvoice(InvoiceCreateRedCmd cmd) {
        return extensionExecutor(cmd.getInvoicePlatform(), executor -> executor.createRedInvoice(cmd));
    }

    @Override
    public List<InvoiceResultDTO> listInvoiceResult(InvoiceResultQry qry) {
        return extensionExecutor(qry.getInvoicePlatform(), executor -> executor.listInvoiceResult(qry));
    }

    @Override
    public String createSerialNo(InvoiceSerialNoCmd cmd) {
        return extensionExecutor(cmd.getInvoicePlatform(), executor -> executor.createSerialNo(cmd.getShopCode()));
    }

    /**
     * 执行扩展点
     *
     * @param invoicePlatform 发票
     * @param function        扩展点方法
     * @param <R>             返回值类型
     * @return 返回值
     */
    private <R> R extensionExecutor(InvoicePlatform invoicePlatform, Function<InvoiceExtPt, R> function) {
        BizScenario bizScenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID,
                InvoiceExtConstant.USE_CASE, invoicePlatform.name());
        return extensionExecutor.execute(InvoiceExtPt.class, bizScenario, function);
    }

}
