package com.xtc.marketing.adapterservice.notify.executor.command;

import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.xtc.marketing.adapterservice.exception.BizErrorCode;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.notify.dataobject.ReceiveLogDO;
import com.xtc.marketing.adapterservice.notify.dto.command.NotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.enums.NotifyEnum;
import com.xtc.marketing.adapterservice.notify.executor.extension.NotifyExtPt;
import com.xtc.marketing.adapterservice.notify.repository.ReceiveLogRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Function;

@Slf4j
@RequiredArgsConstructor
@Component
public class NotifyReceiveDataCmdExe {

    private final ExtensionExecutor extensionExecutor;
    private final ReceiveLogRepository receiveLogRepository;

    public ResponseEntity<String> execute(NotifyEnum notify, NotifyReceiveCmd cmd) {
        try {
            BizScenario scenario = BizScenario.valueOf(notify.getPlatformCode(), notify.getModuleCode(), notify.getScenarioCode());
            // 生成接收记录
            ReceiveLogDO receiveLog = this.extensionExecutor(scenario, exe -> exe.createReceiveLog(notify, cmd));
            // 不保存接收记录生成失败的数据，使用接收记录里的响应结果
            if (receiveLog == null || StringUtils.isBlank(receiveLog.getDataId())) {
                String responseStr = Optional.ofNullable(receiveLog).map(ReceiveLogDO::getResponseStr).orElse(null);
                log.warn("生成接收记录失败 response: {}", responseStr);
                return this.extensionExecutor(scenario, exe -> exe.notifyResponse(responseStr));
            }
            // 设置默认的定位数据，并保存接收记录
            String platformCode = StringUtils.defaultIfBlank(receiveLog.getPlatformCode(), notify.getPlatformCode());
            receiveLog.setPlatformCode(platformCode);
            String moduleCode = StringUtils.defaultIfBlank(receiveLog.getModuleCode(), notify.getModuleCode());
            receiveLog.setModuleCode(moduleCode);
            String scenarioCode = StringUtils.defaultIfBlank(receiveLog.getScenarioCode(), notify.getScenarioCode());
            receiveLog.setScenarioCode(scenarioCode);
            receiveLogRepository.save(receiveLog);
            // 返回响应结果
            return this.extensionExecutor(scenario, exe -> exe.notifyResponse(receiveLog.getResponseStr()));
        } catch (Exception e) {
            throw BizException.of(BizErrorCode.B_NOTIFY_ReceiveFailed, e.getMessage(), e);
        }
    }

    /**
     * 执行扩展点
     *
     * @param function 扩展点方法
     * @param <R>      返回值类型
     * @return 返回值
     */
    private <R> R extensionExecutor(BizScenario scenario, Function<NotifyExtPt, R> function) {
        return extensionExecutor.execute(NotifyExtPt.class, scenario, function);
    }

}
