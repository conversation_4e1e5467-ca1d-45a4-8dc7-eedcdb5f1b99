package com.xtc.marketing.adapterservice.logistics.executor.extension;

import com.alibaba.cola.extension.ExtensionPointI;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsCloudPrintDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsOrderDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCancelOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCloudPrintCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsInterceptCmd;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsOrderGetQry;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsRouteListQry;

import java.util.List;

/**
 * 物流扩展点
 */
public interface LogisticsExtPt extends ExtensionPointI {

    /**
     * 查询快递路由
     *
     * @param account 物流账号
     * @param qry     参数
     * @return 快递路由
     */
    List<LogisticsRouteDTO> routes(LogisticsAccountDO account, LogisticsRouteListQry qry);

    /**
     * 下物流单并生成运单号
     *
     * @param account 物流账号
     * @param cmd     参数
     * @return 订单详情
     */
    LogisticsOrderDTO createOrder(LogisticsAccountDO account, LogisticsCreateOrderCmd cmd);

    /**
     * 查询物流订单
     *
     * @param account 物流账号
     * @param qry     参数
     * @return 物流订单
     */
    String getOrder(LogisticsAccountDO account, LogisticsOrderGetQry qry);

    /**
     * 查询运单号
     *
     * @param account 物流账号
     * @param qry     参数
     * @return 运单号
     */
    String getWaybillNo(LogisticsAccountDO account, LogisticsOrderGetQry qry);

    /**
     * 取消订单
     *
     * @param account 物流账号
     * @param cmd     参数
     * @return 执行结果
     */
    boolean cancelOrder(LogisticsAccountDO account, LogisticsCancelOrderCmd cmd);

    /**
     * 拦截物流
     *
     * @param account 物流账号
     * @param cmd     参数
     * @return 执行结果
     */
    boolean intercept(LogisticsAccountDO account, LogisticsInterceptCmd cmd);

    /**
     * 面单云打印
     *
     * @param account 物流账号
     * @param cmd     参数
     * @return 云打印数据
     */
    LogisticsCloudPrintDTO cloudPrint(LogisticsAccountDO account, LogisticsCloudPrintCmd cmd);

}
