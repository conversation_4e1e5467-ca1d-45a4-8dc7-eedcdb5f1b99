package com.xtc.marketing.adapterservice.shop.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.google.common.collect.ImmutableBiMap;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.jd.open.api.sdk.domain.jinsuanpan.FinInvoiceApplyOrderProvider.request.list.ApplyOrderJosQueryParam;
import com.jd.open.api.sdk.domain.jinsuanpan.FinInvoiceApplyOrderProvider.response.order.ApplyOrderVO;
import com.jd.open.api.sdk.domain.jinsuanpan.FinInvoiceOwnProvider.response.amount.OrderShouldInvoiceAmount;
import com.jd.open.api.sdk.domain.mall.SubsidyOutUploadJsfService.request.insert.SubsidySkuAuthParam;
import com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.get.ItemInfo;
import com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.get.OrderSearchInfo;
import com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.search.OrderListResult;
import com.jd.open.api.sdk.domain.order.OrderShipmentService.response.shipment.OperatorResult;
import com.jd.open.api.sdk.domain.refundapply.RefundApplySoaService.response.queryById.RefundApplyVo;
import com.jd.open.api.sdk.domain.refundapply.RefundApplySoaService.response.queryPageList.QueryResult;
import com.jd.open.api.sdk.domain.shangjiashouhou.ServiceQueryProvider.response.view.ServiceBill;
import com.jd.open.api.sdk.request.etms.LdopWaybillReceiveRequest;
import com.jd.open.api.sdk.request.evaluation.PopPopCommentJsfServiceGetVenderCommentsForJosRequest;
import com.jd.open.api.sdk.request.jinsuanpan.PopCinvoiceApplyOrderRequest;
import com.jd.open.api.sdk.request.jinsuanpan.PopInvoiceSelfApplyRequest;
import com.jd.open.api.sdk.request.mall.DigitalSubsidyUploadSnInsertRequest;
import com.jd.open.api.sdk.request.order.PopOrderModifyVenderRemarkRequest;
import com.jd.open.api.sdk.request.order.PopOrderSearchRequest;
import com.jd.open.api.sdk.request.order.PopOrderShipmentRequest;
import com.jd.open.api.sdk.request.refundapply.PopAfsSoaRefundapplyQueryPageListRequest;
import com.jd.open.api.sdk.request.shangjiashouhou.AscReceiveRegisterRequest;
import com.jd.open.api.sdk.response.evaluation.PopPopCommentJsfServiceGetVenderCommentsForJosResponse;
import com.jd.open.api.sdk.response.shangjiashouhou.AscQueryViewResponse;
import com.xtc.marketing.adapterservice.exception.BizErrorCode;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.jd.JdRpc;
import com.xtc.marketing.adapterservice.rpc.jd.jddto.JdRefreshTokenDTO;
import com.xtc.marketing.adapterservice.rpc.jd.jddto.PrintDataEncryptDTO;
import com.xtc.marketing.adapterservice.rpc.jd.jddto.enums.JdLogisticsCompany;
import com.xtc.marketing.adapterservice.rpc.jd.jddto.invoiceapply.PageMO;
import com.xtc.marketing.adapterservice.rpc.jd.jddto.invoiceapply.ShopCinvoiceApplyOrderListRequest;
import com.xtc.marketing.adapterservice.rpc.jd.jddto.request.LdopAlphaWaybillReceiveRequest;
import com.xtc.marketing.adapterservice.shop.converter.JdShopConverter;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.dto.query.*;
import com.xtc.marketing.adapterservice.shop.util.SkuUtil;
import com.xtc.marketing.adapterservice.util.BeanCopier;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.MoneyUtil;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = ShopExtConstant.USE_CASE, scenario = ShopExtConstant.SCENARIO_JD)
public class JdShopExt implements ShopExtPt {

    private final JdRpc jdRpc;
    private final JdShopConverter jdShopConverter;
    /**
     * 条码上传平台类型和店铺代码映射
     */
    private static final Map<Integer, List<String>> SHOP_CODE_GROUPING = ImmutableBiMap.of(
            0, ImmutableList.of("JD_SELF_XTC", "JD_SELF_BBK"),
            1, ImmutableList.of("JD_XTC", "JD_BBK")
    );

    @Override
    public ShopDO getShopToken(ShopDO shop) {
        // 初始化
        ShopDO newToken = BeanCopier.copy(shop, ShopDO::new);

        // 刷新 AccessToken
        JdRefreshTokenDTO jdRefreshTokenDTO = jdRpc.refreshToken(shop);
        if (jdRefreshTokenDTO == null) {
            return newToken;
        }

        // 设置新的 token 数据
        newToken.setAppAccessToken(jdRefreshTokenDTO.getAccessToken());
        newToken.setAppRefreshToken(jdRefreshTokenDTO.getRefreshToken());
        newToken.setAppExpireTime(LocalDateTime.now().plusSeconds(jdRefreshTokenDTO.getExpiresIn()));
        return newToken;
    }

    @Override
    public PageResponse<OrderDTO> pageOrders(ShopDO shop, OrderPageQry qry) {
        PopOrderSearchRequest request = new PopOrderSearchRequest();
        request.setStartDate(DateUtil.toString(qry.getUpdateTimeStart()));
        request.setEndDate(DateUtil.toString(qry.getUpdateTimeEnd()));
        request.setPage(String.valueOf(qry.getPageIndex()));
        request.setPageSize(String.valueOf(qry.getPageSize()));

        OrderListResult orderListResult = jdRpc.pageOrders(shop, request);
        if (orderListResult == null || CollectionUtils.isEmpty(orderListResult.getOrderInfoList())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }

        List<OrderDTO> convertOrders = orderListResult.getOrderInfoList().stream()
                .map(this::splitSkuIdsAndConvertToAdapterOrderDTO)
                .collect(Collectors.toList());
        return PageResponse.of(convertOrders, orderListResult.getOrderTotal(), qry.getPageSize(), qry.getPageIndex());
    }

    @Override
    public OrderDTO getOrder(ShopDO shop, OrderGetQry qry) {
        OrderSearchInfo order = jdRpc.getOrder(shop, qry.getOrderNo());
        return Optional.ofNullable(order)
                .map(originOrder -> {
                    OrderDTO orderDTO = this.splitSkuIdsAndConvertToAdapterOrderDTO(originOrder);
                    orderDTO.setOriginOrderData(GsonUtil.objectToJson(originOrder));
                    return orderDTO;
                })
                .orElse(null);
    }

    @Override
    public boolean orderShipping(ShopDO shop, OrderShippingCmd cmd) {
        boolean hasShipping = this.checkOrderHasShipping(shop, cmd.getOrderNo());
        if (hasShipping) {
            return true;
        }
        PopOrderShipmentRequest request = new PopOrderShipmentRequest();
        request.setOrderId(Long.parseLong(cmd.getOrderNo()));
        request.setLogiNo(cmd.getWaybillNo());

        JdLogisticsCompany logisticsCompany = JdLogisticsCompany.valueOf(cmd.getLogisticsCompany().name());
        request.setLogiCoprId(logisticsCompany.getProviderId());

        OperatorResult operatorResult = jdRpc.orderShipping(shop, request);
        if (operatorResult.getSuccess()) {
            return true;
        }
        throw BizException.of("订单发货失败 " + GsonUtil.objectToJson(operatorResult));
    }

    @Override
    public boolean orderShippingCancel(ShopDO shop, OrderShippingCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean orderDummyShipping(ShopDO shop, OrderDummyShippingCmd cmd) {
        boolean hasShipping = this.checkOrderHasShipping(shop, cmd.getOrderNo());
        if (hasShipping) {
            return true;
        }
        return jdRpc.orderDummyShipping(shop, cmd.getOrderNo());
    }

    @Override
    public boolean orderRemark(ShopDO shop, OrderRemarkCmd cmd) {
        PopOrderModifyVenderRemarkRequest request = new PopOrderModifyVenderRemarkRequest();
        request.setOrderId(Long.parseLong(cmd.getOrderNo()));
        request.setFlag(4);
        request.setRemark(cmd.getRemark());
        return jdRpc.orderRemark(shop, request);
    }

    @Override
    public OrderDecryptDTO orderDecrypt(ShopDO shop, OrderDecryptCmd cmd) {
        OrderDecryptDTO decrypt = OrderDecryptDTO.builder()
                .orderNo(cmd.getOrderNo())
                .decryptTexts(Maps.newHashMapWithExpectedSize(cmd.getCiphers().size()))
                .build();
        cmd.getCiphers().forEach(cipher -> {
            // 密文集合里传订单号，则说明要解密订单的手机号数据，默认解密文本
            if (cipher.equals(cmd.getOrderNo())) {
                OrderDecryptDTO decryptMobile = jdRpc.decryptMobile(shop, cipher);
                decrypt.setPhone(decryptMobile.getPhone());
                decrypt.setMobile(decryptMobile.getMobile());
                decrypt.setMobileExpireTime(decryptMobile.getMobileExpireTime());
            } else {
                jdRpc.decryptText(shop, cipher).ifPresent(decryptText -> decrypt.getDecryptTexts().put(cipher, decryptText));
            }
        });
        return decrypt;
    }

    @Override
    public PageResponse<InvoiceApplyDTO> pageInvoiceApply(ShopDO shop, InvoiceApplyPageQry qry) {
        ShopCinvoiceApplyOrderListRequest request = new ShopCinvoiceApplyOrderListRequest();
        ApplyOrderJosQueryParam param = new ApplyOrderJosQueryParam();
        param.setPageIndex(qry.getPageIndex());
        param.setPageSize(qry.getPageSize());
        param.setApplyTimeStart(DateUtil.toDate(qry.getStartTime()));
        param.setApplyTimeEnd(DateUtil.toDate(qry.getEndTime()));
        request.setParam(param);
        PageMO page = jdRpc.pageInvoiceApply(shop, request);
        if (page == null || CollectionUtils.isEmpty(page.getList())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }
        List<InvoiceApplyDTO> invoiceApply = page.getList().stream()
                .map(apply -> {
                    jdRpc.decryptText(shop, apply.getConsumerAddress()).ifPresent(apply::setConsumerAddress);
                    jdRpc.decryptText(shop, apply.getConsumerBankAccount()).ifPresent(apply::setConsumerBankAccount);
                    jdRpc.decryptText(shop, apply.getConsumerBankName()).ifPresent(apply::setConsumerBankName);
                    jdRpc.decryptText(shop, apply.getConsumerPhone()).ifPresent(apply::setConsumerPhone);
                    jdRpc.decryptText(shop, apply.getConsumerTaxId()).ifPresent(apply::setConsumerTaxId);
                    return jdShopConverter.toAdapterInvoiceApplyDTO(apply);
                })
                .collect(Collectors.toList());
        return PageResponse.of(invoiceApply, page.getTotalCount().intValue(), page.getPageIndex(), page.getPageSize());
    }

    @Override
    public InvoiceApplyDTO getInvoiceApply(ShopDO shop, InvoiceApplyGetQry qry) {
        // 查询发票申请
        PopCinvoiceApplyOrderRequest request = new PopCinvoiceApplyOrderRequest();
        request.setOrderId(Long.valueOf(qry.getOrderNo()));
        ApplyOrderVO apply = jdRpc.getInvoiceApply(shop, request);
        // 解密发票申请
        if (apply != null) {
            jdRpc.decryptText(shop, apply.getConsumerAddress()).ifPresent(apply::setConsumerAddress);
            jdRpc.decryptText(shop, apply.getConsumerBankAccount()).ifPresent(apply::setConsumerBankAccount);
            jdRpc.decryptText(shop, apply.getConsumerBankName()).ifPresent(apply::setConsumerBankName);
            jdRpc.decryptText(shop, apply.getConsumerPhone()).ifPresent(apply::setConsumerPhone);
            jdRpc.decryptText(shop, apply.getInvoiceTitle()).ifPresent(apply::setInvoiceTitle);
        }
        return jdShopConverter.toAdapterInvoiceApplyDTO(apply);
    }

    @Override
    public boolean uploadInvoiceFile(ShopDO shop, OrderUploadInvoiceCmd cmd, MultipartFile invoiceFile) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceBase64(ShopDO shop, InvoiceUploadCmd cmd) {
        PopInvoiceSelfApplyRequest request = jdShopConverter.toInvoiceApplyRequest(cmd);
        return jdRpc.uploadInvoiceBase64(shop, request);
    }

    @Override
    public InvoiceAmountDTO getInvoiceAmount(ShopDO shop, String orderNo) {
        OrderShouldInvoiceAmount invoiceAmount = jdRpc.getInvoiceAmount(shop, orderNo);

        Integer shouldInvoiceAmount = Optional.ofNullable(invoiceAmount)
                .map(OrderShouldInvoiceAmount::getShouldInvoiceAmount)
                .map(MoneyUtil::yuanToCent)
                .orElse(null);

        return InvoiceAmountDTO.builder()
                .shouldInvoiceAmount(shouldInvoiceAmount)
                .originData(GsonUtil.objectToJson(invoiceAmount))
                .build();
    }

    @Override
    public PageResponse<CommentDTO> pageComments(ShopDO shop, CommentPageQry qry) {
        PopPopCommentJsfServiceGetVenderCommentsForJosRequest request = new PopPopCommentJsfServiceGetVenderCommentsForJosRequest();
        request.setPage(qry.getPageIndex());
        request.setPageSize(qry.getPageSize());
        request.setBeginTime(DateUtil.toString(qry.getCreateTimeStart()));
        request.setEndTime(DateUtil.toString(qry.getCreateTimeEnd()));
        PopPopCommentJsfServiceGetVenderCommentsForJosResponse response = jdRpc.pageComments(shop, request);
        if (response == null || CollectionUtils.isEmpty(response.getComments())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }

        // 订单号密文解密
        response.getComments().forEach(commentDTO ->
                jdRpc.decryptText(shop, commentDTO.getEncryptOrderId()).map(Long::valueOf).ifPresent(commentDTO::setOrderId));

        List<CommentDTO> listCommentDTO = jdShopConverter.toAdapterCommentDTO(response.getComments());
        return PageResponse.of(listCommentDTO, response.getTotalItem(), qry.getPageSize(), qry.getPageIndex());
    }

    @Override
    public PageResponse<RefundDTO> pageRefunds(ShopDO shop, RefundPageQry qry) {
        PopAfsSoaRefundapplyQueryPageListRequest request = new PopAfsSoaRefundapplyQueryPageListRequest();
        request.setPageIndex(qry.getPageIndex());
        request.setPageSize(qry.getPageSize());
        request.setCheckTimeStart(DateUtil.toString(qry.getUpdateTimeStart()));
        request.setCheckTimeEnd(DateUtil.toString(qry.getUpdateTimeEnd()));
        request.setOrderId(qry.getOrderNo());

        QueryResult queryResult = jdRpc.pageRefunds(shop, request);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getResult())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }

        List<RefundDTO> listRefundDTO = jdShopConverter.toAdapterRefundDTO(queryResult.getResult());
        return PageResponse.of(listRefundDTO, queryResult.getTotalCount().intValue(), qry.getPageSize(), qry.getPageIndex());
    }

    @Override
    public RefundDTO getRefund(ShopDO shop, RefundGetQry qry) {
        // 查询退款单
        com.jd.open.api.sdk.domain.refundapply.RefundApplySoaService.response.queryById.QueryResult refund =
                jdRpc.getRefund(shop, qry.getRefundId());
        if (refund != null && CollectionUtils.isNotEmpty(refund.getResult())) {
            RefundApplyVo source = refund.getResult().get(0);
            RefundDTO adapterRefundDTO = jdShopConverter.toAdapterRefundDTO(source);
            adapterRefundDTO.setOriginData(GsonUtil.objectToJson(source));
            return adapterRefundDTO;
        }
        // 查询退货单
        String shopId = shop.getShopId().split("\\|")[0];
        AscQueryViewResponse response = jdRpc.getRefund(shop, qry.getOrderNo(), qry.getRefundId(), shopId);
        if (response == null) {
            return null;
        }
        ServiceBill source = response.getResult().getData();
        if (response.getResult() == null || source == null) {
            return null;
        }
        RefundDTO adapterRefundDTO = jdShopConverter.toAdapterRefundDTO(source);
        adapterRefundDTO.setOriginData(GsonUtil.objectToJson(source));
        return adapterRefundDTO;
    }

    @Override
    public ShopLogisticsOrderDTO createLogisticsOrder(ShopDO shop, ShopLogisticsOrderCreateCmd cmd) {
        // 生成电子面单，获取运单号
        String waybillNo;
        String logisticsOrderId;
        JdLogisticsCompany logisticsCompany = JdLogisticsCompany.valueOf(cmd.getLogisticsCompany().name());
        try {
            if (logisticsCompany == JdLogisticsCompany.JD) {
                // 京东快递
                String customerCode = logisticsCompany.getCustomerCode(shop.getShopCode());
                LdopWaybillReceiveRequest request = jdShopConverter.toLdopWaybillReceiveRequest(cmd, customerCode);
                waybillNo = jdRpc.createLogisticsOrder(shop, request);
                logisticsOrderId = request.getOrderId();
            } else {
                // 其他快递公司
                LdopAlphaWaybillReceiveRequest request = jdShopConverter.toLdopAlphaWaybillReceiveRequest(cmd, logisticsCompany, shop);
                waybillNo = jdRpc.createLogisticsOrderLodp(shop, request);
                logisticsOrderId = request.getVendorOrderCode();
            }
        } catch (Exception e) {
            String errorMessage = e.getMessage();
            // 根据异常信息定义业务异常，调用方根据 errCode 做特定的业务处理
            if (errorMessage.contains("下单失败")) {
                throw BizException.of(BizErrorCode.B_ORDER_OrderStatusNoAllowCreateLogisticsOrder.getErrCode(), errorMessage, e);
            }
            if (errorMessage.contains("网点停派") || errorMessage.contains("目的网点段码停派")) {
                throw BizException.of(BizErrorCode.B_ORDER_LogisticsOrderPackageNotReachable.getErrCode(), errorMessage, e);
            }
            throw e;
        }
        if (waybillNo == null) {
            throw BizException.of("生成电子面单失败 [" + shop.getShopName() + "]");
        }
        // 电子面单数据加密，传参给打印控件
        PrintDataEncryptDTO printDataEncryptDTO = jdRpc.printDataEncrypt(shop, cmd.getShopOrderNo(),
                logisticsOrderId, logisticsCompany, waybillNo);
        return ShopLogisticsOrderDTO.builder()
                .wayBillNo(waybillNo)
                .templateUrl(printDataEncryptDTO.getTemplateUrl())
                .orderDetail(printDataEncryptDTO.getPerPrintData())
                .build();
    }

    @Override
    public boolean cancelLogisticsOrder(ShopDO shop, ShopLogisticsOrderCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void refundGoodsToWarehouse(ShopDO shop, RefundGoodsToWarehouseCmd cmd) {
        AscReceiveRegisterRequest request = jdShopConverter.toRefundGoodsToWarehouse(cmd);
        jdRpc.refundGoodsToWarehouse(shop, request);
    }

    @Override
    public void barcodeUpload(ShopDO shop, BarcodeUploadCmd cmd) {
        try {
            DigitalSubsidyUploadSnInsertRequest request = jdShopConverter.toBarcodeUpload(cmd);
            // 设置条码上传平台类型
            SubsidySkuAuthParam subsidySkuAuthParam = new SubsidySkuAuthParam();
            SHOP_CODE_GROUPING.entrySet().stream()
                    .filter(entry -> entry.getValue().contains(shop.getShopCode()))
                    .findFirst()
                    .map(Map.Entry::getKey)
                    .ifPresent(subsidySkuAuthParam::setSelfSupport);
            request.setSubsidySkuAuthParam(subsidySkuAuthParam);
            jdRpc.barcodeUpload(shop, request);
        } catch (Exception e) {
            if (e.getMessage().contains("存在重复的sn、imei1、imei2数据")) {
                log.warn("条码重复上传，默认上传成功");
                return;
            }
            throw e;
        }
    }

    /**
     * 根据订单明细里的 skuIds 并且转换成统一订单数据
     *
     * @param platformOrder 平台订单
     * @return 统一订单数据
     */
    private OrderDTO splitSkuIdsAndConvertToAdapterOrderDTO(
            com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.search.OrderSearchInfo platformOrder
    ) {
        // 拆分平台维护的 skuId
        List<com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.search.ItemInfo> splitSkuItems =
                SkuUtil.splitSkuIdsAndCloneItem(
                        platformOrder.getItemInfoList(),
                        com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.search.ItemInfo::getOuterSkuId,
                        com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.search.ItemInfo::setOuterSkuId);
        platformOrder.setItemInfoList(splitSkuItems);
        // 转换统一的数据结构
        return jdShopConverter.toAdapterOrderDTO(platformOrder);
    }

    /**
     * 根据订单明细里的 skuIds 并且转换成统一订单数据
     *
     * @param platformOrder 平台订单
     * @return 统一订单数据
     */
    private OrderDTO splitSkuIdsAndConvertToAdapterOrderDTO(OrderSearchInfo platformOrder) {
        // 拆分平台维护的 skuId
        List<ItemInfo> splitSkuItems = SkuUtil.splitSkuIdsAndCloneItem(platformOrder.getItemInfoList(),
                ItemInfo::getOuterSkuId, ItemInfo::setOuterSkuId);
        platformOrder.setItemInfoList(splitSkuItems);
        // 转换统一的数据结构
        return jdShopConverter.toAdapterOrderDTO(platformOrder);
    }

    /**
     * 检查订单已发货
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 执行结果
     */
    private boolean checkOrderHasShipping(ShopDO shop, String orderNo) {
        OrderSearchInfo order = jdRpc.getOrder(shop, orderNo);
        if ("京仓订单".equals(order.getStoreOrder())) {
            throw new BizException(BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrCode(), "京仓订单不允许发货");
        }
        // 判断订单状态已发货，无需重复发货
        if ("WAIT_GOODS_RECEIVE_CONFIRM,FINISHED_L".contains(order.getOrderState())) {
            log.warn("订单已发货，无需重复发货 {} {}", orderNo, order.getOrderState());
            return true;
        }
        // 判断订单状态不是待发货，抛异常
        if (!"WAIT_SELLER_STOCK_OUT".equals(order.getOrderState())) {
            // 例：平台的订单状态不符合推送发货状态的条件 277901543019 orderState: WAIT_BUYER_PAY
            String msg = String.format("%s %s orderState: %s",
                    BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrDesc(),
                    orderNo, order.getOrderState());
            throw new BizException(BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrCode(), msg);
        }
        return false;
    }

}
