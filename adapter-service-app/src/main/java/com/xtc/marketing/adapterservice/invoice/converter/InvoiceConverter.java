package com.xtc.marketing.adapterservice.invoice.converter;

import com.taobao.api.response.AlibabaEinvoiceCreateResultGetResponse;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.invoice.dto.InvoiceResultDTO;
import com.xtc.marketing.adapterservice.invoice.enums.InvoiceCreateType;
import com.xtc.marketing.adapterservice.invoice.enums.InvoicePlatformCode;
import com.xtc.marketing.adapterservice.invoice.enums.InvoiceStatus;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTypeEnum;
import com.xtc.marketing.adapterservice.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Mapper(
        componentModel = "spring",
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface InvoiceConverter {

    /**
     * 转换统一发票结果数据
     *
     * @param source 发票结果数据
     * @return 统一发票结果数据
     */
    List<InvoiceResultDTO> toInvoiceResultDetailsDTO(List<AlibabaEinvoiceCreateResultGetResponse.InvoiceResult> source);

    /**
     * 转换统一发票结果数据
     *
     * @param source 发票结果数据
     * @return 统一发票结果数据
     */
    @Mapping(target = "platformCode", source = "platformCode", qualifiedByName = "toAdapterInvoicePlatformCodeEnum")
    @Mapping(target = "orderNo", source = "platformTid")
    @Mapping(target = "serialNo", source = "serialNo")
    @Mapping(target = "invoiceCode", source = "invoiceCode")
    @Mapping(target = "invoiceNo", source = "invoiceNo")
    @Mapping(target = "invoiceStatus", source = "status", qualifiedByName = "toAdapterInvoiceStatusEnum")
    @Mapping(target = "createType", source = "invoiceType", qualifiedByName = "toAdapterInvoiceCreateTypeEnum")
    @Mapping(target = "invoiceTitle", source = "invoiceKind", qualifiedByName = "toAdapterInvoiceTypeEnum")
    @Mapping(target = "invoiceTime", source = "source", qualifiedByName = "toAdapterInvoiceTime")
    @Mapping(target = "invoiceAmount", source = "invoiceAmount")
    @Mapping(target = "invoiceFilePath", source = "filePath")
    @Mapping(target = "payerName", source = "payerName")
    @Mapping(target = "payerTaxNo", source = "payerRegisterNo")
    @Mapping(target = "payerMobile", source = "payerPhone")
    @Mapping(target = "payerAddress", source = "payerAddress")
    @Mapping(target = "payerBankAccount", source = "payerBankaccount")
    @Mapping(target = "payeeRegisterNo", source = "payeeRegisterNo")
    @Mapping(target = "payeeReceiver", source = "payeeReceiver")
    @Mapping(target = "payeeChecker", source = "payeeChecker")
    @Mapping(target = "payeeOperator", source = "payeeOperator")
    @Mapping(target = "blueInvoiceCode", source = "normalInvoiceCode")
    @Mapping(target = "blueInvoiceNo", source = "normalInvoiceNo")
    @Mapping(target = "errorCode", source = "bizErrorCode")
    @Mapping(target = "errorMsg", source = "bizErrorMsg")
    InvoiceResultDTO toInvoiceResultDetailsDTO(AlibabaEinvoiceCreateResultGetResponse.InvoiceResult source);

    /**
     * 转换开票时间
     *
     * @param source 开票结果
     * @return 开票时间
     */
    @Named("toAdapterInvoiceTime")
    default LocalDateTime toAdapterInvoiceTime(AlibabaEinvoiceCreateResultGetResponse.InvoiceResult source) {
        if (StringUtils.isAnyBlank(source.getInvoiceDate(), source.getInvoiceTime())) {
            return null;
        }
        LocalDate localDate = DateUtil.toLocalDate(source.getInvoiceDate());
        LocalTime localTime = DateUtil.toLocalTime(source.getInvoiceTime());
        return LocalDateTime.of(localDate, localTime);
    }

    /**
     * 转换电商平台代码
     *
     * @param platformCode 电商平台代码
     * @return 电商平台代码枚举
     */
    @Named("toAdapterInvoicePlatformCodeEnum")
    default InvoicePlatformCode toAdapterInvoicePlatformCodeEnum(String platformCode) {
        try {
            return InvoicePlatformCode.valueOf(platformCode);
        } catch (IllegalArgumentException e) {
            throw BizException.of("未知的电商平台代码 " + platformCode);
        }
    }

    /**
     * 转换开票类型
     *
     * @param createType 开票类型
     * @return 开票类型枚举
     */
    @Named("toAdapterInvoiceCreateTypeEnum")
    default InvoiceCreateType toAdapterInvoiceCreateTypeEnum(String createType) {
        if ("blue".equals(createType)) {
            return InvoiceCreateType.BLUE;
        }
        if ("red".equals(createType)) {
            return InvoiceCreateType.RED;
        }
        throw BizException.of("未知的开票类型 " + createType);
    }

    /**
     * 转换发票状态
     *
     * @param invoiceStatus 发票状态
     * @return 发票状态枚举
     */
    @Named("toAdapterInvoiceStatusEnum")
    default InvoiceStatus toAdapterInvoiceStatusEnum(String invoiceStatus) {
        if ("create_success".equals(invoiceStatus)) {
            return InvoiceStatus.CREATE_SUCCESS;
        }
        if ("create_failed".equals(invoiceStatus)) {
            return InvoiceStatus.CREATE_FAILED;
        }
        if ("waiting".equals(invoiceStatus)) {
            return InvoiceStatus.WAITING;
        }
        throw BizException.of("未知的发票状态 " + invoiceStatus);
    }

    /**
     * 转换发票类型
     *
     * @param invoiceType 发票类型
     * @return 发票类型枚举
     */
    @Named("toAdapterInvoiceTypeEnum")
    default InvoiceTypeEnum toAdapterInvoiceTypeEnum(Long invoiceType) {
        // 默认值为【电子发票】，发票种类：0=电子发票，1=纸质发票，2=专票
        if (invoiceType == 1) {
            return InvoiceTypeEnum.NORMAL;
        }
        if (invoiceType == 2) {
            return InvoiceTypeEnum.SPECIAL;
        }
        return InvoiceTypeEnum.ELECTRONIC;
    }

}
