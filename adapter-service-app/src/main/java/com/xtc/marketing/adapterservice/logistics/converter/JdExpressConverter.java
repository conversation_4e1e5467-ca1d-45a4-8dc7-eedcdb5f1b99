package com.xtc.marketing.adapterservice.logistics.converter;

import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCreateOrderV1.C2BAddedSettleTypeInfo;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCreateOrderV1.CommonCargoInfo;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCreateOrderV1.CommonCreateOrderRequest;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCreateOrderV1.CommonGoodsInfo;
import com.lop.open.api.sdk.domain.ECAP.CommonModifyCancelOrderApi.commonCancelOrderV1.CommonOrderCancelRequest;
import com.lop.open.api.sdk.domain.ECAP.CommonQueryOrderApi.commonGetOrderTraceV1.CommonOrderTraceDetail;
import com.lop.open.api.sdk.request.ECAP.*;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCancelOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCargoCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsOrderGetQry;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsRouteListQry;
import com.xtc.marketing.adapterservice.rpc.jd.jdexpressdto.command.JdExpressCancelCmd;
import com.xtc.marketing.adapterservice.rpc.jd.jdexpressdto.command.JdExpressCreateCmd;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Mapper(componentModel = "spring",
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface JdExpressConverter {

    /**
     * 转换为京东路由请求参数
     *
     * @param qry 参数
     * @return 京东路由请求参数
     */
    @Mapping(target = "commonOrderTraceRequest.customerCode", source = "bizAccount")
    @Mapping(target = "commonOrderTraceRequest.waybillCode", source = "waybillNo")
    @Mapping(target = "commonOrderTraceRequest.orderOrigin", source = "waybillScene")
    EcapV1OrdersTraceQueryLopRequest toJdOrdersTraceQueryLopRequest(LogisticsRouteListQry qry);

    /**
     * 转换为统一路由信息
     *
     * @param routes 路由信息
     * @return 统一路由信息
     */
    @Mapping(target = "node", source = "operationTitle")
    @Mapping(target = "time", source = "operationTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "detail", source = "operationRemark")
    @Mapping(target = "title", source = "categoryName")
    @Mapping(target = "code", source = "category")
    LogisticsRouteDTO toLogisticsRouteDTO(CommonOrderTraceDetail routes);

    /**
     * 转换为统一路由信息
     *
     * @param routes 路由信息
     * @return 统一路由信息
     */
    List<LogisticsRouteDTO> toLogisticsRouteDTO(List<CommonOrderTraceDetail> routes);

    /**
     * 转换为京东预下单请求参数
     *
     * @param cmd 参数
     * @return 预下单请求参数
     */
    @Mapping(target = "request.customerCode", source = "bizAccount")
    @Mapping(target = "request.senderContact.name", source = "senderName")
    @Mapping(target = "request.senderContact.mobile", source = "senderMobile")
    @Mapping(target = "request.senderContact.fullAddress", source = "senderAddress")
    @Mapping(target = "request.receiverContact.name", source = "receiverName")
    @Mapping(target = "request.receiverContact.mobile", source = "receiverMobile")
    @Mapping(target = "request.receiverContact.fullAddress", source = "receiverAddress")
    @Mapping(target = "request.cargoes", source = "cargos", qualifiedByName = "toPreCommonCargoInfo")
    @Mapping(target = "request.goods", source = "cargos", qualifiedByName = "toPreCommonGoodsInfo")
    EcapV1OrdersPrecheckLopRequest toPreOrdersCreateLopRequest(LogisticsCreateOrderCmd cmd);

    /**
     * 转换为京东下单请求参数
     *
     * @param cmd 参数
     * @return 京东下单请求参数
     */
    @Mapping(target = "request.customerCode", source = "bizAccount")
    @Mapping(target = "request.orderId", source = "orderId")
    @Mapping(target = "request.senderContact.name", source = "senderName")
    @Mapping(target = "request.senderContact.mobile", source = "senderMobile")
    @Mapping(target = "request.senderContact.fullAddress", source = "senderAddress")
    @Mapping(target = "request.receiverContact.name", source = "receiverName")
    @Mapping(target = "request.receiverContact.mobile", source = "receiverMobile")
    @Mapping(target = "request.receiverContact.fullAddress", source = "receiverAddress")
    @Mapping(target = "request.cargoes", source = "cargos", qualifiedByName = "toCommonCargoInfo")
    @Mapping(target = "request.goods", source = "cargos", qualifiedByName = "toCommonGoodsInfo")
    @Mapping(target = "request.remark", source = "remark")
    @Mapping(target = "request.productsReq.productCode", source = "expressTypeId")
    EcapV1OrdersCreateLopRequest toOrdersCreateLopRequest(LogisticsCreateOrderCmd cmd);

    /**
     * 转换为京东查询订单请求参数
     *
     * @param qry 参数
     * @return 京东查询订单请求参数
     */
    @Mapping(target = "request.customerCode", source = "bizAccount")
    @Mapping(target = "request.waybillCode", source = "orderId")
    @Mapping(target = "request.orderOrigin", source = "waybillScene")
    EcapV1OrdersStatusGetLopRequest toOrdersStatusGetLopRequest(LogisticsOrderGetQry qry);

    /**
     * 转换为京东取消订单请求参数
     *
     * @param cmd 参数
     * @return 京东取消订单请求参数
     */
    @Mapping(target = "request.customerCode", source = "bizAccount")
    @Mapping(target = "request.waybillCode", source = "orderId")
    @Mapping(target = "request.cancelReason", source = "cancelReason")
    EcapV1OrdersCancelLopRequest toJdOrdersCancelLopRequest(LogisticsCancelOrderCmd cmd);

    /**
     * 转换为京东货物
     *
     * @param cargo 货物
     * @return 京东货物
     */
    @Named("toCommonCargoInfo")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "quantity", source = "quantity")
    @Mapping(target = "weight", constant = "1")
    @Mapping(target = "volume", constant = "100")
    CommonCargoInfo toCommonCargoInfo(LogisticsCargoCmd cargo);

    /**
     * 转换为京东货物
     *
     * @param cargo 货物
     * @return 京东货物
     */
    @Named("toPreCommonCargoInfo")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "quantity", source = "quantity")
    @Mapping(target = "weight", constant = "1")
    @Mapping(target = "volume", constant = "100")
    com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCheckPreCreateOrderV1.CommonCargoInfo toPreCommonCargoInfo(LogisticsCargoCmd cargo);

    /**
     * 转换为京东商品
     *
     * @param cargo 京东商品
     * @return 京东商品
     */
    @Named("toCommonGoodsInfo")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "quantity", source = "quantity")
    @Mapping(target = "weight", constant = "1")
    CommonGoodsInfo toCommonGoodsInfo(LogisticsCargoCmd cargo);

    /**
     * 转换为京东商品
     *
     * @param cargo 京东商品
     * @return 京东商品
     */
    @Named("toPreCommonGoodsInfo")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "quantity", source = "quantity")
    com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCheckPreCreateOrderV1.CommonGoodsInfo toPreCommonGoodsInfo(LogisticsCargoCmd cargo);

    /**
     * 转换为京东下单请求参数时，设置平台参数
     *
     * @param request 请求参数
     * @param cmd     参数
     */
    @AfterMapping
    default void setPreCreateOrderParam(@MappingTarget EcapV1OrdersPrecheckLopRequest request, LogisticsCreateOrderCmd cmd) {
        JdExpressCreateCmd param = this.buildJdExpressCreateCmd(cmd);
        com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCheckPreCreateOrderV1.CommonCreateOrderRequest createOrderRequest = request.getRequest();
        // 设置平台参数
        createOrderRequest.setOrderOrigin(param.getWaybillScene());
        createOrderRequest.setSettleType(param.getPaymentType());
    }

    /**
     * 转换为京东下单请求参数时，设置平台参数
     *
     * @param request 请求参数
     * @param cmd     参数
     */
    @AfterMapping
    default void setCreateOrderParam(@MappingTarget EcapV1OrdersCreateLopRequest request, LogisticsCreateOrderCmd cmd) {
        JdExpressCreateCmd param = this.buildJdExpressCreateCmd(cmd);
        CommonCreateOrderRequest createOrderRequest = request.getRequest();
        // 设置平台参数
        createOrderRequest.setOrderOrigin(param.getWaybillScene());
        createOrderRequest.setSettleType(param.getPaymentType());
        Date pickupStartTime = DateUtil.toDate(param.getPickupStartTime());
        Date pickupEndTime = DateUtil.toDate(param.getPickupEndTime());
        createOrderRequest.setPickupStartTime(pickupStartTime);
        createOrderRequest.setPickupEndTime(pickupEndTime);
        createOrderRequest.getProductsReq().setAddedProducts(param.getExtraProducts());
        // c2b场景，必填多项付款方式
        if (param.getWaybillScene() == 2) {
            CommonCreateOrderRequest orderRequest = request.getRequest();
            C2BAddedSettleTypeInfo settleTypeInfo = new C2BAddedSettleTypeInfo();
            settleTypeInfo.setBasicFreigthSettleType(param.getPaymentDetailType());
            settleTypeInfo.setPackageServiceSettleType(param.getPaymentDetailType());
            settleTypeInfo.setGuaranteeMoneyServiceSettleType(param.getPaymentDetailType());
            orderRequest.setC2bAddedSettleTypeInfo(settleTypeInfo);
        }
    }

    /**
     * 转换为京东下单取消参数时，设置平台参数
     *
     * @param request 请求参数
     * @param cmd     参数
     */
    @AfterMapping
    default void setCancelParam(@MappingTarget EcapV1OrdersCancelLopRequest request, LogisticsCancelOrderCmd cmd) {
        if (cmd.getPlatformJson() == null) {
            throw SysException.of(SysErrorCode.S_PARAM_ERROR, "京东快递取消参数不存在");
        }
        JdExpressCancelCmd param;
        try {
            param = GsonUtil.jsonToBean(cmd.getPlatformJson(), JdExpressCancelCmd.class);
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_PARAM_ERROR, "京东快递取消参数转换异常 " + cmd.getPlatformJson());
        }
        CommonOrderCancelRequest cancelRequest = request.getRequest();
        cancelRequest.setCancelReasonCode(param.getCancelReasonCode());
        cancelRequest.setOrderOrigin(param.getWaybillScene());
        cancelRequest.setCancelType(param.getCancelType());
    }

    /**
     * 转换订单查询参数
     *
     * @param operationTime 平台参数
     * @return 订单查询参数
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(String operationTime) {
        return DateUtil.toLocalDateTime(operationTime);
    }

    /**
     * 构建京东下单参数
     *
     * @param cmd 参数
     * @return 京东下单参数
     */
    default JdExpressCreateCmd buildJdExpressCreateCmd(LogisticsCreateOrderCmd cmd) {
        if (cmd.getPlatformJson() == null) {
            throw SysException.of(SysErrorCode.S_PARAM_ERROR, "京东快递下单参数不存在");
        }
        JdExpressCreateCmd param;
        try {
            param = GsonUtil.jsonToBean(cmd.getPlatformJson(), JdExpressCreateCmd.class);
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_PARAM_ERROR, "京东快递下单参数转换异常 " + cmd.getPlatformJson());
        }
        return param;
    }

}
