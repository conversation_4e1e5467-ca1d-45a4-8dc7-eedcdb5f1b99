package com.xtc.marketing.adapterservice.shop.executor.extension;

import com.alibaba.cola.extension.ExtensionPointI;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.dto.query.*;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * 店铺扩展点
 */
public interface ShopExtPt extends ExtensionPointI {

    /**
     * 获取店铺 token 数据
     * <p>默认不处理返回 null</p>
     *
     * @param shop 店铺
     * @return 新的店铺 token 数据
     */
    ShopDO getShopToken(ShopDO shop);

    /**
     * 查询订单分页列表
     *
     * @param shop 店铺
     * @param qry  参数
     * @return 订单分页列表
     */
    PageResponse<OrderDTO> pageOrders(ShopDO shop, OrderPageQry qry);

    /**
     * 查询订单
     *
     * @param shop 店铺
     * @param qry  参数
     * @return 订单
     */
    OrderDTO getOrder(ShopDO shop, OrderGetQry qry);

    /**
     * 订单发货
     *
     * @param shop 店铺
     * @param cmd  参数
     * @return 执行结果
     */
    boolean orderShipping(ShopDO shop, OrderShippingCmd cmd);

    /**
     * 订单取消发货
     *
     * @param shop 店铺
     * @param cmd  参数
     * @return 执行结果
     */
    boolean orderShippingCancel(ShopDO shop, OrderShippingCancelCmd cmd);

    /**
     * 订单无需物流发货
     *
     * @param shop 店铺
     * @param cmd  参数
     * @return 执行结果
     */
    boolean orderDummyShipping(ShopDO shop, OrderDummyShippingCmd cmd);

    /**
     * 订单备注
     *
     * @param shop 店铺
     * @param cmd  参数
     * @return 执行结果
     */
    boolean orderRemark(ShopDO shop, OrderRemarkCmd cmd);

    /**
     * 订单解密
     *
     * @param shop 店铺
     * @param cmd  参数
     * @return 解密数据
     */
    OrderDecryptDTO orderDecrypt(ShopDO shop, OrderDecryptCmd cmd);

    /**
     * 分页查询发票申请列表
     *
     * @param shop 店铺
     * @param qry  参数
     * @return 发票申请分页列表
     */
    PageResponse<InvoiceApplyDTO> pageInvoiceApply(ShopDO shop, InvoiceApplyPageQry qry);

    /**
     * 查询发票申请
     *
     * @param shop 店铺
     * @param qry  参数
     * @return 发票申请
     */
    InvoiceApplyDTO getInvoiceApply(ShopDO shop, InvoiceApplyGetQry qry);

    /**
     * 上传发票 - 文件流
     *
     * @param shop        店铺
     * @param cmd         参数
     * @param invoiceFile 发票文件
     * @return 执行结果
     */
    boolean uploadInvoiceFile(ShopDO shop, OrderUploadInvoiceCmd cmd, MultipartFile invoiceFile);

    /**
     * 上传发票 - base64字符串
     *
     * @param shop 店铺
     * @param cmd  参数
     * @return 执行结果
     */
    boolean uploadInvoiceBase64(ShopDO shop, InvoiceUploadCmd cmd);

    /**
     * 查询发票开票金额
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 发票开票金额
     */
    InvoiceAmountDTO getInvoiceAmount(ShopDO shop, String orderNo);

    /**
     * 查询评价分页列表
     *
     * @param shop 店铺
     * @param qry  参数
     * @return 评价分页列表
     */
    PageResponse<CommentDTO> pageComments(ShopDO shop, CommentPageQry qry);

    /**
     * 查询退款单分页列表
     *
     * @param shop 店铺
     * @param qry  参数
     * @return 退款单分页列表
     */
    PageResponse<RefundDTO> pageRefunds(ShopDO shop, RefundPageQry qry);

    /**
     * 查询退款单
     *
     * @param shop 店铺
     * @param qry  参数
     * @return 退款单
     */
    RefundDTO getRefund(ShopDO shop, RefundGetQry qry);

    /**
     * 生成电子面单
     *
     * @param shop 店铺
     * @param cmd  参数
     * @return 电子面单
     */
    ShopLogisticsOrderDTO createLogisticsOrder(ShopDO shop, ShopLogisticsOrderCreateCmd cmd);

    /**
     * 取消电子面单
     *
     * @param shop 店铺
     * @param cmd  参数
     * @return 执行结果
     */
    boolean cancelLogisticsOrder(ShopDO shop, ShopLogisticsOrderCancelCmd cmd);

    /**
     * 退货确认入仓
     *
     * @param shop 店铺
     * @param cmd  参数
     */
    void refundGoodsToWarehouse(ShopDO shop, RefundGoodsToWarehouseCmd cmd);

    /**
     * 条码上传
     *
     * @param shop 店铺
     * @param cmd  参数
     */
    void barcodeUpload(ShopDO shop, BarcodeUploadCmd cmd);

}
