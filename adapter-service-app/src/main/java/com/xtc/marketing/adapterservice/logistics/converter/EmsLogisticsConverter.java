package com.xtc.marketing.adapterservice.logistics.converter;

import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCargoCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.rpc.ems.EmsRpc;
import com.xtc.marketing.adapterservice.rpc.ems.emsdto.EmsRouteDTO;
import com.xtc.marketing.adapterservice.rpc.ems.emsdto.command.EmsCargoCmd;
import com.xtc.marketing.adapterservice.rpc.ems.emsdto.command.EmsCreateOrderCmd;
import com.xtc.marketing.adapterservice.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.util.List;

@Mapper(
        componentModel = "spring",
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface EmsLogisticsConverter {

    /**
     * 转换平台下单参数
     *
     * @param source 统一下单参数
     * @return 平台下单参数
     */
    @Mapping(target = "orderNormal.logisticsOrderNo", source = "orderId")
    @Mapping(target = "orderNormal.senderNo", source = "bizAccount")
    @Mapping(target = "orderNormal.bizProductNo", constant = EmsRpc.BIZ_PRODUCT_NORMAL)
    @Mapping(target = "orderNormal.baseProductNo", constant = EmsRpc.BIZ_PRODUCT_NORMAL)
    @Mapping(target = "orderNormal.ecommerceUserId", constant = EmsRpc.ECOMMERCE_USER_ID)
    @Mapping(target = "orderNormal.sender.prov", source = "senderProvince")
    @Mapping(target = "orderNormal.sender.city", source = "senderCity")
    @Mapping(target = "orderNormal.sender.county", source = "senderDistrict")
    @Mapping(target = "orderNormal.sender.address", source = "senderAddress")
    @Mapping(target = "orderNormal.sender.name", source = "senderName")
    @Mapping(target = "orderNormal.sender.mobile", source = "senderMobile")
    @Mapping(target = "orderNormal.receiver.prov", source = "receiverProvince")
    @Mapping(target = "orderNormal.receiver.city", source = "receiverCity")
    @Mapping(target = "orderNormal.receiver.county", source = "receiverDistrict")
    @Mapping(target = "orderNormal.receiver.address", source = "receiverAddress")
    @Mapping(target = "orderNormal.receiver.name", source = "receiverName")
    @Mapping(target = "orderNormal.receiver.mobile", source = "receiverMobile")
    @Mapping(target = "orderNormal.cargos", source = "cargos", qualifiedByName = "toEmsCargoCmd")
    EmsCreateOrderCmd toEmsCreateOrderCmd(LogisticsCreateOrderCmd source);

    /**
     * 转换平台货物参数
     *
     * @param source 统一货物参数
     * @return 平台货物参数
     */
    @Named("toEmsCargoCmd")
    @Mapping(target = "cargoName", source = "name")
    EmsCargoCmd toEmsCargoCmd(LogisticsCargoCmd source);

    /**
     * 转换统一路由数据
     *
     * @param source 平台路由数据
     * @return 统一路由数据
     */
    List<LogisticsRouteDTO> toAdapterRouteDTO(List<EmsRouteDTO> source);

    /**
     * 转换统一路由数据
     *
     * @param source 平台路由数据
     * @return 统一路由数据
     */
    @Mapping(target = "time", source = "opTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "detail", source = "opDesc")
    @Mapping(target = "title", source = "opName")
    @Mapping(target = "code", source = "opCode")
    LogisticsRouteDTO toAdapterRouteDTO(EmsRouteDTO source);

    /**
     * 设置路由节点
     *
     * @param route         统一路由数据
     * @param platformRoute 平台路由数据
     */
    @AfterMapping
    default void setRouteNode(@MappingTarget LogisticsRouteDTO route, EmsRouteDTO platformRoute) {
        String node = StringUtils.join(platformRoute.getOpOrgProvName(), platformRoute.getOpOrgCity());
        route.setNode(node);
    }

    /**
     * 时间转换
     *
     * @param time 时间字符串
     * @return LocalDateTime
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(String time) {
        if (StringUtils.isBlank(time)) {
            return null;
        }
        return DateUtil.toLocalDateTime(time);
    }

}
