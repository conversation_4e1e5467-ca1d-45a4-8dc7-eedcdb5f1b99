package com.xtc.marketing.adapterservice.shop.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.google.gson.JsonObject;
import com.kuaishou.merchant.open.api.domain.dropshipping.DsItemDTO;
import com.kuaishou.merchant.open.api.domain.dropshipping.DsOrderDTO;
import com.kuaishou.merchant.open.api.domain.dropshipping.GetEbillOrderDTO;
import com.kuaishou.merchant.open.api.request.dropshipping.OpenDropshippingEbillBatchGetRequest;
import com.kuaishou.merchant.open.api.request.dropshipping.OpenDropshippingOrderDeliverRequest;
import com.kuaishou.merchant.open.api.request.dropshipping.OpenDropshippingOrderListRequest;
import com.kuaishou.merchant.open.api.response.dropshipping.OpenDropshippingOrderDetailQueryResponse;
import com.kuaishou.merchant.open.api.response.dropshipping.OpenDropshippingOrderListResponse;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.kuaishou.KuaishouRpc;
import com.xtc.marketing.adapterservice.rpc.kuaishou.enums.KuaishouLogisticsCompany;
import com.xtc.marketing.adapterservice.shop.converter.KuaishouDistrShopConverter;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.dto.query.*;
import com.xtc.marketing.adapterservice.shop.enums.ShopTypeEnum;
import com.xtc.marketing.adapterservice.shop.executor.query.ShopGetQryExe;
import com.xtc.marketing.adapterservice.shop.util.SkuUtil;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = ShopExtConstant.USE_CASE, scenario = ShopExtConstant.SCENARIO_KUAISHOU_DISTR)
public class KuaishouDistrShopExt implements ShopExtPt {

    private final KuaishouRpc kuaishouRpc;
    private final KuaishouDistrShopConverter kuaishouDistrShopConverter;
    private final ShopGetQryExe shopGetQryExe;

    @Override
    public ShopDO getShopToken(ShopDO shop) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<OrderDTO> pageOrders(ShopDO shop, OrderPageQry qry) {
        // 初始化响应结果
        PageResponse<OrderDTO> response = PageResponse.of(Collections.emptyList(), -1, qry.getPageSize(), qry.getPageIndex());
        // 默认设置下一页没有数据
        response.setHasNext(false);
        response.setNextKey(KuaishouRpc.NO_MORE);
        // 如果请求参数标识下一页没有数据，则直接返回响应结果，用于终止循环分页查询
        if (KuaishouRpc.NO_MORE.equals(qry.getNextKey())) {
            return response;
        }
        // 查询代理关联店铺
        ShopDO relatedShop = this.getRelatedShop(shop);
        OpenDropshippingOrderListRequest request = new OpenDropshippingOrderListRequest();
        request.setQueryType(1);
        request.setPageSize(qry.getPageSize());
        request.setBeginTime(DateUtil.toEpochMilli(qry.getUpdateTimeStart()));
        request.setEndTime(DateUtil.toEpochMilli(qry.getUpdateTimeEnd()));
        request.setCursor(qry.getNextKey());
        OpenDropshippingOrderListResponse orderListData = kuaishouRpc.pageOrdersDistr(relatedShop, request);
        if (orderListData == null || orderListData.getData() == null || orderListData.getData().getDsOrderList() == null) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }
        /*
        1. 代理店铺需要过滤店铺订单，工厂店铺无需过滤
        2. 拆分订单明细里的 skuIds 并且转换成统一订单数据
        3. 为每个订单关联店铺代码
         */
        List<OrderDTO> orders = orderListData.getData().getDsOrderList().stream()
                .filter(order -> ShopTypeEnum.AGENT == relatedShop.getShopType())
                .map(this::splitSkuIdsAndConvertToAdapterOrderDTO)
                .collect(Collectors.toList());
        // 返回分页数据，设置 hasNext nextKey 字段
        PageResponse<OrderDTO> pageResponse = PageResponse.of(orders, -1, qry.getPageSize(), qry.getPageIndex());
        if (BooleanUtils.isTrue(qry.getUseHasNext())) {
            pageResponse.setHasNext(ObjectUtils.notEqual(KuaishouRpc.NO_MORE, orderListData.getData().getCursor()));
            pageResponse.setNextKey(orderListData.getData().getCursor());
        }
        return pageResponse;
    }

    @Override
    public OrderDTO getOrder(ShopDO shop, OrderGetQry qry) {
        ShopDO relatedShop = getRelatedShop(shop);
        String shopId = shop.getAgentCode().split("\\|")[0];
        OpenDropshippingOrderDetailQueryResponse response = kuaishouRpc.getOrderDistr(relatedShop, qry.getOrderNo(), shopId);
        if (response == null) {
            return null;
        }
        OrderDTO orderDTO = this.splitSkuIdsAndConvertToAdapterOrderDTO(response.getData());
        orderDTO.setOriginOrderData(GsonUtil.objectToJson(response.getData()));
        return orderDTO;
    }

    @Override
    public boolean orderRemark(ShopDO shop, OrderRemarkCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean orderShipping(ShopDO shop, OrderShippingCmd cmd) {
        ShopDO relatedShop = getRelatedShop(shop);
        OpenDropshippingOrderDeliverRequest request = new OpenDropshippingOrderDeliverRequest();
        // 获取快递公司编码
        String shopId = shop.getAgentCode().split("\\|")[0];
        KuaishouLogisticsCompany logisticsCompany = KuaishouLogisticsCompany.valueOf(cmd.getLogisticsCompany().name());
        request.setWaybillCode(cmd.getWaybillNo());
        request.setUserCode(shopId);
        request.setAllocateOrderCode(cmd.getOrderNo());
        request.setExpressCompanyCode(logisticsCompany.getName());
        return kuaishouRpc.orderShippingDistr(relatedShop, request);
    }

    @Override
    public boolean orderShippingCancel(ShopDO shop, OrderShippingCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean orderDummyShipping(ShopDO shop, OrderDummyShippingCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public OrderDecryptDTO orderDecrypt(ShopDO shop, OrderDecryptCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<InvoiceApplyDTO> pageInvoiceApply(ShopDO shop, InvoiceApplyPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public InvoiceApplyDTO getInvoiceApply(ShopDO shop, InvoiceApplyGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceFile(ShopDO shop, OrderUploadInvoiceCmd cmd, MultipartFile invoiceFile) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceBase64(ShopDO shop, InvoiceUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public InvoiceAmountDTO getInvoiceAmount(ShopDO shop, String orderNo) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<CommentDTO> pageComments(ShopDO shop, CommentPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<RefundDTO> pageRefunds(ShopDO shop, RefundPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public RefundDTO getRefund(ShopDO shop, RefundGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public ShopLogisticsOrderDTO createLogisticsOrder(ShopDO shop, ShopLogisticsOrderCreateCmd cmd) {
        ShopDO relatedShop = this.getRelatedShop(shop);
        KuaishouLogisticsCompany logisticsCompany = KuaishouLogisticsCompany.valueOf(cmd.getLogisticsCompany().name());
        OpenDropshippingEbillBatchGetRequest request = kuaishouDistrShopConverter.toOpenExpressEbillGetRequest(cmd, shop, logisticsCompany);
        GetEbillOrderDTO order = kuaishouRpc.createLogisticsOrderDistr(relatedShop, request);
        JsonObject orderDetailJson = new JsonObject();
        orderDetailJson.addProperty("signature", order.getSignature());
        orderDetailJson.addProperty("encryptedData", order.getPrintData());
        orderDetailJson.addProperty("key", order.getKey());
        orderDetailJson.addProperty("templateURL", logisticsCompany.getTemplateUrl());
        orderDetailJson.addProperty("ver", order.getVersion());
        return ShopLogisticsOrderDTO.builder()
                .wayBillNo(order.getWaybillCode())
                .orderDetail(orderDetailJson.toString())
                .build();
    }

    @Override
    public boolean cancelLogisticsOrder(ShopDO shop, ShopLogisticsOrderCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void refundGoodsToWarehouse(ShopDO shop, RefundGoodsToWarehouseCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void barcodeUpload(ShopDO shop, BarcodeUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    /**
     * 查询关联的厂家店铺
     *
     * @param shop 代发店铺
     * @return 厂家店铺
     */
    private ShopDO getRelatedShop(ShopDO shop) {
        if (shop.getShopType() != ShopTypeEnum.AGENT) {
            throw BizException.of("店铺不是代理类型，不支持使用代发店铺的操作");
        }
        // 代发店铺通过 shopId 关联的厂家店铺
        return shopGetQryExe.execute(shop.getShopId());
    }

    /**
     * 拆分订单明细里的 skuIds 并且转换成统一订单数据
     *
     * @param platformOrder 平台订单
     * @return 统一订单数据
     */
    private OrderDTO splitSkuIdsAndConvertToAdapterOrderDTO(DsOrderDTO platformOrder) {
        // 拆分平台维护的 skuId
        List<DsItemDTO> splitSkuItems = SkuUtil.splitSkuIdsAndCloneItem(platformOrder.getDsItemDto(), DsItemDTO::getSkuId, DsItemDTO::setSkuId);
        List<OrderItemDTO> items = splitSkuItems.stream()
                .map(item -> kuaishouDistrShopConverter.toAdapterOrderItemDTO(platformOrder.getAllocateOrderCode(), item))
                .collect(Collectors.toList());
        // 转换统一的数据结构
        OrderDTO order = kuaishouDistrShopConverter.toAdapterOrderDTO(platformOrder);
        order.setItems(items);
        return order;
    }

}