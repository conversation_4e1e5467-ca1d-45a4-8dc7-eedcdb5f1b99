package com.xtc.marketing.adapterservice.shop.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.xiaohongshu.fls.opensdk.entity.afterSale.request.ListAfterSaleInfosRequest;
import com.xiaohongshu.fls.opensdk.entity.afterSale.response.GetAfterSaleInfoResponse;
import com.xiaohongshu.fls.opensdk.entity.afterSale.response.ListAfterSaleInfosResponse;
import com.xiaohongshu.fls.opensdk.entity.express.request.ElectronicBillOrdersCreateRequest;
import com.xiaohongshu.fls.opensdk.entity.express.response.ElectronicBillOrdersCreateResponse;
import com.xiaohongshu.fls.opensdk.entity.invoice.request.ConfirmInvoiceRequest;
import com.xiaohongshu.fls.opensdk.entity.invoice.request.GetInvoiceListRequest;
import com.xiaohongshu.fls.opensdk.entity.invoice.response.GetInvoiceListResponse;
import com.xiaohongshu.fls.opensdk.entity.oauth.response.RefreshTokenResponse;
import com.xiaohongshu.fls.opensdk.entity.order.Requset.GetOrderListRequest;
import com.xiaohongshu.fls.opensdk.entity.order.Requset.OrderDeliverRequest;
import com.xiaohongshu.fls.opensdk.entity.order.Response.GetOrderDetailResponse;
import com.xiaohongshu.fls.opensdk.entity.order.Response.GetOrderListResponse;
import com.xiaohongshu.fls.opensdk.entity.order.Response.GetOrderReceiverInfoResponse;
import com.xiaohongshu.fls.opensdk.entity.order.Response.OrderReceiverInfo;
import com.xiaohongshu.fls.opensdk.entity.order.SkuIdentifyCodeInfo;
import com.xtc.marketing.adapterservice.exception.BizErrorCode;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.xiaohongshu.XiaohongshuRpc;
import com.xtc.marketing.adapterservice.rpc.xiaohongshu.enums.XiaohongshuLogisticsCompany;
import com.xtc.marketing.adapterservice.rpc.xiaohongshu.xiaohongshudto.LogisticsOrderExtraInfoDTO;
import com.xtc.marketing.adapterservice.shop.converter.XiaohongshuConverter;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.dto.query.*;
import com.xtc.marketing.adapterservice.shop.util.SkuUtil;
import com.xtc.marketing.adapterservice.util.BeanCopier;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = ShopExtConstant.USE_CASE, scenario = ShopExtConstant.SCENARIO_XIAOHONGSHU)
public class XiaohongshuShopExt implements ShopExtPt {

    private final XiaohongshuRpc xiaohongshuRpc;
    private final XiaohongshuConverter xiaohongshuConverter;

    @Override
    public ShopDO getShopToken(ShopDO shop) {
        // 初始化
        ShopDO newToken = BeanCopier.copy(shop, ShopDO::new);
        // 刷新 AccessToken
        RefreshTokenResponse refreshTokenResponse = xiaohongshuRpc.refreshAccessToken(shop);
        if (refreshTokenResponse == null) {
            return newToken;
        }
        // 设置新的 token 数据
        newToken.setAppAccessToken(refreshTokenResponse.getAccessToken());
        newToken.setAppRefreshToken(refreshTokenResponse.getRefreshToken());
        // 过期时间转换取整
        long expiresSecond = refreshTokenResponse.getAccessTokenExpiresAt() / 1000;
        newToken.setAppExpireTime(DateUtil.toLocalDateTime(expiresSecond));
        return newToken;
    }

    @Override
    public PageResponse<OrderDTO> pageOrders(ShopDO shop, OrderPageQry qry) {
        GetOrderListRequest request = new GetOrderListRequest();
        request.setPageNo(qry.getPageIndex());
        request.setPageSize(qry.getPageSize());
        // 全部
        request.setOrderStatus(0);
        // 全部
        request.setOrderType(0);
        // 更新时间,倒序
        request.setTimeType(2);
        request.setStartTime(DateUtil.toEpochSecond(qry.getUpdateTimeStart()));
        request.setEndTime(DateUtil.toEpochSecond(qry.getUpdateTimeEnd()));
        GetOrderListResponse response = xiaohongshuRpc.pageOrders(shop, request);
        // 转换数据，返回分页数据结构
        List<OrderDTO> orderList = response.getOrderList().stream()
                .map(xiaohongshuConverter::toAdapterOrderDTO)
                .collect(Collectors.toList());
        return PageResponse.of(orderList, response.getTotal(), response.getPageSize(), response.getPageNo());
    }

    @Override
    public OrderDTO getOrder(ShopDO shop, OrderGetQry qry) {
        GetOrderDetailResponse order = xiaohongshuRpc.getOrder(shop, qry.getOrderNo());
        if (order == null) {
            return null;
        }
        GetOrderReceiverInfoResponse receiverResponse = xiaohongshuRpc.getOrderReceiver(shop, order.getOrderId(), order.getOpenAddressId());
        OrderDTO orderItemDTO = this.splitSkuIdsAndConvertToAdapterOrderDTO(order, receiverResponse.getReceiverInfos());
        orderItemDTO.setOriginOrderData(GsonUtil.objectToJson(order));
        return orderItemDTO;
    }

    @Override
    public boolean orderShipping(ShopDO shop, OrderShippingCmd cmd) {
        GetOrderDetailResponse order = xiaohongshuRpc.getOrder(shop, cmd.getOrderNo());
        boolean hasShipping = this.checkOrderHasShipping(order);
        if (hasShipping) {
            return true;
        }
        XiaohongshuLogisticsCompany logisticsCompany = XiaohongshuLogisticsCompany.valueOf(cmd.getLogisticsCompany().name());
        OrderDeliverRequest request = new OrderDeliverRequest();
        request.setOrderId(cmd.getOrderNo());
        request.setExpressNo(cmd.getWaybillNo());
        request.setExpressCompanyCode(logisticsCompany.getCode());
        // 设置国补字段数据
        if (order.getSubsidySkuIdentifyCodeRequiredInfo() != null && CollectionUtils.isNotEmpty(cmd.getBarcodes())) {
            SkuIdentifyCodeInfo skuIdentifyCodeInfo = new SkuIdentifyCodeInfo();
            cmd.getBarcodes().forEach(barcode -> {
                if (order.getSubsidySkuIdentifyCodeRequiredInfo().isSnRequired()) {
                    skuIdentifyCodeInfo.setsNCode(barcode.getBarcode());
                }
                if (order.getSubsidySkuIdentifyCodeRequiredInfo().isImei1Required()) {
                    skuIdentifyCodeInfo.setiMEI1Code(barcode.getImei());
                }
            });
            request.setSkuIdentifyCodeInfo(skuIdentifyCodeInfo);
        }
        return xiaohongshuRpc.orderShipping(shop, request);
    }

    @Override
    public boolean orderShippingCancel(ShopDO shop, OrderShippingCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean orderDummyShipping(ShopDO shop, OrderDummyShippingCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean orderRemark(ShopDO shop, OrderRemarkCmd cmd) {
        return xiaohongshuRpc.orderRemark(shop, cmd.getOrderNo(), cmd.getRemark());
    }

    @Override
    public OrderDecryptDTO orderDecrypt(ShopDO shop, OrderDecryptCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<InvoiceApplyDTO> pageInvoiceApply(ShopDO shop, InvoiceApplyPageQry qry) {
        // 构建请求参数
        GetInvoiceListRequest request = new GetInvoiceListRequest();
        // 设置查询时间范围
        request.setStartDateLong(DateUtil.toEpochMilli(qry.getStartTime()));
        request.setEndDateLong(DateUtil.toEpochMilli(qry.getEndTime()));
        request.setSortEnum(1);
        // 开票状态，1:待开票；6：已开票；10：待作废
        request.setInvoiceStatus(1);
        // 调用小红书开票列表查询接口
        GetInvoiceListResponse response = xiaohongshuRpc.pageInvoiceApply(shop, request);
        if (response == null || response.getTotal() == 0) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }
        List<InvoiceApplyDTO> invoiceList = xiaohongshuConverter.toInvoiceApplyList(response.getInvoiceRecords());
        return PageResponse.of(invoiceList, response.getTotal(), response.getPageSize(), response.getPageIndex());
    }

    @Override
    public InvoiceApplyDTO getInvoiceApply(ShopDO shop, InvoiceApplyGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceFile(ShopDO shop, OrderUploadInvoiceCmd cmd, MultipartFile invoiceFile) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceBase64(ShopDO shop, InvoiceUploadCmd cmd) {
        // 查询待开票信息，获取小红书开票编号
        GetInvoiceListRequest getRequest = new GetInvoiceListRequest();
        getRequest.setSortEnum(1);
        getRequest.setInvoiceStatus(1);
        getRequest.setRefNo(cmd.getOrderNo());
        GetInvoiceListResponse getInvoiceListResponse = xiaohongshuRpc.pageInvoiceApply(shop, getRequest);
        if (CollectionUtils.isEmpty(getInvoiceListResponse.getInvoiceRecords())) {
            throw BizException.of("未查询到订单的待开票信息");
        }
        GetInvoiceListResponse.InvoiceRecord invoiceRecord = getInvoiceListResponse.getInvoiceRecords().get(0);
        // 上传发票文件
        ConfirmInvoiceRequest request = xiaohongshuConverter.toConfirmInvoiceRequest(cmd, invoiceRecord.getXhsInvoiceNo());
        xiaohongshuRpc.confirmInvoice(shop, request);
        return true;
    }

    @Override
    public InvoiceAmountDTO getInvoiceAmount(ShopDO shop, String orderNo) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<CommentDTO> pageComments(ShopDO shop, CommentPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<RefundDTO> pageRefunds(ShopDO shop, RefundPageQry qry) {
        ListAfterSaleInfosRequest request = new ListAfterSaleInfosRequest();
        request.setPageNo(qry.getPageIndex());
        request.setPageSize(qry.getPageSize());
        // 更新时间，范围不大于30分钟
        request.setTimeType(2);
        request.setStartTime(DateUtil.toEpochMilli(qry.getUpdateTimeStart()));
        request.setEndTime(DateUtil.toEpochMilli(qry.getUpdateTimeEnd()));
        ListAfterSaleInfosResponse response = xiaohongshuRpc.pageRefund(shop, request);
        List<RefundDTO> refundList = response.getAfterSaleBasicInfos().stream()
                .map(xiaohongshuConverter::toAdapterRefundDTO)
                .collect(Collectors.toList());
        return PageResponse.of(refundList, response.getTotalCount().intValue(), response.getPageSize(), response.getPageNo());
    }

    @Override
    public RefundDTO getRefund(ShopDO shop, RefundGetQry qry) {
        GetAfterSaleInfoResponse refund = xiaohongshuRpc.getRefund(shop, qry.getRefundId());
        if (refund == null) {
            return null;
        }
        RefundDTO dto = xiaohongshuConverter.toAdapterRefundItemDTO(refund);
        dto.setOriginData(GsonUtil.objectToJson(refund));
        return dto;
    }

    @Override
    public ShopLogisticsOrderDTO createLogisticsOrder(ShopDO shop, ShopLogisticsOrderCreateCmd cmd) {
        XiaohongshuLogisticsCompany logisticsCompany = XiaohongshuLogisticsCompany.valueOf(cmd.getLogisticsCompany().name());
        ElectronicBillOrdersCreateRequest request = xiaohongshuConverter.toElectronicBillOrdersCreateRequest(cmd, shop, logisticsCompany);
        // 设置国补订单扩展字段
        if (CollectionUtils.isNotEmpty(cmd.getBarcodes())) {
            GetOrderDetailResponse order = xiaohongshuRpc.getOrder(shop, cmd.getShopOrderNo());
            if (StringUtils.isNotBlank(order.getSubsidyWpServiceCode())) {
                ShopLogisticsBarcodeCmd barcode = cmd.getBarcodes().get(0);
                LogisticsOrderExtraInfoDTO logisticsOrderExtraInfo = new LogisticsOrderExtraInfoDTO();
                logisticsOrderExtraInfo.setWpServiceCode(order.getSubsidyWpServiceCode());
                if (order.getSubsidySkuIdentifyCodeRequiredInfo().isSnRequired()) {
                    logisticsOrderExtraInfo.setSn(barcode.getBarcode());
                }
                if (order.getSubsidySkuIdentifyCodeRequiredInfo().isImei1Required()) {
                    logisticsOrderExtraInfo.setImei1(barcode.getImei());
                }
                request.setExtraInfo(GsonUtil.objectToJson(logisticsOrderExtraInfo));
            }
        }
        ElectronicBillOrdersCreateResponse.ElectronicBillPrintData response = xiaohongshuRpc.createLogisticsOrder(shop, request);
        return ShopLogisticsOrderDTO.builder()
                .wayBillNo(response.getWaybillCode())
                .orderDetail(response.getPrintData())
                .build();
    }

    @Override
    public boolean cancelLogisticsOrder(ShopDO shop, ShopLogisticsOrderCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void refundGoodsToWarehouse(ShopDO shop, RefundGoodsToWarehouseCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void barcodeUpload(ShopDO shop, BarcodeUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    /**
     * 检查订单已发货
     *
     * @param order 订单
     * @return 执行结果
     */
    private boolean checkOrderHasShipping(GetOrderDetailResponse order) {
        // 判断订单状态已发货，无需重复发货
        if (6 == order.getOrderStatus() || order.getOrderStatus() == 7) {
            log.warn("订单已发货，无需重复发货 {} {}", order.getOrderId(), order.getOrderStatus());
            return true;
        }
        // 判断订单状态不是待发货,部分发货，抛异常
        if (4 != order.getOrderStatus() && 5 != order.getOrderStatus()) {
            String msg = String.format("%s %s orderState: %s ",
                    BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrDesc(), order.getOrderId(), order.getOrderStatus());
            throw new BizException(BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrCode(), msg);
        }
        return false;
    }

    /**
     * 根据订单明细里的 skuIds 并且转换成统一订单数据
     *
     * @param platformOrder 平台订单数据
     * @return 统一订单数据
     */
    private OrderDTO splitSkuIdsAndConvertToAdapterOrderDTO(GetOrderDetailResponse platformOrder, List<OrderReceiverInfo> orderReceivers) {
        // 拆分平台维护的 skuId
        List<GetOrderDetailResponse.OrderSkuDTOV3> skus = SkuUtil.splitSkuIdsAndCloneItem(platformOrder.getSkuList(),
                GetOrderDetailResponse.OrderSkuDTOV3::getErpcode, GetOrderDetailResponse.OrderSkuDTOV3::setErpcode);
        platformOrder.setSkuList(skus);
        return xiaohongshuConverter.toAdapterOrderDTO(platformOrder, orderReceivers.get(0));
    }

}
