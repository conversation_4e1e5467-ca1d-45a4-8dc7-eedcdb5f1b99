package com.xtc.marketing.adapterservice.shop.converter;

import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.InternalShopOrderDTO;
import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.InternalShopOrderItemDTO;
import com.xtc.marketing.adapterservice.rpc.internalshop.internalshopdto.InternalShopRefundDTO;
import com.xtc.marketing.adapterservice.shop.dto.InvoiceApplyDTO;
import com.xtc.marketing.adapterservice.shop.dto.OrderDTO;
import com.xtc.marketing.adapterservice.shop.dto.OrderItemDTO;
import com.xtc.marketing.adapterservice.shop.dto.RefundDTO;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.MoneyUtil;
import org.mapstruct.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.StringJoiner;

@Mapper(
        componentModel = "spring",
        uses = {BaseShopConverter.class},
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface InternalShopConverter {

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "sn")
    @Mapping(target = "orderState", source = "orderStatus")
    @Mapping(target = "orderStateDesc", source = "orderStatusText")
    @Mapping(target = "sellerId", source = "sellerId")
    @Mapping(target = "sellerName", source = "sellerName")
    @Mapping(target = "sellerMemo", source = "sellerRemark")
    @Mapping(target = "buyerId", source = "source", qualifiedByName = "buyerIdJoiner")
    @Mapping(target = "buyerName", source = "source", qualifiedByName = "buyerIdJoiner")
    @Mapping(target = "buyerMemo", source = "buyerRemark")
    @Mapping(target = "receiverName", source = "shipName")
    @Mapping(target = "receiverMobile", source = "shipMobile")
    @Mapping(target = "receiverProvince", source = "shipProvince")
    @Mapping(target = "receiverCity", source = "shipCity")
    @Mapping(target = "receiverDistrict", source = "shipCounty")
    @Mapping(target = "receiverAddress", source = "shipAddr")
    // 金额和时间
    @Mapping(target = "priceTotal", source = "orderPrice", qualifiedByName = "yuanToCent")
    @Mapping(target = "payment", source = "payMoney", qualifiedByName = "yuanToCent")
    @Mapping(target = "shippingPayment", source = "shippingPrice", qualifiedByName = "yuanToCent")
    // 这里的优惠是平台优惠，而订单服务（自营商城）的优惠是商家优惠，不是平台给的，所以这里设置为0
    @Mapping(target = "discount", constant = "0")
    @Mapping(target = "orderTime", source = "createTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "payTime", source = "paymentTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "shippingTime", source = "shipTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "completedTime", source = "completeTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "updateTime", source = "lastUpdateTime", qualifiedByName = "toLocalDateTime")
    // 子对象转换
    @Mapping(target = "items", source = "items", qualifiedByName = "toAdapterOrderItemDTO")
    @Mapping(target = "invoiceApply", source = "shipName", qualifiedByName = "toAdapterInvoiceApplyDTO")
    OrderDTO toAdapterOrderDTO(InternalShopOrderDTO source);

    /**
     * 转换统一订单明细
     *
     * @param source 平台订单明细
     * @return 统一订单明细
     */
    @Named("toAdapterOrderItemDTO")
    @Mapping(target = "itemNo", source = "orderItemsId")
    @Mapping(target = "productId", source = "goodsId")
    @Mapping(target = "productName", source = "goodsName")
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "skuName", source = "goodsName")
    @Mapping(target = "skuErpCode", source = "skuSn")
    @Mapping(target = "num", source = "num")
    @Mapping(target = "unitPrice", source = "originalPrice", qualifiedByName = "yuanToCent")
    @Mapping(target = "priceTotal", source = "subtotal", qualifiedByName = "yuanToCent")
    @Mapping(target = "payment", source = "purchasePrice", qualifiedByName = "yuanToCent")
    OrderItemDTO toAdapterOrderItemDTO(InternalShopOrderItemDTO source);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    List<RefundDTO> toAdapterRefundDTO(List<InternalShopRefundDTO> source);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "orderSn")
    @Mapping(target = "serviceNo", source = "sn")
    @Mapping(target = "serviceType", source = "refundType")
    @Mapping(target = "serviceState", source = "refundStatus")
    @Mapping(target = "applyReason", source = "refundReason")
    @Mapping(target = "sellerId", source = "sellerId")
    @Mapping(target = "sellerName", source = "sellerName")
    @Mapping(target = "sellerMemo", source = "sellerRemark")
    @Mapping(target = "buyerId", source = "memberId")
    @Mapping(target = "buyerName", source = "memberName")
    @Mapping(target = "returnExpressCompany", source = "refundShipId")
    @Mapping(target = "returnWaybillNo", source = "refundShipSn")
    // 金额和时间
    @Mapping(target = "refundApplyAmount", source = "refundPrice", qualifiedByName = "yuanDoubleToCent")
    @Mapping(target = "refundAmount", source = "refundPrice", qualifiedByName = "yuanDoubleToCent")
    @Mapping(target = "createTime", source = "createTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "refundTime", source = "refundTime", qualifiedByName = "toLocalDateTime")
    RefundDTO toAdapterRefundDTO(InternalShopRefundDTO.RefundDTO source);

    /**
     * 转换统一发票申请数据
     *
     * @param invoiceTitle 发票抬头
     * @return 统一发票申请数据
     */
    @Named("toAdapterInvoiceApplyDTO")
    @Mapping(target = "invoiceType", constant = "ELECTRONIC")
    @Mapping(target = "invoiceTitleType", constant = "PERSONAL")
    InvoiceApplyDTO toAdapterInvoiceApplyDTO(String invoiceTitle);

    /**
     * 拼接用户id
     *
     * @param platformOrder 平台订单
     * @return 用户id
     */
    @Named("buyerIdJoiner")
    default String buyerIdJoiner(InternalShopOrderDTO platformOrder) {
        StringJoiner buyerId = new StringJoiner("-");
        buyerId.add(platformOrder.getQyUserId());
        buyerId.add(platformOrder.getMemberName());
        return buyerId.toString();
    }

    /**
     * 元转分
     *
     * @param yuan 元
     * @return 分
     */
    @Named("yuanToCent")
    default Integer yuanToCent(BigDecimal yuan) {
        return MoneyUtil.yuanToCent(yuan);
    }

    /**
     * 元转分
     *
     * @param yuan 元
     * @return 分
     */
    @Named("yuanDoubleToCent")
    default Integer yuanDoubleToCent(Double yuan) {
        return MoneyUtil.yuanToCent(yuan);
    }

    /**
     * 时间转换
     *
     * @param epochSecond 秒
     * @return LocalDateTime
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(Long epochSecond) {
        if (epochSecond == null || epochSecond < 1) {
            return null;
        }
        return DateUtil.toLocalDateTime(epochSecond);
    }

}
