package com.xtc.marketing.adapterservice.logistics.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.logistics.converter.EmsLogisticsConverter;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsCloudPrintDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsOrderDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCancelOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCloudPrintCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsInterceptCmd;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsOrderGetQry;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsRouteListQry;
import com.xtc.marketing.adapterservice.rpc.ems.EmsRpc;
import com.xtc.marketing.adapterservice.rpc.ems.emsdto.EmsBaseDTO;
import com.xtc.marketing.adapterservice.rpc.ems.emsdto.EmsRouteDTO;
import com.xtc.marketing.adapterservice.rpc.ems.emsdto.command.EmsCreateOrderCmd;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = LogisticsExtConstant.USE_CASE, scenario = LogisticsExtConstant.SCENARIO_EMS)
public class EmsLogisticsExt implements LogisticsExtPt {

    private final EmsRpc emsRpc;
    private final EmsLogisticsConverter emsLogisticsConverter;

    @Override
    public List<LogisticsRouteDTO> routes(LogisticsAccountDO account, LogisticsRouteListQry qry) {
        List<EmsRouteDTO> listRouteDTO = emsRpc.searchRoutes(account, qry.getWaybillNo());
        return emsLogisticsConverter.toAdapterRouteDTO(listRouteDTO);
    }

    @Override
    public LogisticsOrderDTO createOrder(LogisticsAccountDO account, LogisticsCreateOrderCmd cmd) {
        EmsCreateOrderCmd createOrderCmd = emsLogisticsConverter.toEmsCreateOrderCmd(cmd);
        EmsBaseDTO order = emsRpc.createOrder(account, createOrderCmd);
        return LogisticsOrderDTO.builder()
                .wayBillNo(order.getBody().getAsJsonObject().get("waybill_no").getAsString())
                .build();
    }

    @Override
    public String getOrder(LogisticsAccountDO account, LogisticsOrderGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public String getWaybillNo(LogisticsAccountDO account, LogisticsOrderGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean cancelOrder(LogisticsAccountDO account, LogisticsCancelOrderCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean intercept(LogisticsAccountDO account, LogisticsInterceptCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public LogisticsCloudPrintDTO cloudPrint(LogisticsAccountDO account, LogisticsCloudPrintCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

}
