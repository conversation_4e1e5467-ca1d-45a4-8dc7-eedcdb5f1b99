package com.xtc.marketing.adapterservice.shop.executor.command;

import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.xtc.marketing.adapterservice.cache.RedissonUtil;
import com.xtc.marketing.adapterservice.constant.SystemConstant;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.executor.extension.ShopExtConstant;
import com.xtc.marketing.adapterservice.shop.executor.extension.ShopExtPt;
import com.xtc.marketing.adapterservice.shop.repository.ShopRepository;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 刷新店铺 token 数据
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class RefreshTokenCmdExe {

    private final ShopRepository shopRepository;
    private final ExtensionExecutor extensionExecutor;
    private final RedissonUtil redissonUtil;

    /**
     * 应用过期前推送告警的时间间隔
     */
    private static final Duration APP_EXPIRE_ALARM_DURATION = Duration.ofDays(7);
    /**
     * 告警间隔时间
     */
    private static final Duration ALARM_DURATION = Duration.ofDays(1);
    /**
     * API：企业微信告警机器人
     */
    private static final String API_WECHAT_WORK_ALARM = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send" +
            "?key=66b40bd7-6c5c-4905-b30d-1106b936accc";

    public void execute() {
        // 查询快过期的需要刷新 token 的店铺列表
        LocalDateTime expireAlarmTime = LocalDateTime.now().plus(APP_EXPIRE_ALARM_DURATION);
        List<ShopDO> shops = shopRepository.listEnabledTokenRefresh(expireAlarmTime);
        if (shops.isEmpty()) {
            log.info("没有需要刷新 token 的店铺");
            return;
        }
        // 遍历处理，捕获每次处理的异常，不影响后续处理，并推送企业微信消息提醒人工处理
        shops.forEach(shop -> {
            log.info("shop: {}", shop);
            try {
                refreshToken(shop);
            } catch (Exception e) {
                log.warn("刷新店铺token数据异常，平台: {}", shop.getPlatformCode(), e);
                sendWechatWorkAlarm(shop);
            }
        });
    }

    /**
     * 刷新店铺 token 数据
     *
     * @param shop 店铺
     */
    private void refreshToken(ShopDO shop) {
        // 获取新的 token 数据
        BizScenario scenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID, ShopExtConstant.USE_CASE, shop.getPlatformCode());
        ShopDO newToken = extensionExecutor.execute(ShopExtPt.class, scenario, executor -> executor.getShopToken(shop));
        log.info("新的店铺token数据: {}", newToken);
        // 新的 token 数据为空，推送企业微信消息提醒人工处理
        if (newToken == null || StringUtils.isBlank(newToken.getAppAccessToken()) || newToken.getAppExpireTime() == null) {
            log.info("新的店铺token数据为空，需要检查");
            this.sendWechatWorkAlarm(shop);
            return;
        }
        // 没有新的 token 数据不做处理： token 数据无变化，并且数据有效期相差 60 秒以内（误差范围内）
        Duration betweenExpireTime = Duration.between(newToken.getAppExpireTime(), shop.getAppExpireTime());
        if (newToken.getAppAccessToken().equals(shop.getAppAccessToken()) && Math.abs(betweenExpireTime.getSeconds()) < 60) {
            log.info("新的店铺token数据无变化，不做更新");
            return;
        }
        // 更新新的 token 数据
        this.updateNewToken(newToken);
    }

    /**
     * 保存新的 token 数据
     *
     * @param newToken 新的 token 数据
     */
    private void updateNewToken(ShopDO newToken) {
        ShopDO updateShop = ShopDO.builder()
                .id(newToken.getId())
                .appExpireTime(newToken.getAppExpireTime())
                .appSessionKey(newToken.getAppSessionKey())
                .appAccessToken(newToken.getAppAccessToken())
                .appRefreshToken(newToken.getAppRefreshToken())
                .build();
        boolean updated = shopRepository.updateById(updateShop);
        log.info("店铺token数据保存结果: {}", updated);
    }

    /**
     * 推送企业微信告警消息
     *
     * @param shop 店铺
     */
    private void sendWechatWorkAlarm(ShopDO shop) {
        String alamKey = SystemConstant.SYSTEM_NAME + ":shop_token_alarm:" + shop.getShopCode();
        long incr = redissonUtil.incr(alamKey, ALARM_DURATION);
        if (incr > 1) {
            log.info("过期告警消息已推送过，不再重复推送");
            return;
        }
        String msgFormat = "{\"msgtype\":\"text\",\"text\":{" +
                "\"content\":\"店铺 token 数据过期，需要更新！\n过期时间：%s\n平台: %s（%s）\n店铺: %s（%s）\"," +
                "\"mentioned_list\": [\"20261647\"]" +
                "}}";
        String msg = String.format(msgFormat, DateUtil.toString(shop.getAppExpireTime()), shop.getPlatformName(),
                shop.getPlatformCode(), shop.getShopName(), shop.getShopCode());
        HttpUtil.post(API_WECHAT_WORK_ALARM, msg);
        log.info("推送企业微信告警消息: {}", msgFormat);
    }

}
