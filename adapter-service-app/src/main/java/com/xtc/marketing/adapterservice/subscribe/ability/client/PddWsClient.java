package com.xtc.marketing.adapterservice.subscribe.ability.client;

import com.google.common.collect.ImmutableMap;
import com.google.gson.annotations.SerializedName;
import com.pdd.pop.sdk.message.MessageHandler;
import com.pdd.pop.sdk.message.WsClient;
import com.pdd.pop.sdk.message.model.Message;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.subscribe.ability.domainservice.SubscribeLogDomainService;
import com.xtc.marketing.adapterservice.subscribe.ability.domainservice.SubscribePushOmsDomainService;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * 拼多多消息订阅 WsClient
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class PddWsClient {

    private final SubscribePushOmsDomainService subscribePushOmsDomainService;

    /**
     * 连接地址
     */
    private static final String CONNECT_URL = "wss://message-api.pinduoduo.com";
    /**
     * WsClient 客户端缓存
     */
    private static final Map<String, WsClient> WS_CLIENT = new ConcurrentHashMap<>();

    /**
     * 清除 WsClient 缓存
     *
     * @param shop 店铺
     * @return 被清除的 WsClient 为 null 说明没有缓存
     */
    public WsClient removeWsClient(ShopDO shop) {
        return WS_CLIENT.remove(shop.getShopCode());
    }

    /**
     * 获取消息订阅 WsClient
     *
     * @param shop     店铺
     * @param bizParam 业务参数
     * @return WsClient
     */
    public WsClient getWsClient(ShopDO shop, String bizParam) {
        return WS_CLIENT.computeIfAbsent(shop.getShopCode(), k -> newWsClient(shop, bizParam));
    }

    /**
     * 生成 WsClient
     *
     * @param shop     店铺
     * @param bizParam 业务参数
     * @return WsClient
     */
    private WsClient newWsClient(ShopDO shop, String bizParam) {
        // 平台与店铺的对应关系，业务参数预处理避免多次解析
        List<BizParam> platformList = GsonUtil.jsonToList(bizParam, BizParam.class);
        ImmutableMap<String, String> platformMap = Optional.ofNullable(platformList)
                .orElse(Collections.emptyList()).stream()
                .collect(ImmutableMap.toImmutableMap(BizParam::getMallId, BizParam::getPlatformId));
        // 消息处理逻辑
        MessageHandler messageHandler = message -> {
            SubscribeLogDomainService.logTraceId();
            log.info("PddWsClient - message: {}", GsonUtil.objectToJson(message));
            try {
                SubscribePushOmsDomainService.BizData bizData = this.buildBizData(message, platformMap);
                Consumer<SubscribePushOmsDomainService.BizData> dataConsumer = this.getDataConsumer(message.getType());
                if (dataConsumer != null) {
                    dataConsumer.accept(bizData);
                } else {
                    log.warn("PddWsClient - 未找到消息处理器 topic: {}, tradeId: {}", message.getType(), bizData.getTradeId());
                }
            } catch (Exception e) {
                log.warn("PddWsClient - 消息处理失败 {}", e.getMessage(), e);
            }
        };
        return new WsClient(CONNECT_URL, shop.getAppKey(), shop.getAppSecret(), messageHandler);
    }

    /**
     * 获取数据处理器
     * <p>消息主题与消息处理器映射</p>
     * <pre>
     *     退款单消息：pdd_refund
     * </pre>
     *
     * @param topic 消息主题
     * @return 数据处理器
     */
    private Consumer<SubscribePushOmsDomainService.BizData> getDataConsumer(String topic) {
        if (topic.startsWith("pdd_refund")) {
            return subscribePushOmsDomainService::refundPushToOms;
        }
        return null;
    }

    /**
     * 构建业务数据
     *
     * @param message     消息
     * @param platformMap 平台与店铺的对应关系
     * @return 业务数据
     */
    private SubscribePushOmsDomainService.BizData buildBizData(Message message, ImmutableMap<String, String> platformMap) {
        // 消息内容解析
        MessageContent content = GsonUtil.jsonToBean(message.getContent(), MessageContent.class);
        // 从关系中确定平台
        String platformId = platformMap.getOrDefault(content.getMallId(), null);
        return SubscribePushOmsDomainService.BizData.builder()
                .platformId(platformId).tradeId(content.getTradeId()).refundId(content.getRefundId()).build();
    }

    /**
     * 消息内容
     */
    @Getter
    @Setter
    @ToString
    private static class MessageContent {

        /**
         * 店铺id
         */
        @SerializedName(value = "mallId", alternate = {"mall_id"})
        private String mallId;
        /**
         * 订单号
         */
        @SerializedName(value = "tradeId", alternate = {"tid"})
        private String tradeId;
        /**
         * 退款单号
         */
        @SerializedName(value = "refundId", alternate = {"refund_id"})
        private String refundId;

    }

    /**
     * 业务参数
     */
    @Getter
    @Setter
    @ToString
    private static class BizParam {

        /**
         * 店铺id
         */
        private String mallId;
        /**
         * OMS平台id
         */
        private String platformId;

    }

}
