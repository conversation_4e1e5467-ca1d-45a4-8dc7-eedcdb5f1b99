package com.xtc.marketing.adapterservice.subscribe.ability.domainservice;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.UUID;

/**
 * 消息订阅日志领域服务
 */
@Slf4j
public class SubscribeLogDomainService {

    /**
     * 日志打印key：跟踪id
     */
    private static final String LOG_KEY_TRACE_ID = "trace.id";

    /**
     * 设置跟踪id
     */
    public static void logTraceId() {
        try {
            String traceId = UUID.randomUUID().toString().replace("-", "");
            MDC.put(LOG_KEY_TRACE_ID, traceId);
            log.info("put trace.id: {}", traceId);
        } catch (IllegalArgumentException e) {
            log.warn("put trace.id error: {}", e.getMessage(), e);
        }
    }

}
