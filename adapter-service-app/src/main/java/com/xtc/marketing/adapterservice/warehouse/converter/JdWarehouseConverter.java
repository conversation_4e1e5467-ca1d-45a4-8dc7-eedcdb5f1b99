package com.xtc.marketing.adapterservice.warehouse.converter;

import com.lop.open.api.sdk.domain.IntegratedSupplyChain.JdlOpenPlatformSoService.querySoOrder.GoodsSerialNoQueryResult;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.JdlOpenPlatformSoService.querySoOrder.SoQueryRequest;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.JdlOpenPlatformSoService.querySoOrder.SoQueryResponse;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.JdlOpenPlatformStockService.queryWarehouseStockMergeByWarehouse.StockSummaryResultItem;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.JdlOpenPlatformStockService.searchShopStockFlow.ShopStockFlowQueryRequest;
import com.lop.open.api.sdk.domain.IntegratedSupplyChain.JdlOpenPlatformStockService.searchShopStockFlow.ShopStockFlowResult;
import com.lop.open.api.sdk.request.IntegratedSupplyChain.IntegratedsupplychainOrderDeliveryQueryV1LopRequest;
import com.lop.open.api.sdk.request.IntegratedSupplyChain.IntegratedsupplychainStockFlowShopstockQueryV1LopRequest;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.warehouse.dto.OutboundBarcodeDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.OutboundDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.OutboundDetailDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.WarehouseStockDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.query.OutboundDetailQry;
import com.xtc.marketing.adapterservice.warehouse.dto.query.OutboundPageQry;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.util.List;

@Mapper(
        componentModel = "spring",
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface JdWarehouseConverter {

    /**
     * 转换平台出库明细参数
     *
     * @param shop 店铺
     * @param qry  参数
     * @return 平台出库明细参数
     */
    @Mapping(target = "request", expression = "java(toSoQueryRequest(shop, qry))")
    IntegratedsupplychainOrderDeliveryQueryV1LopRequest toIntegratedsupplychainOrderDeliveryQueryV1LopRequest(
            ShopDO shop, OutboundDetailQry qry);

    /**
     * 转换平台出库明细参数
     *
     * @param shop 店铺
     * @param qry  参数
     * @return 平台出库明细参数
     */
    @Mapping(target = "salesPlatformDeliveryNo", source = "qry.shopOrderNo")
    @Mapping(target = "deliveryNo", source = "qry.orderId")
    @Mapping(target = "ownerNo", constant = "EBU4418055635758")
    @Mapping(target = "deliverySerialNoFlag", constant = "1")
    @Mapping(target = "pin", expression = "java(shop.getShopId().split(\"\\\\|\")[1])")
    SoQueryRequest toSoQueryRequest(ShopDO shop, OutboundDetailQry qry);

    @Mapping(target = "orderId", source = "source.erpDeliveryNo")
    @Mapping(target = "barcodes", source = "source.serialNoList")
    OutboundDetailDTO toOutboundDetails(SoQueryResponse source);

    /**
     * 转换统一出库详细数据
     *
     * @param source 参数
     * @return 统一出库详细数据
     */
    @Mapping(target = "goodsNo", source = "source.goodsNo")
    @Mapping(target = "barcode", source = "source.serialNo")
    OutboundBarcodeDTO toOutboundDetailDTO(GoodsSerialNoQueryResult source);

    /**
     * 转换平台分页出库参数
     *
     * @param shop 店铺
     * @param qry  参数
     * @return 转换平台分页出库参数
     */
    @Mapping(target = "request", expression = "java(toShopStockFlowQueryRequest(shop, qry))")
    IntegratedsupplychainStockFlowShopstockQueryV1LopRequest toIntegratedsupplychainStockFlowShopstockQueryV1LopRequest(ShopDO shop, OutboundPageQry qry);

    /**
     * 转换平台分页出库参数
     *
     * @param shop 店铺
     * @param qry  参数
     * @return 转换平台分页出库参数
     */
    @Mapping(target = "ownerNo", constant = "EBU4418055635758")
    @Mapping(target = "shopNo", constant = "ESP0020008996688")
    @Mapping(target = "startDate", source = "qry.startTimeStart", qualifiedByName = "timeToStr")
    @Mapping(target = "endDate", source = "qry.endTimeEnd", qualifiedByName = "timeToStr")
    @Mapping(target = "pin", expression = "java(shop.getShopId().split(\"\\\\|\")[1])")
    @Mapping(target = "currentPage", source = "qry.pageIndex")
    @Mapping(target = "pageSize", source = "qry.pageSize")
    ShopStockFlowQueryRequest toShopStockFlowQueryRequest(ShopDO shop, OutboundPageQry qry);

    /**
     * 转换统一出库数据
     *
     * @param source 参数
     * @return 统一出库数据
     */
    @Mapping(target = "skuErpCode", source = "source.erpGoodsSign")
    @Mapping(target = "orderId", source = "source.bizNo")
    @Mapping(target = "goodsNo", source = "source.goodsNo")
    OutboundDTO toOutboundDTO(ShopStockFlowResult source);

    List<OutboundDTO> toOutboundDTO(List<ShopStockFlowResult> jdlOpenPage);

    /**
     * 转换统一库存数据
     *
     * @param source 平台库存数据
     * @return 统一库存数据
     */
    List<WarehouseStockDTO> toWarehouseStockDTO(List<StockSummaryResultItem> source);

    /**
     * 转换统一库存数据
     *
     * @param source 平台库存数据
     * @return 统一库存数据
     */
    @Mapping(target = "warehouseCode", source = "ownerNo")
    @Mapping(target = "skuId", source = "goodsNo")
    @Mapping(target = "inventoryStatus", expression = "java(\"100\".equals(source.getGoodsLevel()) ? \"正品\" : \"残品\")")
    @Mapping(target = "onHandQuantity", source = "usableNum")
    @Mapping(target = "inTransitQuantity", constant = "0")
    @Mapping(target = "totalQuantity", source = "realNum")
    @Mapping(target = "availableQuantity", source = "usableNum")
    WarehouseStockDTO toWarehouseStockDTO(StockSummaryResultItem source);


    /**
     * 时间转换为字符串（毫秒）
     *
     * @param time 时间
     * @return 时间（毫秒）
     */
    @Named("timeToStr")
    default String timeToStr(LocalDateTime time) {
        if (time == null) {
            return "";
        }
        return DateUtil.toStringMillis(time);
    }

}

