package com.xtc.marketing.adapterservice.shop.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.doudian.open.api.afterSale_Detail.AfterSaleDetailRequest;
import com.doudian.open.api.afterSale_Detail.data.AfterSaleDetailData;
import com.doudian.open.api.afterSale_Detail.param.AfterSaleDetailParam;
import com.doudian.open.api.afterSale_List.AfterSaleListRequest;
import com.doudian.open.api.afterSale_List.data.AfterSaleListData;
import com.doudian.open.api.afterSale_List.param.AfterSaleListParam;
import com.doudian.open.api.logistics_newCreateOrder.data.EbillInfosItem;
import com.doudian.open.api.logistics_newCreateOrder.param.LogisticsNewCreateOrderParam;
import com.doudian.open.api.order_addOrderRemark.OrderAddOrderRemarkRequest;
import com.doudian.open.api.order_addOrderRemark.param.OrderAddOrderRemarkParam;
import com.doudian.open.api.order_batchDecrypt.OrderBatchDecryptRequest;
import com.doudian.open.api.order_batchDecrypt.data.OrderBatchDecryptData;
import com.doudian.open.api.order_batchDecrypt.param.CipherInfosItem;
import com.doudian.open.api.order_batchDecrypt.param.OrderBatchDecryptParam;
import com.doudian.open.api.order_invoiceList.OrderInvoiceListRequest;
import com.doudian.open.api.order_invoiceList.data.OrderInvoiceListData;
import com.doudian.open.api.order_invoiceList.param.OrderInvoiceListParam;
import com.doudian.open.api.order_logisticsAdd.param.OrderLogisticsAddParam;
import com.doudian.open.api.order_logisticsAdd.param.ProductOrdersItem;
import com.doudian.open.api.order_logisticsAdd.param.SerialNoListItem;
import com.doudian.open.api.order_logisticsAddMultiPack.param.OrderLogisticsAddMultiPackParam;
import com.doudian.open.api.order_orderDetail.data.ShopOrderDetail;
import com.doudian.open.api.order_searchList.OrderSearchListRequest;
import com.doudian.open.api.order_searchList.data.OrderSearchListData;
import com.doudian.open.api.order_searchList.data.ShopOrderListItem;
import com.doudian.open.api.order_searchList.data.SkuOrderListItem;
import com.doudian.open.api.order_searchList.param.OrderSearchListParam;
import com.doudian.open.api.token_create.data.TokenCreateData;
import com.google.common.collect.Lists;
import com.xtc.marketing.adapterservice.exception.BizErrorCode;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.tiktok.TikTokRpc;
import com.xtc.marketing.adapterservice.rpc.tiktok.tiktokdto.enums.TikTokLogisticsCompany;
import com.xtc.marketing.adapterservice.shop.converter.TikTokShopConverter;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.dto.query.*;
import com.xtc.marketing.adapterservice.shop.util.SkuUtil;
import com.xtc.marketing.adapterservice.util.BeanCopier;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = ShopExtConstant.USE_CASE, scenario = ShopExtConstant.SCENARIO_TIKTOK)
public class TikTokShopExt implements ShopExtPt {

    private final TikTokRpc tikTokRpc;
    private final TikTokShopConverter tikTokShopConverter;

    @Override
    public ShopDO getShopToken(ShopDO shop) {
        // 初始化
        ShopDO newToken = BeanCopier.copy(shop, ShopDO::new);

        // 生成 AccessToken
        TokenCreateData accessTokenData = tikTokRpc.createAccessToken(shop);
        if (accessTokenData == null) {
            return newToken;
        }

        // 设置新的 token 数据
        newToken.setAppAccessToken(accessTokenData.getAccessToken());
        newToken.setAppRefreshToken(accessTokenData.getRefreshToken());
        newToken.setAppExpireTime(LocalDateTime.now().plusSeconds(accessTokenData.getExpiresIn()));
        return newToken;
    }

    @Override
    public PageResponse<OrderDTO> pageOrders(ShopDO shop, OrderPageQry qry) {
        OrderSearchListRequest request = new OrderSearchListRequest();
        OrderSearchListParam param = request.getParam();
        param.setUpdateTimeStart(DateUtil.toEpochSecond(qry.getUpdateTimeStart()));
        param.setUpdateTimeEnd(DateUtil.toEpochSecond(qry.getUpdateTimeEnd()));
        param.setPage((long) qry.getPageIndex() - 1); // 页数从 0 开始
        param.setSize((long) qry.getPageSize());
        param.setOrderBy(TikTokRpc.ORDER_BY);
        param.setOrderAsc(false);

        OrderSearchListData orderSearchListData = tikTokRpc.pageOrders(shop, request);
        if (orderSearchListData == null || CollectionUtils.isEmpty(orderSearchListData.getShopOrderList())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }

        List<OrderDTO> orders = orderSearchListData.getShopOrderList().stream()
                .map(this::splitSkuIdsAndConvertToAdapterOrderDTO)
                .collect(Collectors.toList());
        return PageResponse.of(orders, orderSearchListData.getTotal().intValue(),
                orderSearchListData.getSize().intValue(), orderSearchListData.getPage().intValue() + 1);
    }

    @Override
    public OrderDTO getOrder(ShopDO shop, OrderGetQry qry) {
        ShopOrderDetail order = tikTokRpc.getOrder(shop, qry.getOrderNo());
        return Optional.ofNullable(order)
                .map(originOrder -> {
                    OrderDTO orderDTO = this.splitSkuIdsAndConvertToAdapterOrderDTO(originOrder);
                    orderDTO.setOriginOrderData(GsonUtil.objectToJson(originOrder));
                    return orderDTO;
                })
                .orElse(null);
    }

    @Override
    public boolean orderRemark(ShopDO shop, OrderRemarkCmd cmd) {
        OrderAddOrderRemarkRequest request = new OrderAddOrderRemarkRequest();
        OrderAddOrderRemarkParam param = request.getParam();
        param.setOrderId(cmd.getOrderNo());
        param.setRemark(cmd.getRemark());
        param.setIsAddStar(Boolean.TRUE.toString());
        param.setStar("4");
        return tikTokRpc.orderRemark(shop, request);
    }

    @Override
    public boolean orderShipping(ShopDO shop, OrderShippingCmd cmd) {
        ShopOrderDetail order = tikTokRpc.getOrder(shop, cmd.getOrderNo());
        boolean hasShipping = this.checkOrderHasShipping(cmd.getOrderNo(), order);
        if (hasShipping) {
            return true;
        }
        TikTokLogisticsCompany logisticsCompany = TikTokLogisticsCompany.valueOf(cmd.getLogisticsCompany().name());
        // 国家补贴订单，走正常订单发货接口，推送发货条码
        boolean isNationalSubsidy = order.getSkuOrderList().stream()
                .anyMatch(sku -> sku.getSerialNoInfo() != null && sku.getSerialNoInfo().getShippingNeedCheckCodes() != null);
        if (isNationalSubsidy) {
            OrderLogisticsAddParam param = tikTokShopConverter.toOrderLogisticsAddParam(cmd, logisticsCompany);
            // 生成平台发货条码
            List<ProductOrdersItem> productOrders = this.buildProductOrdersItem(cmd.getBarcodes());
            param.setProductOrders(productOrders);
            return tikTokRpc.orderShipping(shop, param);
        }
        // 过滤已退款的商品明细数据
        List<com.doudian.open.api.order_orderDetail.data.SkuOrderListItem> skuList = order.getSkuOrderList().stream()
                .filter(sku -> sku.getOrderStatus() != 4)
                .collect(Collectors.toList());
        // 订单发货，部分发货未退款的商品
        OrderLogisticsAddMultiPackParam param = tikTokShopConverter.toOrderLogisticsAddMultiPackParam(cmd, logisticsCompany, skuList);
        return tikTokRpc.orderShippingMultiPack(shop, param);
    }

    @Override
    public boolean orderShippingCancel(ShopDO shop, OrderShippingCancelCmd cmd) {
        return tikTokRpc.orderShippingCancel(shop, cmd.getRefundId());
    }

    @Override
    public boolean orderDummyShipping(ShopDO shop, OrderDummyShippingCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public OrderDecryptDTO orderDecrypt(ShopDO shop, OrderDecryptCmd cmd) {
        OrderBatchDecryptRequest request = new OrderBatchDecryptRequest();
        OrderBatchDecryptParam param = request.getParam();
        List<CipherInfosItem> cipherInfosItems = cmd.getCiphers().stream()
                .map(cipherText -> {
                    CipherInfosItem cipherInfosItem = new CipherInfosItem();
                    cipherInfosItem.setAuthId(cmd.getOrderNo());
                    cipherInfosItem.setCipherText(cipherText);
                    return cipherInfosItem;
                })
                .collect(Collectors.toList());
        param.setCipherInfos(cipherInfosItems);

        OrderBatchDecryptData batchDecryptData = tikTokRpc.orderDecrypt(shop, request);
        if (batchDecryptData == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(batchDecryptData.getDecryptInfos())) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR.getErrCode(), GsonUtil.objectToJson(batchDecryptData.getCustomErr()));
        }
        return tikTokShopConverter.toAdapterOrderDecryptDTO(batchDecryptData);
    }

    @Override
    public PageResponse<InvoiceApplyDTO> pageInvoiceApply(ShopDO shop, InvoiceApplyPageQry qry) {
        OrderInvoiceListRequest request = new OrderInvoiceListRequest();
        OrderInvoiceListParam param = request.getParam();
        // 1：待开票
        param.setStatus(1);
        // 页数从 0 开始
        param.setPageNo(qry.getPageIndex() - 1);
        param.setPageSize(qry.getPageSize());
        param.setStartTime(DateUtil.toEpochSecond(qry.getStartTime()));
        param.setEndTime(DateUtil.toEpochSecond(qry.getEndTime()));
        OrderInvoiceListData invoiceListData = tikTokRpc.getInvoiceApply(shop, request);
        if (invoiceListData == null || CollectionUtils.isEmpty(invoiceListData.getInvoiceList())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }
        List<InvoiceApplyDTO> invoiceApplyDTO = tikTokShopConverter.toAdapterInvoiceApplyDTO(invoiceListData.getInvoiceList());
        return PageResponse.of(invoiceApplyDTO, invoiceListData.getTotal().intValue(), qry.getPageIndex(), qry.getPageSize());
    }

    @Override
    public InvoiceApplyDTO getInvoiceApply(ShopDO shop, InvoiceApplyGetQry qry) {
        OrderInvoiceListRequest request = new OrderInvoiceListRequest();
        OrderInvoiceListParam param = request.getParam();
        param.setOrderId(qry.getOrderNo());

        OrderInvoiceListData invoiceListData = tikTokRpc.getInvoiceApply(shop, request);
        if (invoiceListData == null || CollectionUtils.isEmpty(invoiceListData.getInvoiceList())) {
            return null;
        }
        return tikTokShopConverter.toAdapterInvoiceApplyDTO(invoiceListData.getInvoiceList().get(0));
    }

    @Override
    public boolean uploadInvoiceFile(ShopDO shop, OrderUploadInvoiceCmd cmd, MultipartFile invoiceFile) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceBase64(ShopDO shop, InvoiceUploadCmd cmd) {
        byte[] bytes = Base64.getDecoder().decode(cmd.getInvoiceFileBase());
        ByteArrayInputStream stream = new ByteArrayInputStream(bytes);
        return tikTokRpc.uploadInvoiceFile(shop, cmd.getOrderNo(), stream);
    }

    @Override
    public InvoiceAmountDTO getInvoiceAmount(ShopDO shop, String orderNo) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<CommentDTO> pageComments(ShopDO shop, CommentPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<RefundDTO> pageRefunds(ShopDO shop, RefundPageQry qry) {
        AfterSaleListRequest request = new AfterSaleListRequest();
        AfterSaleListParam param = request.getParam();
        param.setOrderBy(Collections.singletonList(TikTokRpc.ORDER_BY + " desc"));
        param.setPage((long) qry.getPageIndex() - 1); // 页数从 0 开始
        param.setSize((long) qry.getPageSize());
        param.setUpdateStartTime(DateUtil.toEpochSecond(qry.getUpdateTimeStart()));
        param.setUpdateEndTime(DateUtil.toEpochSecond(qry.getUpdateTimeEnd()));
        param.setOrderId(qry.getOrderNo());

        AfterSaleListData afterSaleListData = tikTokRpc.pageRefunds(shop, request);
        if (afterSaleListData == null || CollectionUtils.isEmpty(afterSaleListData.getItems())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }

        List<RefundDTO> listRefundDTO = tikTokShopConverter.toAdapterRefundDTO(afterSaleListData.getItems());
        return PageResponse.of(listRefundDTO, afterSaleListData.getTotal().intValue(), qry.getPageSize(), qry.getPageIndex());
    }

    @Override
    public RefundDTO getRefund(ShopDO shop, RefundGetQry qry) {
        AfterSaleDetailRequest request = new AfterSaleDetailRequest();
        AfterSaleDetailParam param = request.getParam();
        param.setAfterSaleId(qry.getRefundId());
        AfterSaleDetailData afterSaleDetailData = tikTokRpc.getRefund(shop, request);
        return tikTokShopConverter.toAdapterRefundDTO(afterSaleDetailData);
    }

    @Override
    public ShopLogisticsOrderDTO createLogisticsOrder(ShopDO shop, ShopLogisticsOrderCreateCmd cmd) {
        TikTokLogisticsCompany logisticsCompany = TikTokLogisticsCompany.valueOf(cmd.getLogisticsCompany().name());
        List<String> receiverList = GsonUtil.jsonToList(cmd.getReceiverOaid(), String.class);
        cmd.setReceiverName(receiverList.get(0));
        cmd.setReceiverMobile(receiverList.get(1));
        cmd.setReceiverAddress(receiverList.get(2));
        try {
            // 取号
            LogisticsNewCreateOrderParam requestParam = tikTokShopConverter.toLogisticsNewCreateOrderParam(cmd, shop, logisticsCompany);
            EbillInfosItem order = tikTokRpc.createLogisticsOrder(shop, requestParam);
            // 打印详情
            String orderDetail = tikTokRpc.printData(shop, order.getTrackNo(), logisticsCompany);
            return ShopLogisticsOrderDTO.builder().wayBillNo(order.getTrackNo()).orderDetail(orderDetail).build();
        } catch (Exception e) {
            String errorMessage = e.getMessage();
            // 根据异常信息定义业务异常，调用方根据 errCode 做特定的业务处理
            if (errorMessage.contains("未支付已关闭订单无需取单")) {
                throw BizException.of(BizErrorCode.B_ORDER_OrderStatusNoAllowCreateLogisticsOrder.getErrCode(), errorMessage, e);
            }
            throw e;
        }
    }

    @Override
    public boolean cancelLogisticsOrder(ShopDO shop, ShopLogisticsOrderCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void refundGoodsToWarehouse(ShopDO shop, RefundGoodsToWarehouseCmd cmd) {
        tikTokRpc.refundGoodsToWarehouse(shop, cmd.getRefundId());
    }

    @Override
    public void barcodeUpload(ShopDO shop, BarcodeUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    /**
     * 根据订单明细里的 skuIds 并且转换成统一订单数据
     *
     * @param platformOrder 平台订单
     * @return 统一订单数据
     */
    private OrderDTO splitSkuIdsAndConvertToAdapterOrderDTO(ShopOrderListItem platformOrder) {
        // 删除无需处理的赠品明细
        platformOrder.getSkuOrderList().removeIf(item -> this.isFreeOrderItem(item.getGivenProductType()));
        // 拆分平台维护的 skuId
        List<SkuOrderListItem> splitSkuItems = SkuUtil.splitSkuIdsAndCloneItem(platformOrder.getSkuOrderList(),
                SkuOrderListItem::getCode, SkuOrderListItem::setCode);
        platformOrder.setSkuOrderList(splitSkuItems);
        // 转换统一的数据结构
        return tikTokShopConverter.toAdapterOrderDTO(platformOrder);
    }

    /**
     * 根据订单明细里的 skuIds 并且转换成统一订单数据
     *
     * @param platformOrder 平台订单
     * @return 统一订单数据
     */
    private OrderDTO splitSkuIdsAndConvertToAdapterOrderDTO(ShopOrderDetail platformOrder) {
        // 删除无需处理的赠品明细
        platformOrder.getSkuOrderList().removeIf(item -> this.isFreeOrderItem(item.getGivenProductType()));
        // 拆分平台维护的 skuId
        List<com.doudian.open.api.order_orderDetail.data.SkuOrderListItem> splitSkuItems =
                SkuUtil.splitSkuIdsAndCloneItem(platformOrder.getSkuOrderList(),
                        com.doudian.open.api.order_orderDetail.data.SkuOrderListItem::getCode,
                        com.doudian.open.api.order_orderDetail.data.SkuOrderListItem::setCode);
        platformOrder.setSkuOrderList(splitSkuItems);
        // 转换统一的数据结构
        return tikTokShopConverter.toAdapterOrderDTO(platformOrder);
    }

    /**
     * 判断无需处理的赠品明细
     *
     * @param givenProductType 赠品类型
     * @return 执行结果
     */
    private boolean isFreeOrderItem(String givenProductType) {
        if (StringUtils.isBlank(givenProductType)) {
            return false;
        }
        return "FREE".equals(givenProductType);
    }

    /**
     * 检查订单发货
     *
     * @param orderNo 订单号
     * @param order   订单
     * @return 执行结果
     */
    private boolean checkOrderHasShipping(String orderNo, ShopOrderDetail order) {
        // 判断订单状态已发货，无需重复发货
        if (order.getMainStatus() == 3 || order.getMainStatus() == 5) {
            log.warn("订单已发货，无需重复发货 {} {}", orderNo, order.getMainStatus());
            return true;
        }
        // 判断订单状态不是待发货，抛异常
        if (order.getMainStatus() != 2 && order.getMainStatus() != 105) {
            // 例：平台的订单状态不符合推送发货状态的条件 6923580254658762042 orderState: 4（已取消） stepOrderState: 1（待确认/待支付）
            String msg = String.format("%s %s orderState: %s（%s） stepOrderState: %s（%s）",
                    BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrDesc(),
                    orderNo, order.getMainStatus(), order.getMainStatusDesc(), order.getOrderStatus(), order.getOrderStatusDesc());
            throw new BizException(BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrCode(), msg);
        }
        return false;
    }

    /**
     * 生成平台发货条码
     *
     * @param barcodes 发货条码
     * @return 平台发货条码
     */
    private List<ProductOrdersItem> buildProductOrdersItem(List<OrderShippingBarcodeCmd> barcodes) {
        return barcodes.stream()
                .filter(cmd -> StringUtils.isNotBlank(cmd.getBarcode()))
                .map(cmd -> {
                    ProductOrdersItem productOrdersItem = new ProductOrdersItem();
                    // 获取平台子订单号，由于子订单号会经过订单处理加工，所以额外使用 “-” 分割
                    String itemNo = cmd.getItemNo().split("-")[0];
                    productOrdersItem.setProductOrderId(itemNo);
                    List<SerialNoListItem> serialNoList = Lists.newArrayListWithCapacity(2);
                    // 条码
                    SerialNoListItem serialBarcodeItem = new SerialNoListItem();
                    serialBarcodeItem.setKey("SN");
                    serialBarcodeItem.setValue(Collections.singletonList(cmd.getBarcode()));
                    serialNoList.add(serialBarcodeItem);
                    // IMEI
                    if (StringUtils.isNotBlank(cmd.getImei())) {
                        SerialNoListItem serialImeiItem = new SerialNoListItem();
                        serialImeiItem.setKey("IMEI");
                        serialImeiItem.setValue(Collections.singletonList(cmd.getImei()));
                        serialNoList.add(serialImeiItem);
                    }
                    productOrdersItem.setSerialNoList(serialNoList);
                    return productOrdersItem;
                })
                .collect(Collectors.toList());
    }

}