package com.xtc.marketing.adapterservice.shop.util;

import com.google.common.base.Splitter;

import java.util.List;
import java.util.stream.Stream;

/**
 * 字符串分割工具类
 */
public class StringSplitter {

    private StringSplitter() {
    }

    /**
     * 分割字符串，默认使用英文逗号分隔
     *
     * @param str 字符串
     * @return 字符串列表
     */
    public static List<String> split(String str) {
        return split(str, ",");
    }

    /**
     * 分割字符串
     *
     * @param str       字符串
     * @param separator 分隔符
     * @return 字符串列表
     */
    public static List<String> split(String str, String separator) {
        return Splitter.on(separator).omitEmptyStrings().trimResults().splitToList(str);
    }

    /**
     * 分割字符串，默认使用英文逗号分隔
     *
     * @param str 字符串
     * @return 字符串流
     */
    public static Stream<String> splitToStream(String str) {
        return splitToStream(str, ",");
    }

    /**
     * 分割字符串
     *
     * @param str       字符串
     * @param separator 分隔符
     * @return 字符串流
     */
    public static Stream<String> splitToStream(String str, String separator) {
        return Splitter.on(separator).omitEmptyStrings().trimResults().splitToStream(str);
    }

}
