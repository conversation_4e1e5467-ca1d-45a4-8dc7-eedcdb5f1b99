package com.xtc.marketing.adapterservice.logistics.converter;

import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCancelOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCargoCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsInterceptCmd;
import com.xtc.marketing.adapterservice.rpc.sto.stodto.StoRouteDTO;
import com.xtc.marketing.adapterservice.rpc.sto.stodto.command.StoCancelOrderCmd;
import com.xtc.marketing.adapterservice.rpc.sto.stodto.command.StoCreateOrderCmd;
import com.xtc.marketing.adapterservice.rpc.sto.stodto.command.StoInterceptCmd;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.util.List;

@Mapper(
        componentModel = "spring",
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface StoLogisticsConverter {

    /**
     * 转换申通下单参数
     *
     * @param cmd 参数
     * @return 申通下单参数
     */
    @Mapping(target = "orderNo", source = "orderId")
    @Mapping(target = "orderSource", constant = "GDTCX")
    @Mapping(target = "sender.name", source = "senderName")
    @Mapping(target = "sender.mobile", source = "senderMobile")
    @Mapping(target = "sender.province", source = "senderProvince")
    @Mapping(target = "sender.city", source = "senderCity")
    @Mapping(target = "sender.area", source = "senderDistrict")
    @Mapping(target = "sender.address", source = "senderAddress")
    @Mapping(target = "receiver.name", source = "receiverName")
    @Mapping(target = "receiver.mobile", source = "receiverMobile")
    @Mapping(target = "receiver.province", source = "receiverProvince")
    @Mapping(target = "receiver.city", source = "receiverCity")
    @Mapping(target = "receiver.area", source = "receiverDistrict")
    @Mapping(target = "receiver.address", source = "receiverAddress")
    @Mapping(target = "cargo.goodsName", source = "cargos", qualifiedByName = "toGoodsName")
    @Mapping(target = "stoCustomerCmd.siteCode", constant = "440222")
    @Mapping(target = "stoCustomerCmd.customerName", constant = "440222000001")
    @Mapping(target = "stoCustomerCmd.logisticsOrderPassword", constant = "sto2024")
    StoCreateOrderCmd toStoCreateOrder(LogisticsCreateOrderCmd cmd);

    /**
     * 转换为统一路由
     *
     * @param route 申通路由列表
     * @return 统一路由
     */
    @Mapping(target = "node", source = "opOrgProvinceName")
    @Mapping(target = "time", source = "opTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "detail", source = "memo")
    @Mapping(target = "title", source = "waybillNo")
    LogisticsRouteDTO toLogisticsRoute(StoRouteDTO route);

    /**
     * 转换为统一路由列表
     *
     * @param routeList 申通路由列表
     * @return 统一路由列表
     */
    List<LogisticsRouteDTO> toLogisticsRoute(List<StoRouteDTO> routeList);

    /**
     * 转换为申通拦截参数
     *
     * @param cmd 统一拦截参数
     * @return 申通拦截参数
     */
    @Mapping(target = "waybillNo", source = "orderId")
    @Mapping(target = "interceptReason", constant = "1002")
    StoInterceptCmd toStoInterceptCmd(LogisticsInterceptCmd cmd);

    /**
     * 转换为申通取消参数
     *
     * @param cmd 统一取消参数
     * @return 申通取消参数
     */
    @Mapping(target = "waybillNo", source = "orderId")
    @Mapping(target = "sourceOrderId", source = "orderId")
    @Mapping(target = "remark", source = "cancelReason")
    @Mapping(target = "orderSource", constant = "GDTCX")
    StoCancelOrderCmd toStoCancelOrderCmd(LogisticsCancelOrderCmd cmd);

    /**
     * 转换为申通下单请求参数时，设置平台参数
     *
     * @param stoCmd 请求参数
     * @param cmd    参数
     */
    @AfterMapping
    default void setCreateOrderParam(@MappingTarget StoCreateOrderCmd stoCmd, LogisticsCreateOrderCmd cmd) {
        if (cmd.getPlatformJson() == null) {
            return;
        }
        StoCreateOrderCmd orderCmd;
        orderCmd = this.buildPlatformJson(cmd.getPlatformJson(), StoCreateOrderCmd.class);
        stoCmd.setOrderType(orderCmd.getOrderType());
        stoCmd.setBillType(orderCmd.getBillType());
        stoCmd.setOrderSource(orderCmd.getOrderSource());
        stoCmd.setPayModel(orderCmd.getPayModel());
        stoCmd.setStoCustomerCmd(orderCmd.getStoCustomerCmd());
    }

    /**
     * 转换为申通取消参数，设置平台参数
     *
     * @param stoCmd 申通取消参数
     * @param cmd    参数
     */
    @AfterMapping
    default void setCancelParam(@MappingTarget StoCancelOrderCmd stoCmd, LogisticsCancelOrderCmd cmd) {
        if (cmd.getPlatformJson() == null) {
            return;
        }
        StoCancelOrderCmd cancelOrderCmd;
        cancelOrderCmd = this.buildPlatformJson(cmd.getPlatformJson(), StoCancelOrderCmd.class);
        stoCmd.setOrderType(cancelOrderCmd.getOrderType());
        stoCmd.setOrderSource(cancelOrderCmd.getOrderSource());
    }

    /**
     * 转换平台参数
     *
     * @param platformJson  平台参数
     * @param responseClass 响应结果类型
     * @return 响应结果
     */
    default <T> T buildPlatformJson(String platformJson, Class<T> responseClass) {
        T platformCmd;
        try {
            platformCmd = GsonUtil.jsonToBean(platformJson, responseClass);
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_PARAM_ERROR, "申通快递下单参数转换异常 " + platformJson);
        }
        return platformCmd;
    }

    /**
     * 转换成申通货物名称
     *
     * @param cargos 货物列表
     * @return 申通货物名称
     */
    @Named("toGoodsName")
    default String toGoodsName(List<LogisticsCargoCmd> cargos) {
        return cargos.get(0).getName();
    }

    /**
     * 转换申通路由时间
     *
     * @param operationTime 平台参数
     * @return 订单查询参数
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(String operationTime) {
        return DateUtil.toLocalDateTime(operationTime);
    }

}
