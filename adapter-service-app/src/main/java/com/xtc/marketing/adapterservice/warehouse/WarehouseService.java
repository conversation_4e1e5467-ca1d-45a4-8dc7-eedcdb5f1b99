package com.xtc.marketing.adapterservice.warehouse;

import com.xtc.marketing.adapterservice.warehouse.dto.OutboundDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.OutboundDetailDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.WarehouseStockDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.command.InboundApplyCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.InboundCancelCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.OutboundApplyCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.OutboundCancelCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.query.OutboundDetailQry;
import com.xtc.marketing.adapterservice.warehouse.dto.query.OutboundPageQry;
import com.xtc.marketing.adapterservice.warehouse.dto.query.WarehouseStockQry;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;

import java.util.List;

public interface WarehouseService {

    /**
     * 分页查询出库单列表
     *
     * @param qry 参数
     * @return 出库单分页列表
     */
    PageResponse<OutboundDTO> pageOutbound(OutboundPageQry qry);

    /**
     * 查询出库单详情
     *
     * @param qry 参数
     * @return 出库单详情
     */
    OutboundDetailDTO getOutboundDetail(OutboundDetailQry qry);

    /**
     * 查询库存
     *
     * @param qry 参数
     * @return 库存
     */
    List<WarehouseStockDTO> queryStocks(WarehouseStockQry qry);

    /**
     * 申请入库
     *
     * @param cmd 参数
     * @return 平台入库单号
     */
    String applyInbound(InboundApplyCmd cmd);

    /**
     * 取消入库
     *
     * @param cmd 参数
     * @return 入库单号（业务方单号）
     */
    String cancelInbound(InboundCancelCmd cmd);

    /**
     * 申请出库
     *
     * @param cmd 参数
     * @return 平台出库单号
     */
    String applyOutbound(OutboundApplyCmd cmd);

    /**
     * 取消出库
     *
     * @param cmd 参数
     * @return 业务订单号
     */
    String cancelOutbound(OutboundCancelCmd cmd);

}
