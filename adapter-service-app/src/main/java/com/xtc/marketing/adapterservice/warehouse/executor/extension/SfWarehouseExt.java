package com.xtc.marketing.adapterservice.warehouse.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.sf.SfWarehouseXmlRpc;
import com.xtc.marketing.adapterservice.rpc.sf.enums.SfOutboundTradePlatform;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.*;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.command.SfInboundApplyXmlCmd;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.command.SfOutboundApplyXmlCmd;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.query.SfStocksXmlQry;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.repository.ShopRepository;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.warehouse.converter.SfWarehouseConverter;
import com.xtc.marketing.adapterservice.warehouse.dto.OutboundDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.OutboundDetailDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.WarehouseStockDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.command.InboundApplyCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.InboundCancelCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.OutboundApplyCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.OutboundCancelCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.query.OutboundDetailQry;
import com.xtc.marketing.adapterservice.warehouse.dto.query.OutboundPageQry;
import com.xtc.marketing.adapterservice.warehouse.dto.query.WarehouseStockQry;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 顺丰仓库扩展点实现
 */
@Slf4j
@RequiredArgsConstructor
@Extension(useCase = WarehouseExtConstant.USE_CASE, scenario = WarehouseExtConstant.SCENARIO_SF)
public class SfWarehouseExt implements WarehouseExtPt {

    private final SfWarehouseXmlRpc sfWarehouseXmlRpc;
    private final SfWarehouseConverter sfWarehouseConverter;
    private final ShopRepository shopRepository;

    @Override
    public List<WarehouseStockDTO> queryStocks(WarehouseStockQry qry) {
        // 转换为顺丰请求参数
        List<SfStocksXmlQry.Sku> qrySkus = qry.getSkuIds().stream()
                .map(SfStocksXmlQry.Sku::new).collect(Collectors.toList());
        SfStocksXmlQry sfStocksXmlQry = SfStocksXmlQry.builder().warehouseCode(qry.getWarehouseCode()).skus(qrySkus).build();
        // 请求顺丰接口，查询库存
        List<SfStockXmlDTO.Stock> stocks = sfWarehouseXmlRpc.queryStocks(sfStocksXmlQry);
        // 转换为统一的库存数据
        return stocks.stream()
                .map(stock -> sfWarehouseConverter.toAdapterStockDTO(stock, qry.getWarehouseCode()))
                .collect(Collectors.toList());
    }

    @Override
    public String applyInbound(InboundApplyCmd cmd) {
        SfInboundApplyXmlCmd sfInboundApplyXmlCmd = sfWarehouseConverter.toSfInboundApplyXmlCmd(cmd);
        SfInboundApplyXmlDTO sfInboundApplyXmlDTO = sfWarehouseXmlRpc.applyInbound(sfInboundApplyXmlCmd);
        return sfInboundApplyXmlDTO.getReceiptId();
    }

    @Override
    public String cancelInbound(InboundCancelCmd cmd) {
        SfInboundCancelXmlDTO sfInboundCancelXmlDTO = sfWarehouseXmlRpc.cancelInbound(cmd.getOrderId());
        return sfInboundCancelXmlDTO.getOrderId();
    }

    @Override
    public String applyOutbound(OutboundApplyCmd cmd) {
        SfOutboundApplyXmlCmd sfCmd = sfWarehouseConverter.toSfOutboundApplyXmlCmd(cmd);
        SfOutboundTradePlatform tradePlatform = SfOutboundTradePlatform.of(cmd.getPlatformCode());
        if (tradePlatform != null) {
            ShopDO shop = shopRepository.getByShopCode(cmd.getShopCode()).orElse(null);
            tradePlatform.setReceiver(cmd, sfCmd, shop);
        }
        // 设置国补微派编码
        if (StringUtils.isNotBlank(cmd.getPlatformJson())) {
            String wpServiceCode = GsonUtil.getAsString(cmd.getPlatformJson(), "wpServiceCode");
            if (StringUtils.isNotBlank(wpServiceCode)) {
                SfOutboundApplyXmlCmd.CarrierAddedServices services = new SfOutboundApplyXmlCmd.CarrierAddedServices();
                services.setAttr02(wpServiceCode);
                sfCmd.getCarrier().setCarrierAddedServices(Collections.singletonList(services));
            }
        }
        SfOutboundApplyXmlDTO sfOutboundApplyXmlDTO = sfWarehouseXmlRpc.applyOutbound(sfCmd);
        return sfOutboundApplyXmlDTO.getShipmentId();
    }

    @Override
    public String cancelOutbound(OutboundCancelCmd cmd) {
        SfOutboundCancelXmlDTO sfOutboundCancelXmlDTO = sfWarehouseXmlRpc.cancelOutbound(cmd.getOrderId());
        return sfOutboundCancelXmlDTO.getOrderId();
    }

    @Override
    public PageResponse<OutboundDTO> pageOutbound(OutboundPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public OutboundDetailDTO getOutboundDetail(OutboundDetailQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

}
