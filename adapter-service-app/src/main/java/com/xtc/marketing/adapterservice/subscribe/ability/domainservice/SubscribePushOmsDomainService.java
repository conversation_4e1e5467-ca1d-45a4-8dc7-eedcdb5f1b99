package com.xtc.marketing.adapterservice.subscribe.ability.domainservice;

import com.xtc.marketing.adapterservice.constant.SystemConstant;
import com.xtc.marketing.adapterservice.rpc.oms.OmsRpc;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.OmsBaseResponse;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.command.SyncInvoiceApplyCmd;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.command.SyncRefundCmd;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.command.SyncTradeCmd;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * 消息订阅推送OMS领域服务
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class SubscribePushOmsDomainService {

    private final OmsRpc omsRpc;

    /**
     * 无需重复申请发票
     */
    private static final String INVOICE_NOT_CREATE_AGAIN = "无需重复申请";

    /**
     * OMS 订单数据同步
     *
     * @param bizData 业务数据
     */
    public void tradePushToOms(BizData bizData) {
        try {
            SyncTradeCmd cmd = SyncTradeCmd.builder()
                    .platformId(bizData.getPlatformId())
                    .tradeIds(Collections.singletonList(bizData.getTradeId()))
                    .processTrade(false)
                    .pullTradeDetail(false)
                    .event(SystemConstant.SYSTEM_NAME)
                    .build();
            omsRpc.syncTrade(cmd);
            log.info("OMS 订单数据同步成功 {}", bizData.getTradeId());
        } catch (Exception e) {
            log.warn("OMS 订单数据同步异常 {} {}", bizData.getTradeId(), e.getMessage(), e);
        }
    }

    /**
     * OMS 退款单数据同步
     *
     * @param bizData 业务数据
     */
    public void refundPushToOms(BizData bizData) {
        try {
            SyncRefundCmd cmd = SyncRefundCmd.builder()
                    .platformId(bizData.getPlatformId())
                    .refundId(bizData.getRefundId())
                    .tradeId(bizData.getTradeId())
                    .event(SystemConstant.SYSTEM_NAME)
                    .build();
            omsRpc.syncRefund(cmd);
            log.info("OMS 退款单数据同步成功 {}", bizData.getRefundId());
        } catch (Exception e) {
            log.warn("OMS 退款单数据同步异常 {} {}", bizData.getRefundId(), e.getMessage(), e);
        }
    }

    /**
     * OMS 发票申请数据同步
     *
     * @param bizData 业务数据
     */
    public void invoiceApplyPushToOms(BizData bizData) {
        OmsBaseResponse<Void> omsResponse;
        try {
            SyncInvoiceApplyCmd cmd = SyncInvoiceApplyCmd.builder()
                    .platformId(bizData.getPlatformId())
                    .tradeId(bizData.getTradeId())
                    .event(SystemConstant.SYSTEM_NAME)
                    .build();
            omsResponse = omsRpc.syncInvoiceApply(cmd);
        } catch (Exception e) {
            log.warn("OMS 发票申请数据同步异常 {}", bizData.getTradeId());
            return;
        }
        // OMS响应无需重复申请发票，算作同步成功
        boolean invoiceNotCreateAgain = StringUtils.contains(omsResponse.getDesc(), INVOICE_NOT_CREATE_AGAIN);
        if (omsResponse.isFailure() && BooleanUtils.isFalse(invoiceNotCreateAgain)) {
            log.warn("OMS 发票申请数据同步异常 {}", bizData.getTradeId());
            return;
        }
        log.info("OMS 发票申请数据同步成功 {}", bizData.getTradeId());
    }

    /**
     * 业务数据
     */
    @Setter
    @ToString
    @Builder
    public static class BizData {

        /**
         * 平台id
         */
        private String platformId;
        /**
         * 订单号
         */
        private String tradeId;
        /**
         * 退款单号
         */
        private String refundId;

        public String getPlatformId() {
            return StringUtils.defaultString(platformId);
        }

        public String getTradeId() {
            return StringUtils.defaultString(tradeId);
        }

        public String getRefundId() {
            return StringUtils.defaultString(refundId);
        }

    }

}
