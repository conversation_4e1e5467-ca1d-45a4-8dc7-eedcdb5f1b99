package com.xtc.marketing.adapterservice.bill.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.xtc.dividendcenter.dividend.dto.InnerWeChatCertificateDTO;
import com.xtc.marketing.adapterservice.bill.dto.query.BillDownloadQry;
import com.xtc.marketing.adapterservice.bill.executor.query.WechatMchGetQryExe;
import com.xtc.marketing.adapterservice.rpc.wechatpay.WechatPayRpc;
import com.xtc.marketing.adapterservice.rpc.wechatpay.wechatdto.WechatPayBillDTO;
import com.xtc.marketing.adapterservice.util.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = BillExtConstant.USE_CASE, scenario = BillExtConstant.SCENARIO_WECHAT_PAY)
public class WechatPayBillExtPt implements BillExtPt {

    private final WechatPayRpc wechatPayRpc;
    private final WechatMchGetQryExe wechatMchGetQryExe;

    @Override
    public Resource billDownload(BillDownloadQry qry) {
        InnerWeChatCertificateDTO mch = wechatMchGetQryExe.execute(qry.getBizCode(), qry.getMchId());
        // 申请交易账单
        WechatPayBillDTO tradeBill = wechatPayRpc.getTradeBill(mch, DateUtil.toString(qry.getBillDate()));
        // 下载交易账单
        return wechatPayRpc.billDownload(mch, tradeBill.getDownloadUrl());
    }

}
