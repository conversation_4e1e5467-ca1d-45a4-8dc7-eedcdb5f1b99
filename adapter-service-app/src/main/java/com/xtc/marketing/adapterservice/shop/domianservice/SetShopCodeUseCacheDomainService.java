package com.xtc.marketing.adapterservice.shop.domianservice;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xtc.marketing.adapterservice.constant.SystemConstant;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.OrderDTO;
import com.xtc.marketing.adapterservice.shop.repository.ShopRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 为订单关联店铺代码（使用缓存）
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class SetShopCodeUseCacheDomainService {

    private final ShopRepository shopRepository;

    /**
     * 店铺缓存key格式
     */
    private static final String CACHE_SHOP_FORMAT = SystemConstant.SYSTEM_NAME + ":shop:%s";
    /**
     * 店铺缓存，过期时间 1 小时
     */
    private static final Cache<String, ShopDO> CACHE_SHOP = CacheBuilder.newBuilder()
            .expireAfterAccess(1L, TimeUnit.HOURS).build();

    /**
     * 为订单关联店铺代码
     *
     * @param order 订单
     */
    public void setShopCode(OrderDTO order) {
        // 订单的商户id与店铺的代理代码对应
        if (StringUtils.isBlank(order.getSellerId())) {
            return;
        }
        // 从缓存中获取店铺信息
        String cacheKey = this.buildCacheShopKey(order.getSellerId());
        ShopDO cacheShop = CACHE_SHOP.getIfPresent(cacheKey);
        if (cacheShop != null) {
            order.setShopCode(cacheShop.getShopCode());
            return;
        }
        // 读取数据库店铺信息
        shopRepository.getByAgentCode(order.getSellerId())
                .ifPresent(shopDO -> {
                    order.setShopCode(shopDO.getShopCode());
                    CACHE_SHOP.put(cacheKey, shopDO);
                });
    }

    /**
     * 构建店铺缓存key
     *
     * @param sellerId 商户id
     * @return 缓存key
     */
    private String buildCacheShopKey(String sellerId) {
        return String.format(CACHE_SHOP_FORMAT, sellerId);
    }

}
