package com.xtc.marketing.adapterservice.bill.executor.query;

import com.xtc.dividendcenter.dividend.dto.InnerCertificateDTO;
import com.xtc.dividendcenter.dividend.dto.InnerWeChatCertificateDTO;
import com.xtc.dividendcenter.dividend.dto.query.InnerApiCertificateQry;
import com.xtc.dividendcenter.dividend.feign.DividendMchInfoFeignClient;
import com.xtc.marketing.adapterservice.exception.BizErrorCode;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.dividendmch.AesUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Component
public class WechatMchGetQryExe {

    private final DividendMchInfoFeignClient dividendMchInfoFeignClient;

    /**
     * 获取微信商户
     *
     * @param bizCode            业务代码
     * @param dividendBusinessId 分利中心业务id
     * @return 微信商户
     */
    public InnerWeChatCertificateDTO execute(String bizCode, String dividendBusinessId) {
        if (StringUtils.isAnyBlank(bizCode, dividendBusinessId)) {
            throw SysException.of(SysErrorCode.S_PARAM_ERROR, "请填写正确的商户参数");
        }

        // 获取商户
        InnerApiCertificateQry qry = new InnerApiCertificateQry();
        qry.setSystemName(bizCode);
        qry.setThirdBusinessId(dividendBusinessId);
        SingleResponse<InnerWeChatCertificateDTO> response = dividendMchInfoFeignClient.weChatCertificate(qry);
        if (response.isFailure()) {
            throw BizException.of(BizErrorCode.B_MCH_DataIncorrect, "获取商户信息失败 response: {}" + response.getErrMessage());
        }

        // 校验数据
        InnerWeChatCertificateDTO weChatCertificate = response.getData();
        InnerCertificateDTO keyPem = weChatCertificate.getKeyPem();
        InnerCertificateDTO merchantCert = weChatCertificate.getMerchantCert();
        InnerCertificateDTO platFormCert = weChatCertificate.getPlatFormCert();
        boolean keyPemExist = keyPem != null && StringUtils.isNotBlank(keyPem.getEncryptCertificate());
        boolean merchantCertExist = merchantCert != null && StringUtils.isNotBlank(merchantCert.getSerialNo());
        boolean platFormCertExist = platFormCert != null && StringUtils.isNotBlank(platFormCert.getSerialNo());
        if (!keyPemExist || !merchantCertExist || !platFormCertExist) {
            log.error("商户信息不完整：{}", GsonUtil.objectToJson(weChatCertificate));
            throw BizException.of(BizErrorCode.B_MCH_DataIncorrect);
        }

        // 解密
        try {
            keyPem.setEncryptCertificate(AesUtil.decryptDataToString(keyPem.getEncryptCertificate()));
            merchantCert.setEncryptCertificate(AesUtil.decryptDataToString(merchantCert.getEncryptCertificate()));
            platFormCert.setEncryptCertificate(AesUtil.decryptDataToString(platFormCert.getEncryptCertificate()));
        } catch (Exception e) {
            throw BizException.of(BizErrorCode.B_MCH_DataIncorrect, "商户信息解密失败", e);
        }
        return weChatCertificate;
    }

}
