package com.xtc.marketing.adapterservice.notify.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.logistics.executor.query.LogisticsAccountGetQryExe;
import com.xtc.marketing.adapterservice.notify.dataobject.ReceiveLogDO;
import com.xtc.marketing.adapterservice.notify.dto.command.NotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.enums.NotifyEnum;
import com.xtc.marketing.adapterservice.rpc.sf.SfRpc;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

/**
 * 通知扩展点 - 顺丰物流国补通知
 */
@Slf4j
@RequiredArgsConstructor
@Extension(bizId = NotifyExtConstant.BIZ_ID_SF,
        useCase = NotifyExtConstant.USE_CASE_LOGISTICS, scenario = NotifyExtConstant.SCENARIO_NATIONAL_SUBSIDY)
public class SfLogisticsNationalSubsidyNotifyExt implements NotifyExtPt {

    private final LogisticsAccountGetQryExe logisticsAccountGetQryExe;
    private final SfRpc sfRpc;

    @Override
    public ReceiveLogDO createReceiveLog(NotifyEnum notify, NotifyReceiveCmd cmd) {
        JsonObject notifyData = GsonUtil.jsonToObject(cmd.getData());
        // 校验通知
        this.verifyNotify(notifyData);
        // 解析数据id：运单号
        String data = GsonUtil.getAsString(notifyData, "msgData");
        String dataId = GsonUtil.getAsString(data, "waybillNo");
        String responseStr;
        if (StringUtils.isNoneBlank(dataId, data)) {
            responseStr = this.responseSuccess();
        } else {
            log.info("解析通知数据异常，未解析到数据id：运单号");
            responseStr = this.responseFailure();
        }
        return ReceiveLogDO.builder().dataId(dataId).rawData(data).responseStr(responseStr).build();
    }

    @Override
    public ResponseEntity<String> notifyResponse(String responseStr) {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_FORM_URLENCODED).body(responseStr);
    }

    @Override
    public Object convertToNotifyBean(String data) {
        return data;
    }

    /**
     * 校验通知
     *
     * @param notifyData 通知数据
     */
    private void verifyNotify(JsonObject notifyData) {
        String partnerId = GsonUtil.getAsString(notifyData, "partnerID");
        LogisticsAccountDO account = logisticsAccountGetQryExe.getByClientCode(partnerId);
        boolean verifyNotify;
        try {
            verifyNotify = sfRpc.verifyCountrySubsidyNotify(account, notifyData);
        } catch (Exception e) {
            throw BizException.of("通知未通过验证");
        }
        if (BooleanUtils.isFalse(verifyNotify)) {
            throw BizException.of("通知未通过验证");
        }
    }

    /**
     * 成功响应
     *
     * @return 成功响应
     */
    private String responseSuccess() {
        return this.buildResponse(true, false, null, null);
    }

    /**
     * 失败响应
     *
     * @return 失败响应
     */
    private String responseFailure() {
        return this.buildResponse(false, true, "500", "系统异常");
    }

    /**
     * 构建响应
     *
     * @param success   成功标识
     * @param retryFlag 重试标识
     * @param errorCode 错误码
     * @param errorMsg  错误详细描述信息
     * @return 响应
     */
    private String buildResponse(boolean success, boolean retryFlag, String errorCode, String errorMsg) {
        JsonObject response = new JsonObject();
        response.addProperty("success", success);
        response.addProperty("retryFlag", retryFlag);
        response.addProperty("errorCode", errorCode);
        response.addProperty("errorMsg", errorMsg);
        return response.toString();
    }

}
