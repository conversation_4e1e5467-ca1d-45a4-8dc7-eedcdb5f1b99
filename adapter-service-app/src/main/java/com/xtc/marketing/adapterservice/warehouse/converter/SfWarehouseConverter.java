package com.xtc.marketing.adapterservice.warehouse.converter;

import com.xtc.marketing.adapterservice.rpc.sf.SfWarehouseRpc;
import com.xtc.marketing.adapterservice.rpc.sf.enums.SfInventoryStatus;
import com.xtc.marketing.adapterservice.rpc.sf.enums.SfOutboundTradePlatform;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.SfStockXmlDTO;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.command.SfInboundApplyXmlCmd;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.command.SfOutboundApplyXmlCmd;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.warehouse.dto.WarehouseStockDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.command.InboundApplyCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.OutboundApplyCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.WarehouseItemCmd;
import com.xtc.marketing.adapterservice.warehouse.enums.InventoryStatus;
import org.mapstruct.*;

import java.time.LocalDateTime;

@Mapper(
        componentModel = "spring",
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface SfWarehouseConverter {

    /**
     * 转换统一库存数据
     *
     * @param source 平台库存数据
     * @return 统一库存数据
     */
    @Mapping(target = "warehouseCode", source = "warehouseCode")
    @Mapping(target = "skuId", source = "source.skuId")
    @Mapping(target = "inventoryStatus", expression = "java(\"10\".equals(source.getInventoryStatus()) ? \"正品\" : \"残品\")")
    @Mapping(target = "onHandQuantity", source = "source.onHandQty")
    @Mapping(target = "inTransitQuantity", source = "source.inTransitQty")
    @Mapping(target = "totalQuantity", source = "source.totalQty")
    @Mapping(target = "availableQuantity", source = "source.availableQty")
    WarehouseStockDTO toAdapterStockDTO(SfStockXmlDTO.Stock source, String warehouseCode);

    /**
     * 转换平台入库申请单参数
     *
     * @param source 统一入库申请单参数
     * @return 平台入库申请参数
     */
    @Mapping(target = "vendorCode", constant = SfWarehouseRpc.COMPANY_CODE)
    @Mapping(target = "warehouseCode", source = "warehouseCode")
    @Mapping(target = "orderId", source = "orderId")
    @Mapping(target = "orderDate", expression = "java(nowDateTime())")
    @Mapping(target = "scheduledReceiptDate", source = "scheduledReceiptTime", qualifiedByName = "timeToStr")
    @Mapping(target = "items", source = "items")
    SfInboundApplyXmlCmd toSfInboundApplyXmlCmd(InboundApplyCmd source);

    /**
     * 转换平台入库申请单货物参数
     *
     * @param source 统一仓库货物参数
     * @return 平台入库申请单货物参数
     */
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "quantity", source = "quantity")
    SfInboundApplyXmlCmd.Item toSfInboundApplyXmlCmdItem(WarehouseItemCmd source);

    /**
     * 转换平台出库申请单参数
     *
     * @param source 统一出库申请单参数
     * @return 平台出库申请单参数
     */
    @Mapping(target = "warehouseCode", source = "warehouseCode")
    @Mapping(target = "orderId", source = "orderId")
    @Mapping(target = "tradePlatform", source = "platformCode", qualifiedByName = "getTradePlatform")
    @Mapping(target = "tradeOrder", source = "shopOrderNo")
    @Mapping(target = "carrier.carrierProduct", source = "logisticsProduct")
    @Mapping(target = "fromFlag", expression = "java(source.getUseSender() ? \"10\" : \"20\")")
    @Mapping(target = "sender.senderCompany", source = "senderCompany")
    @Mapping(target = "sender.senderName", source = "senderName")
    @Mapping(target = "sender.senderPhone", source = "senderPhone")
    @Mapping(target = "sender.senderProvince", source = "senderProvince")
    @Mapping(target = "sender.senderCity", source = "senderCity")
    @Mapping(target = "sender.senderDistrict", source = "senderDistrict")
    @Mapping(target = "sender.senderAddress", source = "senderAddress")
    @Mapping(target = "receiver.receiverName", source = "receiverName")
    @Mapping(target = "receiver.receiverMobile", source = "receiverMobile")
    @Mapping(target = "receiver.receiverPhone", source = "receiverPhone")
    @Mapping(target = "receiver.receiverProvince", source = "receiverProvince")
    @Mapping(target = "receiver.receiverCity", source = "receiverCity")
    @Mapping(target = "receiver.receiverDistrict", source = "receiverDistrict")
    @Mapping(target = "receiver.receiverAddress", source = "receiverAddress")
    @Mapping(target = "receiver.receiverCompany", source = "receiverCompany")
    @Mapping(target = "items", source = "items")
    SfOutboundApplyXmlCmd toSfOutboundApplyXmlCmd(OutboundApplyCmd source);

    /**
     * 转换平台出库申请单货物参数
     *
     * @param source 统一仓库货物参数
     * @return 平台出库申请单货物参数
     */
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "quantity", source = "quantity")
    @Mapping(target = "inventoryStatus", source = "inventoryStatus", qualifiedByName = "toInventoryStatus")
    SfOutboundApplyXmlCmd.Item toSfOutboundApplyXmlCmdItem(WarehouseItemCmd source);

    /**
     * 获取交易平台
     *
     * @param platformCode 平台代码
     * @return 交易平台
     */
    @Named("getTradePlatform")
    default String getTradePlatform(String platformCode) {
        return SfOutboundTradePlatform.getTradePlatform(platformCode);
    }

    /**
     * 库存状态转换
     *
     * @param inventoryStatus 库存状态
     * @return 库存状态
     */
    @Named("toInventoryStatus")
    default String toInventoryStatus(InventoryStatus inventoryStatus) {
        return SfInventoryStatus.of(inventoryStatus.name())
                .map(SfInventoryStatus::getCode)
                .orElse(null);
    }

    /**
     * 当前时间
     *
     * @return 当前时间
     */
    @Named("nowDateTime")
    default String nowDateTime() {
        return DateUtil.nowDateTime();
    }

    /**
     * 时间转换为字符串
     *
     * @param time LocalDateTime
     * @return String
     */
    @Named("timeToStr")
    default String timeToStr(LocalDateTime time) {
        if (time == null) {
            return "";
        }
        return DateUtil.toString(time);
    }

}
