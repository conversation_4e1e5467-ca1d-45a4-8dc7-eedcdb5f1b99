package com.xtc.marketing.adapterservice.notify;

import com.xtc.marketing.adapterservice.notify.dto.command.NotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.enums.NotifyEnum;
import com.xtc.marketing.adapterservice.notify.executor.command.NotifyPushCmdExe;
import com.xtc.marketing.adapterservice.notify.executor.command.NotifyReceiveDataCmdExe;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class NotifyServiceImpl implements NotifyService {

    private final NotifyPushCmdExe notifyPushCmdExe;
    private final NotifyReceiveDataCmdExe notifyReceiveDataCmdExe;

    @Override
    public void notifyPush(int shardIndex) {
        notifyPushCmdExe.execute(shardIndex);
    }

    @Override
    public ResponseEntity<String> receiveData(NotifyEnum notify, NotifyReceiveCmd cmd) {
        return notifyReceiveDataCmdExe.execute(notify, cmd);
    }

}
