package com.xtc.marketing.adapterservice.shop.converter;

import com.google.common.collect.Lists;
import com.xtc.marketing.adapterservice.shop.dto.OrderDTO;
import com.xtc.marketing.adapterservice.shop.dto.OrderItemDTO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.util.Collection;
import java.util.stream.Collectors;

@Mapper(
        componentModel = "spring",
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface BaseShopConverter {

    /**
     * 转换统一订单数据时，设置子订单单号
     *
     * @param order 统一订单数据
     */
    @AfterMapping
    default void setOrderParam(@MappingTarget OrderDTO order) {
        if (order == null) {
            return;
        }

        if (order.getItems() != null) {
            // 子订单根据 skuErpCode 分组去重，然后累加 skuNum
            Collection<OrderItemDTO> groupBySkuErpCodeAndSumSkuNum = order.getItems().stream()
                    .collect(Collectors.toMap(OrderItemDTO::getSkuErpCode, item -> item,
                            (item1, item2) -> {
                                item1.setNum(item1.getNum() + item2.getNum());
                                return item1;
                            })
                    )
                    .values();
            order.setItems(Lists.newArrayList(groupBySkuErpCodeAndSumSkuNum));

            order.getItems().forEach(item -> {
                // 子订单设置订单号
                item.setOrderNo(order.getOrderNo());

                // 设置子订单单号：平台子订单id + skuId / productId
                String prefix = StringUtils.defaultIfBlank(item.getItemNo(), order.getOrderNo());
                String skuSuffix = StringUtils.defaultIfBlank(item.getSkuErpCode(), item.getSkuId());
                String suffix = StringUtils.defaultIfBlank(skuSuffix, item.getProductId());
                item.setItemNo(prefix + "-" + suffix);
            });
        }

        if (order.getInvoiceApply() != null) {
            // 发票申请设置订单号
            order.getInvoiceApply().setOrderNo(order.getOrderNo());
        }

        // 收件人城市无数据时，使用收件人区县数据赋值
        if (StringUtils.isBlank(order.getReceiverCity()) && StringUtils.isNotBlank(order.getReceiverDistrict())) {
            order.setReceiverCity(order.getReceiverDistrict());
        }
    }

}
