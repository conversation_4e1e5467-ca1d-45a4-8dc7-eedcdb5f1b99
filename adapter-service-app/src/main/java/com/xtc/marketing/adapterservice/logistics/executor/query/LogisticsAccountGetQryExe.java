package com.xtc.marketing.adapterservice.logistics.executor.query;

import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.logistics.repository.LogisticsAccountRepository;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;

@RequiredArgsConstructor
@Component
public class LogisticsAccountGetQryExe {

    private final LogisticsAccountRepository logisticsAccountRepository;

    /**
     * 查询物流账号
     *
     * @param bizAccount 业务账号
     * @return 物流账号
     */
    public LogisticsAccountDO execute(String bizAccount) {
        LogisticsAccountDO account = logisticsAccountRepository.getByBizAccount(bizAccount)
                .orElseThrow(() -> BizException.of("物流账号不存在"));
        // 校验物流账号
        this.verifyAccount(account);
        return account;
    }

    /**
     * 查询物流账号
     *
     * @param clientCode 客户代码
     * @return 物流账号
     */
    public LogisticsAccountDO getByClientCode(String clientCode) {
        LogisticsAccountDO account = logisticsAccountRepository.getByClientCode(clientCode)
                .orElseThrow(() -> BizException.of("物流账号不存在"));
        // 校验物流账号
        this.verifyAccount(account);
        return account;
    }

    /**
     * 校验物流账号
     *
     * @param account 物流账号
     */
    private void verifyAccount(LogisticsAccountDO account) {
        if (BooleanUtils.isFalse(account.getEnabled())) {
            throw BizException.of("物流账号未开启使用");
        }
        if (Objects.nonNull(account.getExpireTime()) && account.getExpireTime().isBefore(LocalDateTime.now())) {
            throw BizException.of("物流账号已过期");
        }
    }

}
