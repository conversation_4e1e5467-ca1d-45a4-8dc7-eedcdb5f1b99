package com.xtc.marketing.adapterservice.shop.converter;

import com.pdd.pop.sdk.http.api.pop.response.PddFdsOrderGetResponse;
import com.pdd.pop.sdk.http.api.pop.response.PddFdsOrderListGetResponse;
import com.xtc.marketing.adapterservice.rpc.pdd.pdddto.PddFdsWaybillGetRequest;
import com.xtc.marketing.adapterservice.rpc.pdd.pdddto.enums.PddLogisticsCompany;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.OrderDTO;
import com.xtc.marketing.adapterservice.shop.dto.OrderItemDTO;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsCargoCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsOrderCreateCmd;
import com.xtc.marketing.adapterservice.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Mapper(
        componentModel = "spring",
        uses = {BaseShopConverter.class},
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface PddDistrShopConverter {

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "orderMaskSn")
    @Mapping(target = "orderState", source = "status")
    @Mapping(target = "sellerId", source = "mallMaskId")
    @Mapping(target = "sellerName", source = "mallMaskName")
    @Mapping(target = "sellerMemo", source = "remark", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverName", source = "mallMaskId")
    @Mapping(target = "receiverMobile", source = "receiverId")
    @Mapping(target = "receiverProvince", source = "province", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverCity", source = "city", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverDistrict", source = "district", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverAddress", source = "receiverId")
    // 金额和时间
    @Mapping(target = "priceTotal", expression = "java(sumAmount(source.getGoodsNumber(), source.getProductPrice()))")
    @Mapping(target = "payment", expression = "java(sumAmount(source.getGoodsNumber(), source.getProductPrice()))")
    @Mapping(target = "updateTime", source = "allowTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "orderTime", source = "allowTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "payTime", source = "allowTime", qualifiedByName = "toLocalDateTime")
    OrderDTO toAdapterOrderDTO(PddFdsOrderListGetResponse.InnerPddFdsOrderListGetResponseOrderListItem source);

    /**
     * 转换统一订单明细
     *
     * @param source 平台订单明细
     * @return 统一订单明细
     */
    @Mapping(target = "orderNo", source = "orderMaskSn")
    @Mapping(target = "itemNo", expression = "java(source.getOrderMaskSn() + \"-\" + source.getMallMaskId())")
    @Mapping(target = "productId", source = "productSn")
    @Mapping(target = "productName", source = "goodsName")
    @Mapping(target = "skuId", source = "productSn")
    @Mapping(target = "skuName", source = "goodsSpec")
    @Mapping(target = "skuErpCode", source = "outSkuSn")
    @Mapping(target = "num", source = "goodsNumber")
    @Mapping(target = "unitPrice", source = "productPrice")
    @Mapping(target = "priceTotal", expression = "java(sumAmount(source.getGoodsNumber(), source.getProductPrice()))")
    @Mapping(target = "payment", expression = "java(sumAmount(source.getGoodsNumber(), source.getProductPrice()))")
    OrderItemDTO toAdapterOrderItemDTO(PddFdsOrderListGetResponse.InnerPddFdsOrderListGetResponseOrderListItem source);

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "orderMaskSn")
    @Mapping(target = "orderState", source = "status")
    @Mapping(target = "sellerId", source = "mallMaskId")
    @Mapping(target = "sellerName", source = "mallMaskName")
    @Mapping(target = "sellerMemo", source = "remark", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverName", source = "mallMaskId")
    @Mapping(target = "receiverMobile", source = "receiverId")
    @Mapping(target = "receiverProvince", source = "province", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverCity", source = "city", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverDistrict", source = "district", qualifiedByName = "blankToNull")
    @Mapping(target = "receiverAddress", source = "receiverId")
    // 金额和时间
    @Mapping(target = "priceTotal", expression = "java(sumAmount(source.getGoodsNumber(), source.getProductPrice()))")
    @Mapping(target = "payment", expression = "java(sumAmount(source.getGoodsNumber(), source.getProductPrice()))")
    @Mapping(target = "updateTime", source = "allowTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "orderTime", source = "allowTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "payTime", source = "allowTime", qualifiedByName = "toLocalDateTime")
    OrderDTO toAdapterOrderDTO(PddFdsOrderGetResponse.InnerPddFdsOrderGetResponse source);

    /**
     * 转换统一订单明细
     *
     * @param source 平台订单明细
     * @return 统一订单明细
     */
    @Mapping(target = "orderNo", source = "orderMaskSn")
    @Mapping(target = "itemNo", expression = "java(source.getOrderMaskSn() + \"-\" + source.getMallMaskId())")
    @Mapping(target = "productId", source = "productSn")
    @Mapping(target = "productName", source = "goodsName")
    @Mapping(target = "skuName", source = "goodsSpec")
    @Mapping(target = "skuErpCode", source = "outSkuSn")
    @Mapping(target = "num", source = "goodsNumber")
    @Mapping(target = "unitPrice", source = "productPrice")
    @Mapping(target = "priceTotal", expression = "java(sumAmount(source.getGoodsNumber(), source.getProductPrice()))")
    @Mapping(target = "payment", expression = "java(sumAmount(source.getGoodsNumber(), source.getProductPrice()))")
    OrderItemDTO toAdapterOrderItemDTO(PddFdsOrderGetResponse.InnerPddFdsOrderGetResponse source);

    /**
     * 转换平台电子面单参数
     *
     * @param source 统一电子面单参数
     * @param order  平台电子面单，订单参数
     * @return 平台电子面单参数
     */
    @Mapping(target = "paramFdsWaybillGetRequest.wpCode", source = "source.logisticsCompany")
    @Mapping(target = "paramFdsWaybillGetRequest.sender.name", source = "source.senderName")
    @Mapping(target = "paramFdsWaybillGetRequest.sender.mobile", source = "source.senderPhone")
    @Mapping(target = "paramFdsWaybillGetRequest.sender.address.province", source = "source.senderProvince")
    @Mapping(target = "paramFdsWaybillGetRequest.sender.address.city", source = "source.senderCity")
    @Mapping(target = "paramFdsWaybillGetRequest.sender.address.district", source = "source.senderDistrict")
    @Mapping(target = "paramFdsWaybillGetRequest.sender.address.detail", source = "source.senderAddress", qualifiedByName = "blankToNull")
    @Mapping(target = "paramFdsWaybillGetRequest.tradeOrderInfoDtos", source = "order", qualifiedByName = "newSingletonList")
    PddFdsWaybillGetRequest toWaybillGetRequest(ShopLogisticsOrderCreateCmd source, PddFdsWaybillGetRequest.ParamFdsWaybillGetRequestTradeOrderInfoDtosItem order);

    /**
     * 转换平台电子面单参数
     *
     * @param shop             店铺
     * @param source           统一电子面单参数
     * @param logisticsCompany 快递公司
     * @return 平台电子面单参数
     */
    @Mapping(target = "objectId", source = "source.logisticsOrderId", defaultExpression = "java(java.util.UUID.randomUUID().toString().replace(\"-\", \"\"))")
    @Mapping(target = "userId", source = "shop.agentCode")
    @Mapping(target = "orderInfo.orderChannelsType", constant = "PDD")
    @Mapping(target = "orderInfo.tradeOrderList", source = "item", qualifiedByName = "newSingletonList")
    @Mapping(target = "packageInfo.goodsDescription", source = "source.mainCargoName")
    @Mapping(target = "packageInfo.items", source = "source.cargos")
    @Mapping(target = "templateUrl", source = "logisticsCompany.templateUrl")
    @Mapping(target = "logisticsServices", expression = "java(toPddWaybillGetRequestLogisticsServices(source, logisticsCompany))")
    PddFdsWaybillGetRequest.ParamFdsWaybillGetRequestTradeOrderInfoDtosItem toTradeOrderInfoDto(
            ShopLogisticsOrderCreateCmd source,
            ShopDO shop,
            PddLogisticsCompany logisticsCompany,
            PddFdsWaybillGetRequest.ParamFdsWaybillGetRequestTradeOrderInfoDtosItemOrderInfoTradeOrderListItem item);

    /**
     * 转换电子面单代打订单列表
     *
     * @param shop   店铺
     * @param source 面单参数
     * @return 电子面单代打订单列表
     */
    @Mapping(target = "orderMaskSn", source = "source.shopOrderNo")
    @Mapping(target = "mallMaskId", source = "shop.agentCode")
    PddFdsWaybillGetRequest.ParamFdsWaybillGetRequestTradeOrderInfoDtosItemOrderInfoTradeOrderListItem toTradeOrder(
            ShopDO shop,
            ShopLogisticsOrderCreateCmd source);

    /**
     * 转换电子面单代打货物明细
     *
     * @param source 货物明细
     * @return 货物
     */
    @Mapping(target = "count", source = "quantity")
    @Mapping(target = "name", source = "name")
    PddFdsWaybillGetRequest.ParamFdsWaybillGetRequestTradeOrderInfoDtosItemPackageInfoItemsItem toPackageInfoItemsItem(ShopLogisticsCargoCmd source);

    /**
     * 空字符串转换为 null
     *
     * @param str 字符串
     * @return 转换后的字符串
     */
    @Named("blankToNull")
    default String blankToNull(String str) {
        return StringUtils.defaultIfBlank(str, null);
    }

    /**
     * 汇总金额
     *
     * @param goodsNumber  商品数量
     * @param productPrice 商品价格
     * @return 汇总金额
     */
    @Named("sumAmount")
    default int sumAmount(Integer goodsNumber, Integer productPrice) {
        int num = Optional.ofNullable(goodsNumber).orElse(0);
        int price = Optional.ofNullable(productPrice).orElse(0);
        return price * num;
    }

    /**
     * 时间转换
     *
     * @param time 时间
     * @return LocalDateTime
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(Long time) {
        if (time == null || time < 1) {
            return null;
        }
        return DateUtil.toLocalDateTime(time / 1000);
    }

    /**
     * 转换物流服务配置
     *
     * @param source           统一电子面单参数
     * @param logisticsCompany 快递公司
     * @return 物流服务配置
     */
    @Named("toPddWaybillGetRequestLogisticsServices")
    default String toPddWaybillGetRequestLogisticsServices(ShopLogisticsOrderCreateCmd source, PddLogisticsCompany logisticsCompany) {
        if (StringUtils.isAnyBlank(source.getLogisticsType(), logisticsCompany.getLogisticsService())) {
            return null;
        }
        return logisticsCompany.getLogisticsService(source.getLogisticsType());
    }

    /**
     * 生成单例集合
     *
     * @param obj 对象
     * @return 单例集合
     */
    @Named("newSingletonList")
    default <T> List<T> newSingletonList(T obj) {
        return Collections.singletonList(obj);
    }

}
