package com.xtc.marketing.adapterservice.logistics.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.adapterservice.logistics.converter.JtExpressConverter;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsCloudPrintDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsOrderDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCancelOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCloudPrintCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsInterceptCmd;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsOrderGetQry;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsRouteListQry;
import com.xtc.marketing.adapterservice.rpc.jitu.JtExpressRpc;
import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.request.JtExpressCreateOrderRequest;
import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.request.JtExpressInterceptOrderRequest;
import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.response.*;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = LogisticsExtConstant.USE_CASE, scenario = LogisticsExtConstant.SCENARIO_JT)
public class JtExpressExt implements LogisticsExtPt {

    private final JtExpressRpc jtExpressRpc;
    private final JtExpressConverter jtExpressConverter;

    @Override
    public List<LogisticsRouteDTO> routes(LogisticsAccountDO account, LogisticsRouteListQry qry) {
        JtExpressRouteResponse response = jtExpressRpc.routes(account, qry.getWaybillNo());
        return jtExpressConverter.toLogisticsRouteDTO(response.getDetails());
    }

    @Override
    public LogisticsOrderDTO createOrder(LogisticsAccountDO account, LogisticsCreateOrderCmd cmd) {
        JtExpressCreateOrderRequest createOrderDTO = jtExpressConverter.toJtExpressCreateOrderRequest(cmd, account);
        JtExpressCreateOrderResponse response = jtExpressRpc.createOrderWithWaybill(account, createOrderDTO);
        return LogisticsOrderDTO.builder()
                .wayBillNo(response.getBillCode())
                .orderDetail(GsonUtil.objectToJson(response))
                .build();
    }

    @Override
    public String getOrder(LogisticsAccountDO account, LogisticsOrderGetQry qry) {
        JtExpressQueryOrderResponse response = jtExpressRpc.queryOrder(account, qry.getOrderId());
        return GsonUtil.objectToJson(response);
    }

    @Override
    public String getWaybillNo(LogisticsAccountDO account, LogisticsOrderGetQry qry) {
        JtExpressQueryOrderResponse response = jtExpressRpc.queryOrder(account, qry.getOrderId());
        return Optional.ofNullable(response).map(JtExpressQueryOrderResponse::getBillCode).orElse(null);
    }

    @Override
    public boolean cancelOrder(LogisticsAccountDO account, LogisticsCancelOrderCmd cmd) {
        JtExpressCancelOrderResponse response = jtExpressRpc.cancelOrder(account, cmd.getOrderId(), cmd.getCancelReason());
        return response != null;
    }

    @Override
    public boolean intercept(LogisticsAccountDO account, LogisticsInterceptCmd cmd) {
        JtExpressInterceptOrderRequest request = jtExpressConverter.toJtExpressInterceptOrderRequest(cmd, account);
        JtInterceptOrderResponse response = jtExpressRpc.interceptOrder(account, request);
        return response != null;
    }

    @Override
    public LogisticsCloudPrintDTO cloudPrint(LogisticsAccountDO account, LogisticsCloudPrintCmd cmd) {
        String pdfBase64 = jtExpressRpc.printWaybill(account, cmd.getWaybillNo());
        return LogisticsCloudPrintDTO.builder().pdfBase64(pdfBase64).build();
    }

}
