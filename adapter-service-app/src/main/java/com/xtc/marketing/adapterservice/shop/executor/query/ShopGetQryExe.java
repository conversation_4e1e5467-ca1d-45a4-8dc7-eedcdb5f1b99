package com.xtc.marketing.adapterservice.shop.executor.query;

import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.repository.ShopRepository;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Function;

/**
 * 查询店铺
 */
@RequiredArgsConstructor
@Component
public class ShopGetQryExe {

    private final ShopRepository shopRepository;

    /**
     * 查询店铺
     *
     * @param shopCode 店铺代码
     * @return 店铺
     */
    public ShopDO execute(String shopCode) {
        return execute(shopCode, shopRepository::getByShopCode);
    }

    /**
     * 查询店铺
     *
     * @param shopId 店铺id
     * @return 店铺
     */
    public ShopDO getByShopId(String shopId) {
        return execute(shopId, shopRepository::getByShopId);
    }

    /**
     * 查询店铺
     *
     * @param appKey 应用key
     * @return 店铺
     */
    public ShopDO getByAppKey(String appKey) {
        return execute(appKey, shopRepository::getByAppKey);
    }

    /**
     * 查询店铺
     *
     * @param param    查询参数
     * @param function 获取店铺函数
     * @return 店铺
     */
    private ShopDO execute(String param, Function<String, Optional<ShopDO>> function) {
        ShopDO shop = function.apply(param).orElseThrow(() -> BizException.of("店铺不存在"));
        if (BooleanUtils.isFalse(shop.getEnabled())) {
            throw BizException.of("店铺未开启使用");
        }
        return shop;
    }

}
