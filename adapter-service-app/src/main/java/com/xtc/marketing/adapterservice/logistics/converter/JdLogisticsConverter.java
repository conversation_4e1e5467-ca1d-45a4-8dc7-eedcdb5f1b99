package com.xtc.marketing.adapterservice.logistics.converter;

import com.jd.open.api.sdk.domain.etms.TraceQueryJsf.response.get.TraceDTO;
import com.jd.open.api.sdk.request.etms.LdopWaybillReceiveRequest;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCargoCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Mapper(
        componentModel = "spring",
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface JdLogisticsConverter {

    /**
     * 转换平台下单参数
     *
     * @param source 统一下单参数
     * @return 平台下单参数
     */
    @Mapping(target = "orderId", source = "orderId")
    @Mapping(target = "remark", source = "remark")
    @Mapping(target = "customerCode", source = "bizAccount")
    @Mapping(target = "salePlat", constant = "0010001")
    @Mapping(target = "senderName", source = "senderName")
    @Mapping(target = "senderMobile", source = "senderMobile")
    @Mapping(target = "receiveName", source = "receiverName")
    @Mapping(target = "receiveMobile", source = "receiverMobile")
    @Mapping(target = "vloumn", source = "volume")
    @Mapping(target = "weight", source = "weight")
    LdopWaybillReceiveRequest toLdopWaybillReceiveRequest(LogisticsCreateOrderCmd source);

    /**
     * 转换统一路由数据
     *
     * @param source 平台路由数据
     * @return 统一路由数据
     */
    List<LogisticsRouteDTO> toAdapterRouteDTO(List<TraceDTO> source);

    /**
     * 转换统一路由数据
     *
     * @param source 平台路由数据
     * @return 统一路由数据
     */
    @Mapping(target = "detail", source = "opeRemark")
    @Mapping(target = "title", source = "opeTitle")
    @Mapping(target = "time", source = "opeTime", qualifiedByName = "toLocalDateTime")
    LogisticsRouteDTO toAdapterRouteDTO(TraceDTO source);

    /**
     * 转换平台下单参数时，组装寄件地址、收件地址、商品数量
     *
     * @param target 平台下单参数
     * @param source 统一下单参数
     */
    @AfterMapping
    default void setLdopWaybillReceiveRequest(@MappingTarget LdopWaybillReceiveRequest target, LogisticsCreateOrderCmd source) {
        // 寄件地址
        String sendAddress = source.getSenderProvince() + source.getSenderCity() + source.getSenderDistrict()
                + StringUtils.defaultString(source.getSenderTown()) + source.getSenderAddress();
        target.setSenderAddress(sendAddress);

        // 收件地址
        String recipientAddress = source.getReceiverProvince() + source.getReceiverCity() + source.getReceiverDistrict()
                + StringUtils.defaultString(source.getReceiverTown()) + source.getReceiverAddress();
        target.setReceiveAddress(recipientAddress);

        // 统计商品数量
        int goodsCount = source.getCargos().stream()
                .mapToInt(LogisticsCargoCmd::getQuantity).filter(Objects::nonNull).sum();
        target.setPackageCount(goodsCount);
    }

    /**
     * 时间转换
     *
     * @param time 时间字符串
     * @return LocalDateTime
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(String time) {
        if (StringUtils.isBlank(time)) {
            return null;
        }
        return DateUtil.toLocalDateTime(time, "yyyy/MM/dd HH:mm:ss");
    }

}
