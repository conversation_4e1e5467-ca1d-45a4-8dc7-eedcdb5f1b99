package com.xtc.marketing.adapterservice.shop.converter;

import com.google.common.base.Joiner;
import com.jd.open.api.sdk.domain.evaluation.PopCommentJsfService.response.getVenderCommentsForJos.PopCommentJosVo;
import com.jd.open.api.sdk.domain.jinsuanpan.FinInvoiceApplyOrderProvider.response.list.ApplyOrderJosVo;
import com.jd.open.api.sdk.domain.jinsuanpan.FinInvoiceApplyOrderProvider.response.order.ApplyOrderVO;
import com.jd.open.api.sdk.domain.mall.SubsidyOutUploadJsfService.request.insert.SubsidySkuSnInfoParam;
import com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.get.ItemInfo;
import com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.get.OrderSearchInfo;
import com.jd.open.api.sdk.domain.refundapply.RefundApplySoaService.response.queryPageList.RefundApplyVo;
import com.jd.open.api.sdk.domain.shangjiashouhou.ServiceQueryProvider.response.view.ServiceBill;
import com.jd.open.api.sdk.request.etms.LdopWaybillReceiveRequest;
import com.jd.open.api.sdk.request.jinsuanpan.PopInvoiceSelfApplyRequest;
import com.jd.open.api.sdk.request.mall.DigitalSubsidyUploadSnInsertRequest;
import com.jd.open.api.sdk.request.shangjiashouhou.AscReceiveRegisterRequest;
import com.xtc.marketing.adapterservice.rpc.jd.jddto.enums.JdLogisticsCompany;
import com.xtc.marketing.adapterservice.rpc.jd.jddto.request.LdopAlphaWaybillReceiveRequest;
import com.xtc.marketing.adapterservice.shop.constant.ShopOrderConstant;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.enums.BarcodeUploadCategoryEnum;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTitleTypeEnum;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTypeEnum;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.MoneyUtil;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

@Mapper(
        componentModel = "spring",
        uses = {BaseShopConverter.class},
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface JdShopConverter {

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "orderId")
    @Mapping(target = "orderType", source = "orderType")
    @Mapping(target = "orderState", source = "orderState")
    @Mapping(target = "orderStateDesc", source = "orderStateRemark")
    @Mapping(target = "riskState", source = "orderState", qualifiedByName = "toRiskState")
    @Mapping(target = "sellerId", source = "venderId")
    @Mapping(target = "sellerMemo", source = "venderRemark")
    @Mapping(target = "buyerId", source = "realPin")
    @Mapping(target = "buyerName", constant = "京东用户")
    @Mapping(target = "buyerMemo", source = "orderRemark")
    @Mapping(target = "receiverName", source = "consigneeInfo.fullname")
    @Mapping(target = "receiverMobile", source = "consigneeInfo.mobile")
    @Mapping(target = "receiverProvince", source = "consigneeInfo.province")
    @Mapping(target = "receiverCity", source = "consigneeInfo.city")
    @Mapping(target = "receiverDistrict", source = "consigneeInfo.county")
    @Mapping(target = "receiverAddress", source = "consigneeInfo.fullAddress")
    // 金额和时间
    @Mapping(target = "priceTotal", source = "totalSellerReceivable", qualifiedByName = "yuanToCent", defaultExpression = "java(yuanToCent(source.getOrderPayment()))")
    @Mapping(target = "payment", source = "totalSellerReceivable", qualifiedByName = "yuanToCent", defaultExpression = "java(yuanToCent(source.getOrderPayment()))")
    @Mapping(target = "shippingPayment", source = "freightPrice", qualifiedByName = "yuanToCent")
    @Mapping(target = "discount", expression = "java(yuanToCent(source.getTotalSellerReceivable()) - yuanToCent(source.getShouldPay()))")
    @Mapping(target = "updateTime", source = "modified", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "orderTime", source = "orderStartTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "payTime", source = "paymentConfirmTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "latestShippingTime", source = "promisePickDate", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "completedTime", source = "orderEndTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "waybillNo", source = "waybill")
    // 子对象转换
    @Mapping(target = "items", source = "itemInfoList")
    @Mapping(target = "cipherTexts", expression = "java(java.util.Collections.singletonList(source.getConsigneeInfo().getOaid()))")
    OrderDTO toAdapterOrderDTO(com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.search.OrderSearchInfo source);

    /**
     * 转换统一订单明细
     *
     * @param source 平台订单明细
     * @return 统一订单明细
     */
    @Mapping(target = "productId", source = "productNo")
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "skuName", source = "skuName")
    @Mapping(target = "skuErpCode", source = "outerSkuId")
    @Mapping(target = "num", source = "itemTotal")
    @Mapping(target = "unitPrice", source = "jdPrice", qualifiedByName = "yuanToCent")
    OrderItemDTO toAdapterOrderItemDTO(com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.search.ItemInfo source);

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "orderId")
    @Mapping(target = "orderType", source = "orderType")
    @Mapping(target = "orderState", source = "orderState")
    @Mapping(target = "orderStateDesc", source = "orderStateRemark")
    @Mapping(target = "riskState", source = "orderState", qualifiedByName = "toRiskState")
    @Mapping(target = "sellerId", source = "venderId")
    @Mapping(target = "sellerMemo", source = "venderRemark")
    @Mapping(target = "buyerId", source = "realPin")
    @Mapping(target = "buyerName", constant = "京东用户")
    @Mapping(target = "buyerMemo", source = "orderRemark")
    @Mapping(target = "receiverName", source = "consigneeInfo.fullname")
    @Mapping(target = "receiverMobile", source = "consigneeInfo.mobile")
    @Mapping(target = "receiverProvince", source = "consigneeInfo.province")
    @Mapping(target = "receiverCity", source = "consigneeInfo.city")
    @Mapping(target = "receiverDistrict", source = "consigneeInfo.county")
    @Mapping(target = "receiverAddress", source = "consigneeInfo.fullAddress")
    // 金额和时间
    @Mapping(target = "priceTotal", source = "totalSellerReceivable", qualifiedByName = "yuanToCent", defaultExpression = "java(yuanToCent(source.getOrderPayment()))")
    @Mapping(target = "payment", source = "totalSellerReceivable", qualifiedByName = "yuanToCent", defaultExpression = "java(yuanToCent(source.getOrderPayment()))")
    @Mapping(target = "shippingPayment", source = "freightPrice", qualifiedByName = "yuanToCent")
    @Mapping(target = "discount", expression = "java(yuanToCent(source.getTotalSellerReceivable()) - yuanToCent(source.getShouldPay()))")
    @Mapping(target = "updateTime", source = "modified", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "orderTime", source = "orderStartTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "payTime", source = "paymentConfirmTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "latestShippingTime", source = "promisePickDate", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "completedTime", source = "orderEndTime", qualifiedByName = "toLocalDateTime")
    // 子对象转换
    @Mapping(target = "items", source = "itemInfoList")
    @Mapping(target = "cipherTexts", expression = "java(java.util.Collections.singletonList(source.getConsigneeInfo().getOaid()))")
    OrderDTO toAdapterOrderDTO(OrderSearchInfo source);

    /**
     * 转换统一订单明细
     *
     * @param source 平台订单明细
     * @return 统一订单明细
     */
    @Mapping(target = "productId", source = "productNo")
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "skuName", source = "skuName")
    @Mapping(target = "skuErpCode", source = "outerSkuId")
    @Mapping(target = "num", source = "itemTotal")
    @Mapping(target = "unitPrice", source = "jdPrice", qualifiedByName = "yuanToCent")
    OrderItemDTO toAdapterOrderItemDTO(ItemInfo source);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    List<RefundDTO> toAdapterRefundDTO(List<RefundApplyVo> source);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    @Mapping(target = "orderNo", source = "orderId")
    @Mapping(target = "serviceNo", source = "id")
    @Mapping(target = "serviceState", source = "status")
    @Mapping(target = "applyReason", source = "reason")
    @Mapping(target = "sellerId", source = "storeId")
    @Mapping(target = "buyerId", source = "buyerId")
    @Mapping(target = "buyerName", source = "buyerName")
    @Mapping(target = "auditorId", source = "checkUserName")
    @Mapping(target = "auditorName", source = "checkUserName")
    // 金额和时间
    @Mapping(target = "refundApplyAmount", source = "applyRefundSum")
    @Mapping(target = "refundAmount", source = "applyRefundSum")
    @Mapping(target = "createTime", source = "applyTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "updateTime", source = "checkTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "auditTime", source = "checkTime", qualifiedByName = "toLocalDateTime")
    RefundDTO toAdapterRefundDTO(RefundApplyVo source);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    @Mapping(target = "orderNo", source = "orderId")
    @Mapping(target = "serviceNo", source = "id")
    @Mapping(target = "serviceState", source = "status")
    @Mapping(target = "applyReason", source = "reason")
    @Mapping(target = "sellerId", source = "storeId")
    @Mapping(target = "buyerId", source = "buyerId")
    @Mapping(target = "buyerName", source = "buyerName")
    @Mapping(target = "auditorId", source = "checkUserName")
    @Mapping(target = "auditorName", source = "checkUserName")
    // 金额和时间
    @Mapping(target = "refundApplyAmount", source = "applyRefundSum")
    @Mapping(target = "refundAmount", source = "applyRefundSum")
    @Mapping(target = "createTime", source = "applyTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "updateTime", source = "checkTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "auditTime", source = "checkTime", qualifiedByName = "toLocalDateTime")
    RefundDTO toAdapterRefundDTO(com.jd.open.api.sdk.domain.refundapply.RefundApplySoaService.response.queryById.RefundApplyVo source);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    @Mapping(target = "orderNo", source = "orderId")
    @Mapping(target = "serviceNo", source = "serviceId")
    @Mapping(target = "serviceState", source = "serviceStatus")
    @Mapping(target = "applyReason", source = "approveNotes")
    @Mapping(target = "sellerId", source = "approvePin")
    @Mapping(target = "buyerId", source = "approvePin")
    @Mapping(target = "buyerName", source = "approveName")
    @Mapping(target = "returnWaybillNo", source = "expressage.expressCode")
    @Mapping(target = "returnExpressCompany", source = "expressage.expressCompany")
    @Mapping(target = "auditorId", source = "processPin")
    @Mapping(target = "auditorName", source = "processName")
    // 金额和时间
    @Mapping(target = "refundApplyAmount", source = "refundAmt")
    @Mapping(target = "refundAmount", source = "refundAmt")
    @Mapping(target = "createTime", source = "applyTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "auditTime", source = "applyTime", qualifiedByName = "toLocalDateTime")
    RefundDTO toAdapterRefundDTO(ServiceBill source);

    /**
     * 转换平台电子面单参数
     *
     * @param cmd          统一电子面单参数
     * @param customerCode 商家编码，商家入驻时给分配的固定值
     * @return 平台电子面单参数
     */
    // 忽略默认映射，必须明确配置 @Mapping
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "salePlat", constant = "0010001")
    @Mapping(target = "customerCode", source = "customerCode")
    @Mapping(target = "orderId", source = "cmd.logisticsOrderId", defaultExpression = "java(java.util.UUID.randomUUID().toString().replace(\"-\", \"\"))")
    @Mapping(target = "thrOrderId", source = "cmd.shopOrderNo")
    // 寄件人
    @Mapping(target = "senderName", source = "cmd.senderName")
    @Mapping(target = "senderTel", source = "cmd.senderPhone")
    @Mapping(target = "senderAddress", expression = "java(joinStr(cmd.getSenderProvince(), cmd.getSenderCity(), cmd.getSenderDistrict(), cmd.getSenderTown(), cmd.getSenderAddress()))")
    // 收件人
    @Mapping(target = "receiveOAID", source = "cmd.receiverOaid")
    @Mapping(target = "receiveName", source = "cmd.receiverName")
    @Mapping(target = "receiveMobile", source = "cmd.receiverMobile")
    @Mapping(target = "receiveAddress", expression = "java(joinStr(cmd.getReceiverProvince(), cmd.getReceiverCity(), cmd.getReceiverDistrict(), cmd.getReceiverTown(), cmd.getReceiverAddress()))")
    // 包裹信息
    @Mapping(target = "packageCount", constant = "1")
    @Mapping(target = "weight", constant = "1D")
    @Mapping(target = "vloumn", constant = "1D")
    LdopWaybillReceiveRequest toLdopWaybillReceiveRequest(ShopLogisticsOrderCreateCmd cmd, String customerCode);

    /**
     * 转换平台电子面单参数
     *
     * @param cmd     统一电子面单参数
     * @param company 平台物流公司
     * @param shop    店铺
     * @return 平台电子面单参数
     */
    // 忽略默认映射，必须明确配置 @Mapping
    @BeanMapping(ignoreByDefault = true)
    // 订单
    @Mapping(target = "vendorCode", expression = "java(shop.getShopId().split(\"\\\\|\")[0])")
    @Mapping(target = "vendorName", expression = "java(shop.getShopId().split(\"\\\\|\")[1])")
    @Mapping(target = "vendorOrderCode", source = "cmd.logisticsOrderId")
    @Mapping(target = "platformOrderNo", source = "cmd.shopOrderNo")
    @Mapping(target = "providerId", source = "company.providerId")
    @Mapping(target = "providerCode", source = "company.providerCode")
    @Mapping(target = "settlementCode", expression = "java(company.getCustomerCode(shop.getShopCode()))")
    @Mapping(target = "branchCode", expression = "java(company.getCustomerCode(shop.getShopCode()))")
    @Mapping(target = "expressType", source = "cmd.logisticsType")
    // 寄件人
    @Mapping(target = "fromAddress.contact", source = "cmd.senderName")
    @Mapping(target = "fromAddress.phone", source = "cmd.senderPhone")
    @Mapping(target = "fromAddress.mobile", source = "cmd.senderPhone")
    @Mapping(target = "fromAddress.provinceName", source = "cmd.senderProvince")
    @Mapping(target = "fromAddress.cityName", source = "cmd.senderCity")
    @Mapping(target = "fromAddress.countryName", source = "cmd.senderDistrict")
    @Mapping(target = "fromAddress.countrysideName", source = "cmd.senderTown")
    @Mapping(target = "fromAddress.address", source = "cmd.senderAddress")
    // 收件人
    @Mapping(target = "toAddress.oaid", source = "cmd.receiverOaid")
    @Mapping(target = "toAddress.contact", source = "cmd.receiverName")
    @Mapping(target = "toAddress.phone", source = "cmd.receiverMobile")
    @Mapping(target = "toAddress.mobile", source = "cmd.receiverMobile")
    @Mapping(target = "toAddress.provinceName", source = "cmd.receiverProvince")
    @Mapping(target = "toAddress.cityName", source = "cmd.receiverCity")
    @Mapping(target = "toAddress.countryName", source = "cmd.receiverDistrict")
    @Mapping(target = "toAddress.countrysideName", source = "cmd.receiverTown")
    @Mapping(target = "toAddress.address", source = "cmd.receiverAddress")
    // 包裹
    @Mapping(target = "goodsName", source = "cmd.mainCargoName")
    @Mapping(target = "goodsMoney", source = "cmd.cargoPriceTotal", qualifiedByName = "centToYuan")
    LdopAlphaWaybillReceiveRequest toLdopAlphaWaybillReceiveRequest(ShopLogisticsOrderCreateCmd cmd, JdLogisticsCompany company, ShopDO shop);

    /**
     * 转换统一评价数据
     *
     * @param source 平台评价数据
     * @return 统一评价数据
     */
    List<CommentDTO> toAdapterCommentDTO(List<PopCommentJosVo> source);

    /**
     * 转换统一评价数据
     *
     * @param source 平台评价数据
     * @return 统一评价数据
     */
    @Mapping(target = "orderNo", source = "orderId")
    @Mapping(target = "buyerName", source = "nickName")
    @Mapping(target = "skuId", source = "skuid")
    @Mapping(target = "skuName", source = "skuName")
    @Mapping(target = "content", source = "content")
    @Mapping(target = "isGoodRating", expression = "java(source.getScore() == 5)")
    @Mapping(target = "createTime", source = "creationTime", qualifiedByName = "dateToLocalDateTime")
    CommentDTO toAdapterCommentDTO(PopCommentJosVo source);

    /**
     * 转换统一发票申请数据
     *
     * @param source 平台发票申请数据
     * @return 统一发票申请数据
     */
    @Mapping(target = "orderNo", source = "orderId")
    @Mapping(target = "invoiceType", source = "invoiceEasyInfo.invoiceType", qualifiedByName = "toAdapterInvoiceTypeEnum")
    @Mapping(target = "invoiceTitleType", constant = "PERSONAL")
    @Mapping(target = "invoiceTitle", source = "invoiceEasyInfo.invoiceTitle")
    @Mapping(target = "taxNo", source = "invoiceEasyInfo.invoiceCode")
    @Mapping(target = "bankName", source = "vatInfo.depositBank")
    @Mapping(target = "bankAccount", source = "vatInfo.bankAccount")
    @Mapping(target = "companyMobile", source = "invoiceEasyInfo.invoiceConsigneePhone")
    @Mapping(target = "companyAddress", source = "vatInfo.userAddress")
    InvoiceApplyDTO toAdapterInvoiceApplyDTO(com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.search.OrderSearchInfo source);

    /**
     * 转换统一发票申请数据
     *
     * @param source 平台发票申请数据
     * @return 统一发票申请数据
     */
    @Mapping(target = "orderNo", source = "orderId")
    @Mapping(target = "invoiceType", source = "invoiceType", qualifiedByName = "toAdapterInvoiceTypeEnum")
    @Mapping(target = "invoiceTitleType", source = "invoiceTitleType", qualifiedByName = "toAdapterInvoiceTitleTypeEnum")
    @Mapping(target = "invoiceTitle", source = "invoiceTitle")
    @Mapping(target = "taxNo", source = "consumerTaxId")
    @Mapping(target = "bankName", source = "consumerBankName")
    @Mapping(target = "bankAccount", source = "consumerBankAccount")
    @Mapping(target = "companyMobile", source = "consumerPhone")
    @Mapping(target = "companyAddress", source = "consumerAddress")
    @Mapping(target = "invoiceAmount", source = "shouldInvoiceAmount", qualifiedByName = "yuanToCent")
    @Mapping(target = "sellerName", source = "companyName")
    InvoiceApplyDTO toAdapterInvoiceApplyDTO(ApplyOrderVO source);

    /**
     * 转换统一发票申请数据
     *
     * @param source 平台发票申请数据
     * @return 统一发票申请数据
     */
    @Mapping(target = "orderNo", source = "orderId")
    @Mapping(target = "invoiceType", source = "invoiceType", qualifiedByName = "toAdapterInvoiceTypeEnum")
    @Mapping(target = "invoiceTitleType", source = "invoiceTitleType", qualifiedByName = "toAdapterInvoiceTitleTypeEnum")
    @Mapping(target = "invoiceTitle", source = "invoiceTitle")
    @Mapping(target = "taxNo", source = "consumerTaxId")
    @Mapping(target = "bankName", source = "consumerBankName")
    @Mapping(target = "bankAccount", source = "consumerBankAccount")
    @Mapping(target = "companyMobile", source = "consumerPhone")
    @Mapping(target = "companyAddress", source = "consumerAddress")
    @Mapping(target = "invoiceAmount", source = "invoiceAmount", qualifiedByName = "yuanToCent")
    @Mapping(target = "sellerName", source = "companyName")
    InvoiceApplyDTO toAdapterInvoiceApplyDTO(ApplyOrderJosVo source);

    /**
     * 转换统一退货确认入仓参数
     *
     * @param source 参数
     * @return 平台退货确认入仓
     */
    @Mapping(target = "buId", constant = "********")
    @Mapping(target = "operatePin", constant = "xtcgc")
    @Mapping(target = "operateNick", constant = "管理员")
    @Mapping(target = "serviceId", source = "refundId")
    @Mapping(target = "orderId", source = "orderNo")
    @Mapping(target = "receivePin", constant = "xtcgc")
    @Mapping(target = "receiveName", constant = "管理员")
    @Mapping(target = "packingState", constant = "10")
    @Mapping(target = "qualityState", constant = "10")
    @Mapping(target = "invoiceRecord", constant = "10")
    @Mapping(target = "judgmentReason", constant = "10")
    @Mapping(target = "accessoryOrGift", constant = "1")
    @Mapping(target = "appearanceState", constant = "10")
    @Mapping(target = "receiveRemark", constant = "ok")
    AscReceiveRegisterRequest toRefundGoodsToWarehouse(RefundGoodsToWarehouseCmd source);

    /**
     * 转换平台发票上传数据
     *
     * @param cmd 参数
     * @return 发票上传数据
     */
    @Mapping(target = "orderId", source = "orderNo")
    @Mapping(target = "receiverTaxNo", source = "payeeRegisterNo")
    @Mapping(target = "receiverName", source = "payeeName")
    @Mapping(target = "invoiceCode", source = "blueInvoiceCode")
    @Mapping(target = "invoiceNo", source = "blueInvoiceNo")
    @Mapping(target = "ivcTitle", source = "invoiceTitle")
    @Mapping(target = "totalPrice", source = "invoiceAmount", qualifiedByName = "centToYuan")
    @Mapping(target = "invoiceTime", source = "blueTime", qualifiedByName = "localDateTimeToString")
    @Mapping(target = "pdfInfo", source = "invoiceFileBase")
    PopInvoiceSelfApplyRequest toInvoiceApplyRequest(InvoiceUploadCmd cmd);

    /**
     * 转换平台条码上传
     *
     * @param source 参数
     * @return 条码上传
     */
    @Mapping(target = "subsidySkuSnInfoParamList", expression = "java(java.util.Collections.singletonList(toSubsidySkuSnInfoParam(source)))")
    DigitalSubsidyUploadSnInsertRequest toBarcodeUpload(BarcodeUploadCmd source);

    /**
     * 转换平台条码上传明细
     *
     * @param source 参数
     * @return 条码上传明细
     */
    @Mapping(target = "catId3", source = "barcodeUploadCategory", qualifiedByName = "toCatId3")
    @Mapping(target = "imei1", source = "imei")
    @Mapping(target = "sn", source = "barcode")
    @Mapping(target = "upc", source = "barcode69")
    SubsidySkuSnInfoParam toSubsidySkuSnInfoParam(BarcodeUploadCmd source);

    /**
     * 转换条码品类
     *
     * @param barcodeUploadCategory 条码品类
     * @return 条码品类
     */
    @Named("toCatId3")
    default long toCatId3(BarcodeUploadCategoryEnum barcodeUploadCategory) {
        if (barcodeUploadCategory == BarcodeUploadCategoryEnum.PAD) {
            return 2L;
        }
        if (barcodeUploadCategory == BarcodeUploadCategoryEnum.WATCH) {
            return 3L;
        }
        return 0L;
    }

    /**
     * 转换发票抬头类型
     *
     * @param invoiceTitleType 发票抬头类型
     * @return 发票抬头类型枚举
     */
    @Named("toAdapterInvoiceTitleTypeEnum")
    default InvoiceTitleTypeEnum toAdapterInvoiceTitleTypeEnum(Long invoiceTitleType) {
        // 默认值为【个人】，发票抬头类型 4:个人, 5:企业
        if (invoiceTitleType == 5) {
            return InvoiceTitleTypeEnum.COMPANY;
        }
        return InvoiceTitleTypeEnum.PERSONAL;
    }

    /**
     * 转换发票类型
     *
     * @param invoiceType 发票类型
     * @return 发票类型枚举
     */
    @Named("toAdapterInvoiceTypeEnum")
    default InvoiceTypeEnum toAdapterInvoiceTypeEnum(Long invoiceType) {
        // 发票类型 1电子发票, 3普票, 4增票
        if (invoiceType == 1) {
            return InvoiceTypeEnum.ELECTRONIC;
        }
        if (invoiceType == 3) {
            return InvoiceTypeEnum.NORMAL;
        }
        return InvoiceTypeEnum.ELECTRONIC;
    }

    /**
     * 转换统一订单数据时，设置特殊参数
     *
     * @param order         统一订单数据
     * @param platformOrder 平台订单数据
     */
    @AfterMapping
    default void setOrderParam(@MappingTarget OrderDTO order,
                               com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.search.OrderSearchInfo platformOrder) {
        if (order == null) {
            return;
        }
        // 识别京东仓库类型
        if ("京仓订单".equals(platformOrder.getStoreOrder())) {
            order.setWarehouseType(ShopOrderConstant.WAREHOUSE_TYPE_JD_WAREHOUSE);
            order.setWarehouseTypeDesc(ShopOrderConstant.WAREHOUSE_TYPE_DESC_JD_WAREHOUSE);
        }
        // 识别国家补贴订单
        boolean isNationalSubsidy = this.isNationalSubsidy(platformOrder.getOrderExt());
        if (isNationalSubsidy) {
            order.setOrderType(ShopOrderConstant.ORDER_TYPE_NATIONAL_SUBSIDY);
            order.setOrderTypeDesc(ShopOrderConstant.ORDER_TYPE_DESC_NATIONAL_SUBSIDY);
        }
    }

    /**
     * 转换统一订单数据时，设置特殊参数
     *
     * @param order         统一订单数据
     * @param platformOrder 平台订单数据
     */
    @AfterMapping
    default void setOrderParam(@MappingTarget OrderDTO order, OrderSearchInfo platformOrder) {
        if (order == null) {
            return;
        }
        // 识别京东仓库类型
        if ("京仓订单".equals(platformOrder.getStoreOrder())) {
            order.setWarehouseType(ShopOrderConstant.WAREHOUSE_TYPE_JD_WAREHOUSE);
            order.setWarehouseTypeDesc(ShopOrderConstant.WAREHOUSE_TYPE_DESC_JD_WAREHOUSE);
        }
        // 识别国家补贴订单
        boolean isNationalSubsidy = this.isNationalSubsidy(platformOrder.getOrderExt());
        if (isNationalSubsidy) {
            order.setOrderType(ShopOrderConstant.ORDER_TYPE_NATIONAL_SUBSIDY);
            order.setOrderTypeDesc(ShopOrderConstant.ORDER_TYPE_DESC_NATIONAL_SUBSIDY);
        }
    }

    /**
     * 识别国家补贴订单
     *
     * @param orderExt 订单标识
     * @return 识别结果
     */
    @Named("isNationalSubsidy")
    default boolean isNationalSubsidy(String orderExt) {
        if (StringUtils.isBlank(orderExt)) {
            return false;
        }
        String isGovSubsidyOrder = GsonUtil.getAsString(orderExt, "isGovSubsidyOrder");
        return Boolean.parseBoolean(isGovSubsidyOrder);
    }

    /**
     * 转换统一风险状态
     *
     * @param orderState 平台订单状态
     * @return 统一风险状态
     */
    @Named("toRiskState")
    default boolean toRiskState(String orderState) {
        return "LOCKED".equals(orderState) || "PAUSE".equals(orderState);
    }

    /**
     * 拼接字符串
     *
     * @param strArr 字符串集合
     * @return 拼接后的字符串
     */
    @Named("joinStr")
    default String joinStr(String... strArr) {
        return Joiner.on("").skipNulls().join(strArr);
    }

    /**
     * 汇总金额
     *
     * @param amountArr 金额集合
     * @return 总金额
     */
    @Named("sumAmount")
    default int sumAmount(String... amountArr) {
        return Stream.of(amountArr)
                .filter(Objects::nonNull)
                .map(MoneyUtil::yuanToCent)
                .reduce(Integer::sum)
                .orElse(0);
    }

    /**
     * 元转分
     *
     * @param yuan 元
     * @return 分
     */
    @Named("yuanToCent")
    default int yuanToCent(String yuan) {
        if (StringUtils.isBlank(yuan)) {
            return 0;
        }
        return MoneyUtil.yuanToCent(yuan);
    }

    /**
     * 分转元
     *
     * @param yuan 分
     * @return 元
     */
    @Named("centToYuan")
    default String centToYuan(int yuan) {
        return MoneyUtil.centToYuan(yuan);
    }

    /**
     * 时间转换
     *
     * @param time 时间字符串
     * @return LocalDateTime
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(String time) {
        if (StringUtils.isBlank(time)) {
            return null;
        }
        return DateUtil.toLocalDateTime(time);
    }

    /**
     * 时间转换
     *
     * @param time 时间字符串
     * @return LocalDateTime
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(Date time) {
        if (time == null) {
            return null;
        }
        return DateUtil.toLocalDateTime(time);
    }

    /**
     * 时间转换
     *
     * @param time 时间
     * @return LocalDateTime
     */
    @Named("dateToLocalDateTime")
    default LocalDateTime dateToLocalDateTime(Date time) {
        if (time == null) {
            return null;
        }
        return DateUtil.toLocalDateTime(time);
    }

    /**
     * 时间转换
     *
     * @param time 时间
     * @return 时间字符串
     */
    @Named("localDateTimeToString")
    default String localDateTimeToString(LocalDateTime time) {
        if (time == null) {
            return null;
        }
        return DateUtil.toString(time);
    }

}
