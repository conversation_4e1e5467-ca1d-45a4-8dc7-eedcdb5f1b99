package com.xtc.marketing.adapterservice.subscribe;

import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.executor.query.ShopGetQryExe;
import com.xtc.marketing.adapterservice.subscribe.executor.extension.SubscribeExtConstant;
import com.xtc.marketing.adapterservice.subscribe.executor.extension.SubscribeExtPt;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.function.Function;

@Slf4j
@RequiredArgsConstructor
@Service
public class SubscribeServiceImpl implements SubscribeService {

    private final ShopGetQryExe shopGetQryExe;
    private final ExtensionExecutor extensionExecutor;

    @Override
    public String connect(String shopCode, String bizParam) {
        ShopDO shop = shopGetQryExe.execute(shopCode);
        return extensionExecutor(shop, executor -> executor.connect(shop, bizParam));
    }

    @Override
    public String close(String shopCode) {
        ShopDO shop = shopGetQryExe.execute(shopCode);
        return extensionExecutor(shop, executor -> executor.close(shop));
    }

    /**
     * 执行扩展点
     *
     * @param shop     店铺
     * @param function 扩展点方法
     * @param <R>      返回值类型
     * @return 返回值
     */
    private <R> R extensionExecutor(ShopDO shop, Function<SubscribeExtPt, R> function) {
        BizScenario bizScenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID,
                SubscribeExtConstant.USE_CASE, shop.getPlatformCode());
        return extensionExecutor.execute(SubscribeExtPt.class, bizScenario, function);
    }

}
