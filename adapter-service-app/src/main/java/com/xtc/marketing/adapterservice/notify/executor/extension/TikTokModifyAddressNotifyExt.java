package com.xtc.marketing.adapterservice.notify.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.doudian.open.spi.order_shopAddress_getReviewResult.OrderShopAddressGetReviewResultRequest;
import com.doudian.open.spi.order_shopAddress_getReviewResult.param.OrderShopAddressGetReviewResultParam;
import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.notify.converter.TikTokNotifyConverter;
import com.xtc.marketing.adapterservice.notify.dataobject.ReceiveLogDO;
import com.xtc.marketing.adapterservice.notify.dto.command.NotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.enums.NotifyEnum;
import com.xtc.marketing.adapterservice.rpc.oms.OmsRpc;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.command.OmsModifyAddressNotifyCmd;
import com.xtc.marketing.adapterservice.rpc.tiktok.TikTokRpc;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.executor.query.ShopGetQryExe;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;

/**
 * 通知扩展点 - 抖音修改地址
 */
@Slf4j
@RequiredArgsConstructor
@Extension(bizId = NotifyExtConstant.BIZ_ID_TIKTOK,
        useCase = NotifyExtConstant.USE_CASE_SHOP, scenario = NotifyExtConstant.SCENARIO_MODIFY_ADDRESS)
public class TikTokModifyAddressNotifyExt implements NotifyExtPt {

    private final ShopGetQryExe shopGetQryExe;
    private final OmsRpc omsRpc;
    private final TikTokRpc tikTokRpc;
    private final TikTokNotifyConverter tikTokNotifyConverter;

    @Override
    public ReceiveLogDO createReceiveLog(NotifyEnum notify, NotifyReceiveCmd cmd) {
        // 解析通知数据，获取 shopId
        OrderShopAddressGetReviewResultRequest rpcRequest = OrderShopAddressGetReviewResultRequest.wrap(cmd.getRequest());
        OrderShopAddressGetReviewResultParam param = tikTokRpc.paresTiktokModifyAddressParam(rpcRequest);
        ShopDO shop = shopGetQryExe.getByShopId(param.getShopId().toString());
        // 验签
        try {
            tikTokRpc.checkSpiSign(shop, rpcRequest);
        } catch (Exception e) {
            log.warn("解析通知数据异常 {}", e.getMessage(), e);
            // 返回验签失败响应，不需要保存数据所以不设置 dataId
            return ReceiveLogDO.builder().responseStr(this.responseSignCheckFailure()).build();
        }
        // 校验核心数据
        if (StringUtils.isBlank(param.getOrderId())) {
            throw BizException.of("订单不存在");
        }
        // 初始化响应，默认不允许修改地址
        String responseStr = this.responseFailure();
        try {
            // 调用 OMS 系统接口修改地址
            this.omsNotifyModifyAddress(param);
            // 响应成功
            responseStr = this.responseSuccess();
        } catch (Exception e) {
            log.warn("抖音修改地址通知 OMS 失败 {}", e.getMessage(), e);
        }
        return ReceiveLogDO.builder()
                .dataId(param.getOrderId())
                .rawData(GsonUtil.objectToJson(param))
                .responseStr(responseStr)
                .build();
    }

    @Override
    public ResponseEntity<String> notifyResponse(String responseStr) {
        return ResponseEntity.ok().body(responseStr);
    }

    @Override
    public Object convertToNotifyBean(String data) {
        return GsonUtil.jsonToObject(data);
    }

    /**
     * 调用 OMS 系统接口修改地址
     *
     * @param notifyData 通知数据
     */
    private void omsNotifyModifyAddress(OrderShopAddressGetReviewResultParam notifyData) {
        OmsModifyAddressNotifyCmd cmd = tikTokNotifyConverter.toOmsModifyAddressNotifyCmd(notifyData);
        omsRpc.notifyModifyAddress(cmd);
    }

    /**
     * 成功响应
     *
     * @return 成功响应
     */
    private String responseSuccess() {
        return this.buildResponse(0, "success");
    }

    /**
     * 失败响应
     *
     * @return 失败响应
     */
    private String responseFailure() {
        return this.buildResponse(100003, "系统错误");
    }

    /**
     * 验签失败响应
     *
     * @return 验签失败响应
     */
    private String responseSignCheckFailure() {
        return this.buildResponse(100001, "验签失败");
    }

    /**
     * 构建响应
     *
     * @param code    错误码
     * @param message 错误详细描述信息
     * @return 响应
     */
    private String buildResponse(int code, String message) {
        JsonObject result = new JsonObject();
        result.addProperty("message", message);
        result.addProperty("code", code);
        return result.toString();
    }

}
