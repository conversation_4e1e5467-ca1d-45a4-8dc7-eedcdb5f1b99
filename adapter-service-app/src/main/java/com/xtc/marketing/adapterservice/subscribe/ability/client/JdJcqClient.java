package com.xtc.marketing.adapterservice.subscribe.ability.client;

import com.google.common.collect.ImmutableMap;
import com.xtc.marketing.adapterservice.rpc.jd.JcqClient;
import com.xtc.marketing.adapterservice.rpc.jd.jdjcqdto.AfsStepResultJosDTO;
import com.xtc.marketing.adapterservice.rpc.jd.jdjcqdto.JcqMessageDTO;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.subscribe.ability.domainservice.SubscribeLogDomainService;
import com.xtc.marketing.adapterservice.subscribe.ability.domainservice.SubscribePushOmsDomainService;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * 京东消息订阅 JcqClient
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class JdJcqClient {

    private final SubscribePushOmsDomainService subscribePushOmsDomainService;

    /**
     * 消费组Id
     */
    private static final String CONSUMER_GROUP_ID = "open_message_650723933947";
    /**
     * topic名称
     */
    private static final String TOPIC_AFS_STEPRESULT_JOS = "568091687201$Default$open_message_AFS_StepResult_JOS_A44020750E1C3532733F30F5BE74DD23";
    /**
     * JcqClient 客户端缓存
     */
    private static final Map<String, JcqClient> JCQ_CLIENT = new ConcurrentHashMap<>();

    /**
     * 清除 JcqClient 缓存
     *
     * @param shop 店铺
     * @return 被清除的 JcqClient 为 null 说明没有缓存
     */
    public JcqClient removeJcqClient(ShopDO shop) {
        return JCQ_CLIENT.remove(shop.getShopCode());
    }

    /**
     * 获取消息订阅 JcqClient
     *
     * @param shop     店铺
     * @param bizParam 业务参数
     * @return JcqClient
     */
    public JcqClient getJcqClient(ShopDO shop, String bizParam) {
        return JCQ_CLIENT.computeIfAbsent(shop.getShopCode(), k -> newJcqClient(bizParam));
    }

    /**
     * 生成 JcqClient
     *
     * @param bizParam 业务参数
     * @return JcqClient
     */
    private JcqClient newJcqClient(String bizParam) {
        // 平台与店铺的对应关系，业务参数预处理避免多次解析
        List<BizParam> platformList = GsonUtil.jsonToList(bizParam, BizParam.class);
        Map<String, String> platformMap = Optional.ofNullable(platformList)
                .orElse(Collections.emptyList()).stream()
                .collect(ImmutableMap.toImmutableMap(BizParam::getShopId, BizParam::getPlatformId));
        // 生成 JcqClient
        JcqClient jcqClient = new JcqClient(CONSUMER_GROUP_ID, TOPIC_AFS_STEPRESULT_JOS);
        // 设置消息消费者
        jcqClient.setMessageConsumer(pullResult -> {
            SubscribeLogDomainService.logTraceId();
            log.info("JdJcqClient - pullResult: {}", GsonUtil.objectToJson(pullResult));
            if (CollectionUtils.isEmpty(pullResult.getMessages())) {
                return;
            }
            String topic = pullResult.getTopicName();
            pullResult.getMessages().forEach(message -> {
                try {
                    // 消息处理
                    Consumer<SubscribePushOmsDomainService.BizData> dataConsumer = this.getDataConsumer(topic);
                    if (dataConsumer != null) {
                        SubscribePushOmsDomainService.BizData bizData = this.buildBizData(topic, message, platformMap);
                        dataConsumer.accept(bizData);
                    } else {
                        log.warn("JdJcqClient - 未找到消息处理器 topic: {}, dataId: {}", topic, message.getMessageId());
                    }
                } catch (Exception e) {
                    log.warn("JdJcqClient - 消息处理失败 {}", e.getMessage(), e);
                }
            });
        });
        return jcqClient;
    }

    /**
     * 获取数据处理器
     * <p>消息主题与消息处理器映射</p>
     * <pre>
     *     退款单消息：AFS_StepResult_JOS
     * </pre>
     *
     * @param topic 消息主题
     * @return 数据处理器
     */
    private Consumer<SubscribePushOmsDomainService.BizData> getDataConsumer(String topic) {
        if (topic.contains("AFS_StepResult_JOS")) {
            return subscribePushOmsDomainService::refundPushToOms;
        }
        return null;
    }

    /**
     * 构建业务数据
     *
     * @param topic       消息主题
     * @param message     消息
     * @param platformMap 平台与店铺的对应关系
     * @return 业务数据
     */
    private SubscribePushOmsDomainService.BizData buildBizData(String topic, JcqMessageDTO message, Map<String, String> platformMap) {
        SubscribePushOmsDomainService.BizData bizData = SubscribePushOmsDomainService.BizData.builder().build();
        String dataJson = message.getMessageBody();
        // 不同的业务设置不同的数据
        if (topic.contains("AFS_StepResult_JOS")) {
            AfsStepResultJosDTO data = GsonUtil.jsonToBean(dataJson, AfsStepResultJosDTO.class);
            bizData.setPlatformId(platformMap.get(data.getBuId()));
            bizData.setTradeId(data.getOrderId());
            bizData.setRefundId(data.getAfsServiceId());
        }
        return bizData;
    }

    /**
     * 业务参数
     */
    @Getter
    @Setter
    @ToString
    private static class BizParam {

        /**
         * 店铺id
         */
        private String shopId;
        /**
         * OMS平台id
         */
        private String platformId;

    }

}
