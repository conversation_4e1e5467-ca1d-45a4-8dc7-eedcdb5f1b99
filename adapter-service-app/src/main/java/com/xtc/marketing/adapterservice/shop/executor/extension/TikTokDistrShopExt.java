package com.xtc.marketing.adapterservice.shop.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.doudian.open.api.iop_orderInfo.data.IopOrderInfoData;
import com.doudian.open.api.iop_orderList.IopOrderListRequest;
import com.doudian.open.api.iop_orderList.data.IopOrderListData;
import com.doudian.open.api.iop_orderList.data.OrderListItem;
import com.doudian.open.api.iop_orderList.param.IopOrderListParam;
import com.doudian.open.api.iop_waybillGet.data.EbillInfosItem;
import com.doudian.open.api.iop_waybillGet.param.IopWaybillGetParam;
import com.doudian.open.api.iop_waybillReturn.IopWaybillReturnRequest;
import com.doudian.open.api.iop_waybillReturn.param.IopWaybillReturnParam;
import com.xtc.marketing.adapterservice.exception.BizErrorCode;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.tiktok.TikTokRpc;
import com.xtc.marketing.adapterservice.rpc.tiktok.tiktokdto.enums.TikTokLogisticsCompany;
import com.xtc.marketing.adapterservice.shop.converter.TikTokDistrShopConverter;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.domianservice.SetShopCodeUseCacheDomainService;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.dto.query.*;
import com.xtc.marketing.adapterservice.shop.enums.ShopTypeEnum;
import com.xtc.marketing.adapterservice.shop.executor.query.ShopGetQryExe;
import com.xtc.marketing.adapterservice.shop.util.SkuUtil;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = ShopExtConstant.USE_CASE, scenario = ShopExtConstant.SCENARIO_TIKTOK_DISTR)
public class TikTokDistrShopExt implements ShopExtPt {

    private final TikTokRpc tikTokRpc;
    private final TikTokDistrShopConverter tikTokDistrShopConverter;
    private final ShopGetQryExe shopGetQryExe;
    private final SetShopCodeUseCacheDomainService setShopCodeUseCacheDomainService;

    @Override
    public ShopDO getShopToken(ShopDO shop) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<OrderDTO> pageOrders(ShopDO shop, OrderPageQry qry) {
        ShopDO relatedShop = shopGetQryExe.execute(shop.getShopId());

        IopOrderListRequest request = new IopOrderListRequest();
        IopOrderListParam param = request.getParam();
        param.setStartUpdateTime(DateUtil.toEpochSecond(qry.getUpdateTimeStart()));
        param.setEndUpdateTime(DateUtil.toEpochSecond(qry.getUpdateTimeEnd()));
        // 页数从 0 开始
        param.setPage(qry.getPageIndex() - 1);
        param.setSize(qry.getPageSize());

        IopOrderListData iopOrderListData = tikTokRpc.pageOrdersDistr(relatedShop, request);
        if (iopOrderListData == null || CollectionUtils.isEmpty(iopOrderListData.getOrderList())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }

        /*
        1. 代理店铺需要过滤店铺订单，工厂店铺无需过滤
        2. 拆分订单明细里的 skuIds 并且转换成统一订单数据
        3. 为每个订单关联店铺代码
         */
        Long shopId = Long.valueOf(shop.getAgentCode().split("\\|")[0]);
        List<OrderDTO> orders = iopOrderListData.getOrderList().stream()
                .filter(order -> ShopTypeEnum.XTC == shop.getShopType() || shopId.equals(order.getUserId()))
                .map(this::splitSkuIdsAndConvertToAdapterOrderDTO)
                .peek(setShopCodeUseCacheDomainService::setShopCode)
                .collect(Collectors.toList());
        return PageResponse.of(orders, iopOrderListData.getTotal().intValue(), qry.getPageSize(), qry.getPageIndex());
    }

    @Override
    public OrderDTO getOrder(ShopDO shop, OrderGetQry qry) {
        ShopDO relatedShop = this.getRelatedShop(shop);
        String shopId = shop.getAgentCode().split("\\|")[0];
        IopOrderInfoData originOrder = tikTokRpc.getOrderDistr(relatedShop, qry.getOrderNo(), shopId);
        if (originOrder == null) {
            return null;
        }
        OrderDTO orderDTO = this.splitSkuIdsAndConvertToAdapterOrderDTO(originOrder);
        orderDTO.setOriginOrderData(GsonUtil.objectToJson(originOrder));
        return orderDTO;
    }

    @Override
    public boolean orderRemark(ShopDO shop, OrderRemarkCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean orderShipping(ShopDO shop, OrderShippingCmd cmd) {
        ShopDO relatedShop = this.getRelatedShop(shop);
        String shopId = shop.getAgentCode().split("\\|")[0];
        boolean hasShipping = this.checkOrderHasShipping(relatedShop, cmd.getOrderNo(), shopId);
        if (hasShipping) {
            return true;
        }
        IopWaybillReturnRequest request = new IopWaybillReturnRequest();
        IopWaybillReturnParam param = request.getParam();
        param.setUserId(Long.valueOf(shopId));
        param.setDistrOrderId(cmd.getOrderNo());
        // 拒绝退款申请
        param.setIsRefundReject(true);
        String logisticsCompany = TikTokLogisticsCompany.valueOf(cmd.getLogisticsCompany().name()).getCode();
        param.setCompanyCode(logisticsCompany);
        param.setTrackNo(cmd.getWaybillNo());
        List<String> barcodes = cmd.getBarcodes().stream().map(OrderShippingBarcodeCmd::getBarcode).collect(Collectors.toList());
        param.setSerialNumberList(barcodes);
        return tikTokRpc.orderShippingDistr(relatedShop, request);
    }

    @Override
    public boolean orderShippingCancel(ShopDO shop, OrderShippingCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean orderDummyShipping(ShopDO shop, OrderDummyShippingCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public OrderDecryptDTO orderDecrypt(ShopDO shop, OrderDecryptCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<InvoiceApplyDTO> pageInvoiceApply(ShopDO shop, InvoiceApplyPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public InvoiceApplyDTO getInvoiceApply(ShopDO shop, InvoiceApplyGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceFile(ShopDO shop, OrderUploadInvoiceCmd cmd, MultipartFile invoiceFile) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceBase64(ShopDO shop, InvoiceUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public InvoiceAmountDTO getInvoiceAmount(ShopDO shop, String orderNo) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<CommentDTO> pageComments(ShopDO shop, CommentPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<RefundDTO> pageRefunds(ShopDO shop, RefundPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public RefundDTO getRefund(ShopDO shop, RefundGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public ShopLogisticsOrderDTO createLogisticsOrder(ShopDO shop, ShopLogisticsOrderCreateCmd cmd) {
        ShopDO relatedShop = this.getRelatedShop(shop);
        // 电子面单取号
        TikTokLogisticsCompany logisticsCompany = TikTokLogisticsCompany.valueOf(cmd.getLogisticsCompany().name());
        IopWaybillGetParam requestParam = tikTokDistrShopConverter.toIopWaybillGetParam(cmd, shop, logisticsCompany);
        EbillInfosItem orderDistr = tikTokRpc.createLogisticsOrderDistr(relatedShop, requestParam);
        // 打印数据
        String orderDetail = tikTokRpc.printData(relatedShop, orderDistr.getTrackNo(), logisticsCompany);
        return ShopLogisticsOrderDTO.builder()
                .wayBillNo(orderDistr.getTrackNo())
                .orderDetail(orderDetail)
                .build();
    }

    @Override
    public boolean cancelLogisticsOrder(ShopDO shop, ShopLogisticsOrderCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void refundGoodsToWarehouse(ShopDO shop, RefundGoodsToWarehouseCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void barcodeUpload(ShopDO shop, BarcodeUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    /**
     * 拆分订单明细里的 skuIds 并且转换成统一订单数据
     *
     * @param platformOrder 平台订单
     * @return 统一订单数据
     */
    private OrderDTO splitSkuIdsAndConvertToAdapterOrderDTO(OrderListItem platformOrder) {
        // 拆分平台维护的 skuId
        List<OrderListItem> splitSkuItems = SkuUtil.splitSkuIdsAndCloneItem(platformOrder,
                OrderListItem::getOutSkuId, OrderListItem::setOutSkuId);
        List<OrderItemDTO> items = splitSkuItems.stream()
                .map(tikTokDistrShopConverter::toAdapterOrderListItemDTO)
                .collect(Collectors.toList());
        // 转换统一的数据结构
        OrderDTO order = tikTokDistrShopConverter.toAdapterOrderDTO(platformOrder);
        order.setItems(items);
        return order;
    }

    /**
     * 拆分订单明细里的 skuIds 并且转换成统一订单数据
     *
     * @param platformOrder 平台订单
     * @return 统一订单数据
     */
    private OrderDTO splitSkuIdsAndConvertToAdapterOrderDTO(IopOrderInfoData platformOrder) {
        // 拆分平台维护的 skuId
        List<IopOrderInfoData> splitSkuItems = SkuUtil.splitSkuIdsAndCloneItem(platformOrder,
                IopOrderInfoData::getOutSkuId, IopOrderInfoData::setOutSkuId);
        List<OrderItemDTO> items = splitSkuItems.stream()
                .map(tikTokDistrShopConverter::toAdapterOrderInfoItemDTO)
                .collect(Collectors.toList());
        // 转换统一的数据结构
        OrderDTO order = tikTokDistrShopConverter.toAdapterOrderDTO(platformOrder);
        order.setItems(items);
        return order;
    }

    /**
     * 检查订单已发货
     *
     * @param shop        店铺
     * @param orderNo     订单号
     * @param distrShopId 代发店铺id
     * @return 执行结果
     */
    private boolean checkOrderHasShipping(ShopDO shop, String orderNo, String distrShopId) {
        IopOrderInfoData order = tikTokRpc.getOrderDistr(shop, orderNo, distrShopId);
        // 判断订单状态已发货，无需重复发货
        if (order.getDistrStatus() == 2) {
            log.warn("订单已发货，无需重复发货 {} {}", orderNo, order.getDistrStatus());
            return true;
        }
        // 判断订单状态不是待发货，抛异常
        if (order.getDistrStatus() != 1) {
            // 例：平台的订单状态不符合推送发货状态的条件 6923580254658762042 distrStatus: 0
            String msg = String.format("%s %s distrStatus: %s",
                    BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrDesc(), orderNo, order.getDistrStatus());
            throw new BizException(BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrCode(), msg);
        }
        return false;
    }

    /**
     * 查询关联的厂家店铺
     *
     * @param shop 代发店铺
     * @return 厂家店铺
     */
    private ShopDO getRelatedShop(ShopDO shop) {
        if (shop.getShopType() != ShopTypeEnum.AGENT) {
            throw BizException.of("店铺不是代理类型，不支持使用代发店铺的操作");
        }
        // 代发店铺通过 shopId 关联的厂家店铺
        ShopDO relatedShop = shopGetQryExe.execute(shop.getShopId());
        if (StringUtils.isBlank(relatedShop.getAgentCode())) {
            throw BizException.of("代发店铺未维护店铺名称");
        }
        return relatedShop;
    }

}
