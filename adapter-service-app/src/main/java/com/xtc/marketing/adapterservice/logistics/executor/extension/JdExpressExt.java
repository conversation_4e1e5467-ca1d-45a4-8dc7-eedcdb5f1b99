package com.xtc.marketing.adapterservice.logistics.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCreateOrderV1.CommonCreateOrderResponse;
import com.lop.open.api.sdk.domain.ECAP.CommonQueryOrderApi.commonGetOrderStatusV1.CommonOrderStatusResponse;
import com.lop.open.api.sdk.domain.ECAP.CommonQueryOrderApi.commonGetOrderTraceV1.CommonOrderTraceDetail;
import com.lop.open.api.sdk.request.ECAP.*;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.logistics.converter.JdExpressConverter;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsCloudPrintDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsOrderDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCancelOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCloudPrintCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsInterceptCmd;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsOrderGetQry;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsRouteListQry;
import com.xtc.marketing.adapterservice.rpc.jd.JdExpressRpc;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = LogisticsExtConstant.USE_CASE, scenario = LogisticsExtConstant.SCENARIO_JD_EXPRESS)
public class JdExpressExt implements LogisticsExtPt {

    private final JdExpressRpc jdExpressRpc;
    private final JdExpressConverter jdExpressConverter;

    @Override
    public List<LogisticsRouteDTO> routes(LogisticsAccountDO account, LogisticsRouteListQry qry) {
        EcapV1OrdersTraceQueryLopRequest lopRequest = jdExpressConverter.toJdOrdersTraceQueryLopRequest(qry);
        List<CommonOrderTraceDetail> routes = jdExpressRpc.routes(account, lopRequest);
        return jdExpressConverter.toLogisticsRouteDTO(routes);
    }

    @Override
    public LogisticsOrderDTO createOrder(LogisticsAccountDO account, LogisticsCreateOrderCmd cmd) {
        EcapV1OrdersPrecheckLopRequest preRequest = jdExpressConverter.toPreOrdersCreateLopRequest(cmd);
        EcapV1OrdersCreateLopRequest request = jdExpressConverter.toOrdersCreateLopRequest(cmd);
        CommonCreateOrderResponse response = jdExpressRpc.createOrder(account, request, preRequest);
        return LogisticsOrderDTO.builder()
                .wayBillNo(response.getWaybillCode())
                .orderDetail(GsonUtil.objectToJson(response))
                .build();
    }

    @Override
    public String getOrder(LogisticsAccountDO account, LogisticsOrderGetQry qry) {
        EcapV1OrdersStatusGetLopRequest request = jdExpressConverter.toOrdersStatusGetLopRequest(qry);
        CommonOrderStatusResponse order = jdExpressRpc.getOrder(account, request);
        return Optional.ofNullable(order)
                .map(GsonUtil::objectToJson)
                .orElse(null);
    }

    @Override
    public String getWaybillNo(LogisticsAccountDO account, LogisticsOrderGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean cancelOrder(LogisticsAccountDO account, LogisticsCancelOrderCmd cmd) {
        EcapV1OrdersCancelLopRequest lopRequest = jdExpressConverter.toJdOrdersCancelLopRequest(cmd);
        return jdExpressRpc.cancelOrder(account, lopRequest);
    }

    @Override
    public boolean intercept(LogisticsAccountDO account, LogisticsInterceptCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public LogisticsCloudPrintDTO cloudPrint(LogisticsAccountDO account, LogisticsCloudPrintCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

}
