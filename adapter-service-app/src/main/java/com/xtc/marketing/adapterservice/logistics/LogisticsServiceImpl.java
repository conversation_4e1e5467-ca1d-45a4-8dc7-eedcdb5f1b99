package com.xtc.marketing.adapterservice.logistics;

import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsCloudPrintDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsOrderDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCancelOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCloudPrintCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsInterceptCmd;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsOrderGetQry;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsRouteListQry;
import com.xtc.marketing.adapterservice.logistics.executor.extension.LogisticsExtConstant;
import com.xtc.marketing.adapterservice.logistics.executor.extension.LogisticsExtPt;
import com.xtc.marketing.adapterservice.logistics.executor.query.LogisticsAccountGetQryExe;
import com.xtc.marketing.adapterservice.warehouse.enums.WarehouseEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;
import java.util.function.Function;

@RequiredArgsConstructor
@Service("logisticsAccountService")
public class LogisticsServiceImpl implements LogisticsService {

    private final LogisticsAccountGetQryExe logisticsAccountGetQryExe;
    private final ExtensionExecutor extensionExecutor;

    @Override
    public List<LogisticsRouteDTO> routes(LogisticsRouteListQry qry) {
        LogisticsAccountDO account = logisticsAccountGetQryExe.execute(qry.getBizAccount());
        return extensionExecutor(account.getCompanyCode(), exe -> exe.routes(account, qry));
    }

    @Override
    public LogisticsOrderDTO createOrder(LogisticsCreateOrderCmd cmd) {
        // 默认每次都使用新的【业务订单号】向物流公司【下单】生成新的【运单号】
        if (StringUtils.isBlank(cmd.getOrderId())) {
            String uuid = UUID.randomUUID().toString().replace("-", "");
            cmd.setOrderId(uuid);
        }
        LogisticsAccountDO account = logisticsAccountGetQryExe.execute(cmd.getBizAccount());
        return extensionExecutor(account.getCompanyCode(), exe -> exe.createOrder(account, cmd));
    }

    @Override
    public String getOrder(LogisticsOrderGetQry qry) {
        LogisticsAccountDO account = logisticsAccountGetQryExe.execute(qry.getBizAccount());
        return extensionExecutor(account.getCompanyCode(), exe -> exe.getOrder(account, qry));
    }

    @Override
    public String getWaybillNo(LogisticsOrderGetQry cmd) {
        LogisticsAccountDO account = logisticsAccountGetQryExe.execute(cmd.getBizAccount());
        return extensionExecutor(account.getCompanyCode(), exe -> exe.getWaybillNo(account, cmd));
    }

    @Override
    public boolean cancelOrder(LogisticsCancelOrderCmd cmd) {
        LogisticsAccountDO account = logisticsAccountGetQryExe.execute(cmd.getBizAccount());
        return extensionExecutor(account.getCompanyCode(), exe -> exe.cancelOrder(account, cmd));
    }

    @Override
    public void intercept(LogisticsInterceptCmd cmd) {
        // 顺丰仓直接使用参数中的月卡帐号, 不需要再查询数据库
        if (WarehouseEnum.SF.equals(cmd.getWarehouse())) {
            extensionExecutor(LogisticsExtConstant.SCENARIO_SF, exe -> exe.intercept(null, cmd));
        } else {
            LogisticsAccountDO account = logisticsAccountGetQryExe.execute(cmd.getBizAccount());
            extensionExecutor(account.getCompanyCode(), exe -> exe.intercept(account, cmd));
        }
    }

    @Override
    public LogisticsCloudPrintDTO cloudPrint(LogisticsCloudPrintCmd cmd) {
        LogisticsAccountDO account = logisticsAccountGetQryExe.execute(cmd.getBizAccount());
        return extensionExecutor(account.getCompanyCode(), exe -> exe.cloudPrint(account, cmd));
    }

    /**
     * 执行扩展点
     *
     * @param companyCode 公司代码
     * @param function    扩展点方法
     * @param <R>         返回值类型
     * @return 返回值
     */
    private <R> R extensionExecutor(String companyCode, Function<LogisticsExtPt, R> function) {
        BizScenario bizScenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID, LogisticsExtConstant.USE_CASE, companyCode);
        return extensionExecutor.execute(LogisticsExtPt.class, bizScenario, function);
    }

}
