package com.xtc.marketing.adapterservice.shop.converter;

import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.*;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.command.WechatChannelsShopLogisticsCreateCmd;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.command.WechatChannelsShopShippingCmd;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.enums.WechatChannelsShopLogisticsCompany;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.query.WechatChannelsShopOrderPageQry;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.OrderDTO;
import com.xtc.marketing.adapterservice.shop.dto.OrderDecryptDTO;
import com.xtc.marketing.adapterservice.shop.dto.OrderItemDTO;
import com.xtc.marketing.adapterservice.shop.dto.RefundDTO;
import com.xtc.marketing.adapterservice.shop.dto.command.OrderDecryptCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.OrderShippingCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsOrderCreateCmd;
import com.xtc.marketing.adapterservice.shop.dto.query.OrderPageQry;
import com.xtc.marketing.adapterservice.shop.enums.LogisticsCompanyEnum;
import com.xtc.marketing.adapterservice.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper(
        componentModel = "spring",
        uses = {BaseShopConverter.class},
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface WechatChannelsShopConverter {

    /**
     * 转换为统一解密订单
     *
     * @param source 解密订单
     * @param cmd    参数
     * @return 统一解密订单
     */
    @Mapping(target = "orderNo", source = "cmd.orderNo")
    @Mapping(target = "name", source = "source.receiverName")
    @Mapping(target = "mobile", source = "source.receiverMobile")
    @Mapping(target = "address", source = "source.receiverAddress")
    @Mapping(target = "city", source = "source.receiverCity")
    @Mapping(target = "district", source = "source.receiverDistrict")
    @Mapping(target = "province", source = "source.receiverProvince")
    OrderDecryptDTO toOrderDecryptDTO(WechatChannelsShopDecryptDTO source, OrderDecryptCmd cmd);

    /**
     * 转换为平台订单查询参数
     *
     * @param qry 参数
     * @return 订单查询参数
     */
    @Mapping(target = "pageSize", source = "pageSize")
    @Mapping(target = "status", source = "orderState")
    @Mapping(target = "updateTimeRange.startTime", source = "updateTimeStart", qualifiedByName = "localDateTimeToSecond")
    @Mapping(target = "updateTimeRange.endTime", source = "updateTimeEnd", qualifiedByName = "localDateTimeToSecond")
    @Mapping(target = "nextKey", source = "nextKey", conditionExpression = "java(org.apache.commons.lang3.BooleanUtils.isTrue(qry.getUseHasNext()))")
    WechatChannelsShopOrderPageQry toWechatChannelsShopOrderPageQry(OrderPageQry qry);

    /**
     * 转换为统一订单
     *
     * @param order 订单
     * @return 订单
     */
    @Mapping(target = "orderTime", source = "orderTime", qualifiedByName = "secondToLocalDateTime")
    @Mapping(target = "orderState", source = "orderState")
    @Mapping(target = "updateTime", source = "updateTime", qualifiedByName = "secondToLocalDateTime")
    @Mapping(target = "payTime", source = "orderDetail.pay.payTime", qualifiedByName = "secondToLocalDateTime")
    @Mapping(target = "shippingTime", source = "orderDetail.shipping.logistics", qualifiedByName = "longToLocalDateTime")
    @Mapping(target = "priceTotal", source = "orderDetail.price.priceTotal")
    @Mapping(target = "riskState", source = "order.orderDetail.shipping.address", qualifiedByName = "toRiskState")
    @Mapping(target = "shippingPayment", source = "orderDetail.price.shippingPayment")
    @Mapping(target = "discount", constant = "0")
    @Mapping(target = "payment", source = "orderDetail.price.payment")
    @Mapping(target = "sellerId", source = "orderDetail.agent.sellerId")
    @Mapping(target = "sellerName", source = "orderDetail.agent.sellerName")
    @Mapping(target = "buyerId", source = "buyerId")
    @Mapping(target = "buyerMemo", source = "orderDetail.ext.userMemo")
    @Mapping(target = "sellerMemo", source = "orderDetail.ext.sellerMemo")
    @Mapping(target = "receiverName", source = "orderDetail.shipping.address.receiverName")
    @Mapping(target = "receiverMobile", source = "orderDetail.shipping.address.receiverMobile")
    @Mapping(target = "receiverProvince", source = "orderDetail.shipping.address.receiverProvince")
    @Mapping(target = "receiverCity", source = "orderDetail.shipping.address.receiverCity")
    @Mapping(target = "receiverDistrict", source = "orderDetail.shipping.address.receiverDistrict")
    @Mapping(target = "receiverAddress", source = "orderDetail.shipping.address.receiverAddress")
    @Mapping(target = "cipherTexts", expression = "java(java.util.Collections.singletonList(order.getOrderDetail().getShipping().getWaybillOrderCode()))")
    @Mapping(target = "items", source = "orderDetail.products")
    OrderDTO toOrderDTO(WechatChannelsShopOrderItemDTO order);

    /**
     * 转换为统一订单详情
     *
     * @param product 商品
     * @return 订单
     */
    @Mapping(target = "productId", source = "productId")
    @Mapping(target = "productName", source = "productName")
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "skuName", source = "productName")
    @Mapping(target = "num", source = "num")
    @Mapping(target = "unitPrice", source = "unitPrice")
    @Mapping(target = "payment", source = "payment")
    @Mapping(target = "skuErpCode", source = "skuErpCode")
    OrderItemDTO toOrderItemDTO(WechatChannelsShopProductDTO product);

    /**
     * 转换平台订单发货参数
     *
     * @param cmd 统一订单发货参数
     * @return 平台订单发货参数
     */
    @Mapping(target = "orderId", source = "cmd.orderNo")
    @Mapping(target = "deliverList", expression = "java(java.util.Collections.singletonList(toWechatChannelsShopShippingDeliveryCmd(cmd, order)))")
    WechatChannelsShopShippingCmd toWechatChannelsShopShipping(OrderShippingCmd cmd, WechatChannelsShopOrderItemDTO order);

    /**
     * 转换平台订单发货物流参数
     *
     * @param cmd   统一订单发货参数
     * @param order 平台订单
     * @return 平台订单发货物流参数
     */
    @Mapping(target = "deliveryId", source = "cmd.logisticsCompany", qualifiedByName = "toWechatChannelsShopShippingDeliveryId")
    @Mapping(target = "waybillId", source = "cmd.waybillNo")
    @Mapping(target = "products", source = "order.orderDetail.products")
    WechatChannelsShopShippingCmd.DeliveryInfoCmd toWechatChannelsShopShippingDeliveryCmd(OrderShippingCmd cmd,
                                                                                          WechatChannelsShopOrderItemDTO order);

    /**
     * 转换平台订单发货商品参数
     *
     * @param product 平台商品
     * @return 平台订单发货商品参数
     */
    @Mapping(target = "productId", source = "productId")
    @Mapping(target = "productCnt", source = "num")
    @Mapping(target = "skuId", source = "skuId")
    WechatChannelsShopShippingCmd.DeliveryProductInfoCmd toWechatChannelsShopShippingProductCmd(WechatChannelsShopProductDTO product);

    /**
     * 转换为平台面单取号
     *
     * @param cmd  参数
     * @param shop 店铺
     * @return 视频号面单
     */
    @Mapping(target = "deliveryId", source = "cmd.logisticsCompany")
    @Mapping(target = "sender.name", source = "cmd.senderName")
    @Mapping(target = "sender.mobile", source = "cmd.senderPhone")
    @Mapping(target = "sender.province", source = "cmd.senderProvince")
    @Mapping(target = "sender.city", source = "cmd.senderCity")
    @Mapping(target = "sender.county", source = "cmd.senderDistrict")
    @Mapping(target = "sender.street", source = "cmd.senderTown")
    @Mapping(target = "sender.address", source = "cmd.senderAddress")
    @Mapping(target = "receiver.name", source = "cmd.receiverName")
    @Mapping(target = "receiver.mobile", source = "cmd.receiverMobile")
    @Mapping(target = "receiver.province", source = "cmd.receiverProvince")
    @Mapping(target = "receiver.city", source = "cmd.receiverCity")
    @Mapping(target = "receiver.county", source = "cmd.receiverDistrict")
    @Mapping(target = "receiver.street", source = "cmd.receiverTown")
    @Mapping(target = "receiver.address", source = "cmd.receiverAddress")
    @Mapping(target = "logisticsType", source = "cmd.logisticsType")
    WechatChannelsShopLogisticsCreateCmd toWechatChannelsShopLogisticsCreateCmd(ShopLogisticsOrderCreateCmd cmd, ShopDO shop);

    /**
     * 转换为统一退款单
     *
     * @param refund 退款单
     * @return 统一退款单
     */
    @Mapping(target = "serviceNo", source = "serviceNo")
    @Mapping(target = "orderNo", source = "orderNo")
    @Mapping(target = "serviceState", source = "serviceState")
    @Mapping(target = "serviceType", source = "serviceType")
    @Mapping(target = "applyReason", source = "applyReason")
    @Mapping(target = "refundAmount", source = "refundInfo.refundAmount")
    @Mapping(target = "buyerId", source = "buyerId")
    @Mapping(target = "originData", source = "originData")
    @Mapping(target = "createTime", source = "createTime", qualifiedByName = "secondToLocalDateTime")
    @Mapping(target = "updateTime", source = "updateTime", qualifiedByName = "secondToLocalDateTime")
    @Mapping(target = "productId", source = "product.productId")
    @Mapping(target = "skuId", source = "product.skuId")
    @Mapping(target = "num", source = "product.num")
    @Mapping(target = "returnWaybillNo", source = "returnInfo.returnWaybillNo")
    @Mapping(target = "returnExpressCompany", source = "returnInfo.returnExpressCompany")
    RefundDTO toRefundDTO(WechatChannelsShopRefundDTO refund);

    /**
     * 转换为平台面单取号，设置平台参数
     *
     * @param shippingCmd 视频号取号参数
     * @param cmd         统一取号参数
     * @param shop        店铺
     */
    @AfterMapping
    default void setLogisticsCreateCmd(@MappingTarget WechatChannelsShopLogisticsCreateCmd shippingCmd, ShopLogisticsOrderCreateCmd cmd, ShopDO shop) {
        List<WechatChannelsShopLogisticsCreateCmd.EcOrderInfo> orderInfoList = new ArrayList<>();
        WechatChannelsShopLogisticsCreateCmd.EcOrderInfo order = new WechatChannelsShopLogisticsCreateCmd.EcOrderInfo();
        order.setOrderNo(Long.parseLong(cmd.getShopOrderNo()));
        // 填充商品
        List<WechatChannelsShopLogisticsCreateCmd.GoodsInfo> goodsList = new ArrayList<>();
        cmd.getCargos().forEach(cargo -> {
            WechatChannelsShopLogisticsCreateCmd.GoodsInfo goodsInfo = new WechatChannelsShopLogisticsCreateCmd.GoodsInfo();
            goodsInfo.setGoodName(cargo.getName());
            goodsInfo.setGoodCount(cargo.getQuantity());
            goodsList.add(goodsInfo);
        });
        order.setGoods(goodsList);
        orderInfoList.add(order);
        shippingCmd.setOrders(orderInfoList);
        shippingCmd.setShopId(shop.getShopId());
        // 默认简单模板
        shippingCmd.setTemplateId("single");
        // 填充客户编码
        String logisticsCompany = cmd.getLogisticsCompany().name();
        WechatChannelsShopLogisticsCompany shopLogisticsCompany = WechatChannelsShopLogisticsCompany.valueOf(logisticsCompany);
        Map<String, String> accountIdMap = shopLogisticsCompany.getAccountId();
        String accountId = accountIdMap.get(cmd.getShopCode());
        shippingCmd.setWaybillAcctId(accountId);
    }

    /**
     * 转换平台物流公司id
     *
     * @param logisticsCompany 物流公司
     * @return 物流公司id
     */
    @Named("toWechatChannelsShopShippingDeliveryId")
    default String toWechatChannelsShopShippingDeliveryId(LogisticsCompanyEnum logisticsCompany) {
        return WechatChannelsShopLogisticsCompany.valueOf(logisticsCompany.name()).getDeliveryId();
    }

    /**
     * 转换统一风险状态
     *
     * @param address 地址
     * @return 统一风险状态
     */
    @Named("toRiskState")
    default boolean toRiskState(WechatChannelsShopAddressDTO address) {
        if (address == null) {
            return true;
        }
        return StringUtils.isAnyBlank(address.getReceiverAddress(), address.getReceiverMobile());
    }

    /**
     * 时间转换
     *
     * @param time 时间
     * @return LocalDateTime
     */
    @Named("dateToLocalDateTime")
    default LocalDateTime dateToLocalDateTime(Date time) {
        return DateUtil.toLocalDateTime(time);
    }

    /**
     * 时间转换
     *
     * @param logistics 发货列表
     * @return LocalDateTime
     */
    @Named("longToLocalDateTime")
    default LocalDateTime longToLocalDateTime(List<WechatChannelsShopOrderItemDetailDTO.LogisticsDTO> logistics) {
        if (logistics.isEmpty()) {
            return null;
        }
        Long shippingTime = logistics.get(0).getShippingTime();
        return DateUtil.toLocalDateTime(shippingTime);
    }

    /**
     * 时间转换
     *
     * @param time 时间
     * @return 时间戳
     */
    @Named("localDateTimeToSecond")
    default long localDateTimeToSecond(LocalDateTime time) {
        return DateUtil.toEpochSecond(time);
    }

    /**
     * 时间转换
     *
     * @param time 时间
     * @return LocalDateTime
     */
    @Named("secondToLocalDateTime")
    default LocalDateTime secondToLocalDateTime(long time) {
        return DateUtil.toLocalDateTime(time);
    }

}
