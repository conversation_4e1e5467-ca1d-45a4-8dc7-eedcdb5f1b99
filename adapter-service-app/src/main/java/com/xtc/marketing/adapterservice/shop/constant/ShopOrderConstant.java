package com.xtc.marketing.adapterservice.shop.constant;

/**
 * 店铺订单常量
 */
public class ShopOrderConstant {

    private ShopOrderConstant() {
    }

    /* 订单类型 */
    /**
     * 订单类型：全渠道
     */
    public static final String OMNICHANNEL_TYPE = "OMNICHANNEL";
    /**
     * 订单状态：全渠道已发货
     */
    public static final String OMNICHANNEL_FINISHED_STATE = "OMNICHANNEL_FINISHED";
    /**
     * 订单类型：国家补贴
     */
    public static final String ORDER_TYPE_NATIONAL_SUBSIDY = "NATIONAL_SUBSIDY";
    /**
     * 订单类型描述：国家补贴
     */
    public static final String ORDER_TYPE_DESC_NATIONAL_SUBSIDY = "国家补贴";

    /* 仓库类型 */
    /**
     * 仓库类型：京东仓库订单
     */
    public static final String WAREHOUSE_TYPE_JD_WAREHOUSE = "JD_WAREHOUSE";
    /**
     * 仓库类型描述：京东仓库订单
     */
    public static final String WAREHOUSE_TYPE_DESC_JD_WAREHOUSE = "京东仓库";

}
