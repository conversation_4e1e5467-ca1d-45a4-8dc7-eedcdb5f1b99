package com.xtc.marketing.adapterservice.invoice;

import com.xtc.marketing.adapterservice.invoice.dto.InvoiceResultDTO;
import com.xtc.marketing.adapterservice.invoice.dto.command.InvoiceCreateCmd;
import com.xtc.marketing.adapterservice.invoice.dto.command.InvoiceCreateRedCmd;
import com.xtc.marketing.adapterservice.invoice.dto.command.InvoiceSerialNoCmd;
import com.xtc.marketing.adapterservice.invoice.dto.query.InvoiceResultQry;

import java.util.List;

public interface InvoiceService {

    /**
     * 开票
     *
     * @param cmd 参数
     * @return 执行结果
     */
    boolean createInvoice(InvoiceCreateCmd cmd);

    /**
     * 冲红
     *
     * @param cmd 参数
     * @return 执行结果
     */
    boolean createRedInvoice(InvoiceCreateRedCmd cmd);

    /**
     * 查询开票结果
     *
     * @param qry 参数
     * @return 开票结果
     */
    List<InvoiceResultDTO> listInvoiceResult(InvoiceResultQry qry);

    /**
     * 生成开票流水号
     *
     * @param cmd 参数
     * @return 开票流水号
     */
    String createSerialNo(InvoiceSerialNoCmd cmd);

}
