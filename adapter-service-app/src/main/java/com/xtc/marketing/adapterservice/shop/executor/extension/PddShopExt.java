package com.xtc.marketing.adapterservice.shop.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.google.common.collect.Lists;
import com.google.gson.JsonObject;
import com.pdd.pop.sdk.http.api.pop.request.PddInvoiceApplicationQueryRequest;
import com.pdd.pop.sdk.http.api.pop.request.PddLogisticsOnlineSendRequest;
import com.pdd.pop.sdk.http.api.pop.request.PddOrderNumberListIncrementGetRequest;
import com.pdd.pop.sdk.http.api.pop.request.PddRefundListIncrementGetRequest;
import com.pdd.pop.sdk.http.api.pop.response.*;
import com.xtc.marketing.adapterservice.exception.BizErrorCode;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.invoice.enums.InvoiceCreateType;
import com.xtc.marketing.adapterservice.rpc.pdd.PddRpc;
import com.xtc.marketing.adapterservice.rpc.pdd.pdddto.PddInvoiceDetailUploadRequest;
import com.xtc.marketing.adapterservice.rpc.pdd.pdddto.PddWaybillGetRequest;
import com.xtc.marketing.adapterservice.rpc.pdd.pdddto.enums.PddLogisticsCompany;
import com.xtc.marketing.adapterservice.shop.converter.PddShopConverter;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.dto.query.*;
import com.xtc.marketing.adapterservice.shop.util.SkuUtil;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.MoneyUtil;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = ShopExtConstant.USE_CASE, scenario = ShopExtConstant.SCENARIO_PDD)
public class PddShopExt implements ShopExtPt {

    private final PddRpc pddRpc;
    private final PddShopConverter pddShopConverter;

    @Override
    public ShopDO getShopToken(ShopDO shop) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<OrderDTO> pageOrders(ShopDO shop, OrderPageQry qry) {
        PddOrderNumberListIncrementGetRequest request = new PddOrderNumberListIncrementGetRequest();
        request.setOrderStatus(5);
        request.setRefundStatus(5);
        request.setIsLuckyFlag(0);
        request.setStartUpdatedAt(DateUtil.toEpochSecond(qry.getUpdateTimeStart()));
        request.setEndUpdatedAt(DateUtil.toEpochSecond(qry.getUpdateTimeEnd()));
        request.setPage(qry.getPageIndex());
        request.setPageSize(qry.getPageSize());
        request.setUseHasNext(BooleanUtils.isTrue(qry.getUseHasNext()));

        // 调用接口，校验结果异常返回默认数据
        PddOrderNumberListIncrementGetResponse originResponse = pddRpc.pageOrders(shop, request);
        PddOrderNumberListIncrementGetResponse.OrderSnIncrementGetResponse response = Optional.ofNullable(originResponse)
                .map(PddOrderNumberListIncrementGetResponse::getOrderSnIncrementGetResponse)
                .filter(orderSnIncrementGetResponse -> CollectionUtils.isNotEmpty(orderSnIncrementGetResponse.getOrderSnList()))
                .orElse(null);
        if (response == null) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }

        // 转换数据，返回分页数据结构
        List<OrderDTO> listOrderDTO = response.getOrderSnList().stream()
                .map(pddShopConverter::toAdapterOrderDTO)
                .map(this::splitSkuIds)
                .collect(Collectors.toList());
        PageResponse<OrderDTO> pageResponse = PageResponse.of(listOrderDTO, -1, qry.getPageSize(), qry.getPageIndex());
        Optional.ofNullable(response.getTotalCount()).ifPresent(pageResponse::setTotalCount);
        Optional.ofNullable(response.getHasNext()).ifPresent(pageResponse::setHasNext);
        return pageResponse;
    }

    @Override
    public OrderDTO getOrder(ShopDO shop, OrderGetQry qry) {
        PddOrderInformationGetResponse.OrderInfoGetResponseOrderInfo order = pddRpc.getOrder(shop, qry.getOrderNo());
        return Optional.ofNullable(order)
                .map(originOrder -> {
                    OrderDTO orderDTO = pddShopConverter.toAdapterOrderDTO(originOrder);
                    orderDTO.setOriginOrderData(GsonUtil.objectToJson(originOrder));
                    return orderDTO;
                })
                .map(this::splitSkuIds)
                .orElse(null);
    }

    @Override
    public boolean orderRemark(ShopDO shop, OrderRemarkCmd cmd) {
        return pddRpc.orderRemark(shop, cmd.getOrderNo(), cmd.getRemark());
    }

    @Override
    public boolean orderShipping(ShopDO shop, OrderShippingCmd cmd) {
        PddOrderInformationGetResponse.OrderInfoGetResponseOrderInfo order = pddRpc.getOrder(shop, cmd.getOrderNo());
        boolean hasShipping = this.checkOrderHasShipping(order);
        if (hasShipping) {
            return true;
        }
        PddLogisticsOnlineSendRequest request = new PddLogisticsOnlineSendRequest();
        request.setOrderSn(cmd.getOrderNo());
        // 设置物流信息
        Long logisticsId = PddLogisticsCompany.valueOf(cmd.getLogisticsCompany().name()).getCode();
        request.setLogisticsId(logisticsId);
        request.setTrackingNumber(cmd.getWaybillNo());
        // 设置国补字段数据
        boolean isNationalSubsidy = order.getOrderTagList().stream()
                .filter(tag -> "trade_in_national_subsidy".equals(tag.getName()))
                .map(PddOrderInformationGetResponse.OrderInfoGetResponseOrderInfoOrderTagListItem::getValue)
                .anyMatch(value -> value == 1);
        if (isNationalSubsidy) {
            String feature = cmd.getBarcodes().stream()
                    .map(barcode -> {
                        StringBuilder stringBuilder = new StringBuilder();
                        if (StringUtils.isNotBlank(barcode.getImei())) {
                            stringBuilder.append("imei=").append(barcode.getImei()).append(";");
                        }
                        return stringBuilder.append("deviceSn=").append(barcode.getBarcode()).append(";");
                    })
                    .collect(Collectors.joining());
            request.setFeature(feature);
            log.info("设置国补字段数据 feature=\"{}\"", feature);
        }
        return pddRpc.orderShipping(shop, request);
    }

    @Override
    public boolean orderShippingCancel(ShopDO shop, OrderShippingCancelCmd cmd) {
        return pddRpc.orderShippingCancel(shop, cmd.getOrderNo(), cmd.getRefundId());
    }

    @Override
    public boolean orderDummyShipping(ShopDO shop, OrderDummyShippingCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public OrderDecryptDTO orderDecrypt(ShopDO shop, OrderDecryptCmd cmd) {
        List<String> ciphers = cmd.getCiphers();
        if (cmd.getCiphers().size() == 1) {
            // 转换密文数据格式，支持 json 对象格式
            JsonObject jsonObject = GsonUtil.jsonToObject(cmd.getCiphers().get(0));
            String receiverName = GsonUtil.getAsString(jsonObject, "receiverName");
            String receiverMobile = GsonUtil.getAsString(jsonObject, "receiverMobile");
            String receiverAddress = GsonUtil.getAsString(jsonObject, "receiverAddress");
            ciphers = Lists.newArrayList(receiverName, receiverMobile, receiverAddress);
        }

        PddOpenDecryptBatchResponse response = pddRpc.orderDecrypt(shop, cmd.getOrderNo(), ciphers);
        return pddShopConverter.toAdapterOrderDecryptDTO(response.getOpenDecryptBatchResponse());
    }

    @Override
    public PageResponse<InvoiceApplyDTO> pageInvoiceApply(ShopDO shop, InvoiceApplyPageQry qry) {
        PddInvoiceApplicationQueryRequest request = new PddInvoiceApplicationQueryRequest();
        request.setPage(qry.getPageIndex());
        request.setPageSize(qry.getPageSize());
        request.setUpdateStartTime(DateUtil.toEpochMilli(qry.getStartTime()));
        request.setUpdateEndTime(DateUtil.toEpochMilli(qry.getEndTime()));
        List<PddInvoiceApplicationQueryResponse.InvoiceApplicationQueryResponseInvoiceApplicationListItem> listItems = pddRpc.pageInvoiceApply(shop, request);
        List<InvoiceApplyDTO> applyDTO = pddShopConverter.toAdapterInvoiceApplyDTO(listItems);
        return PageResponse.of(applyDTO, applyDTO.size(), qry.getPageIndex(), qry.getPageSize());
    }

    @Override
    public InvoiceApplyDTO getInvoiceApply(ShopDO shop, InvoiceApplyGetQry qry) {
        PddInvoiceApplicationQueryResponse.InvoiceApplicationQueryResponseInvoiceApplicationListItem invoiceApply =
                pddRpc.getInvoiceApply(shop, qry.getOrderNo());
        InvoiceApplyDTO invoiceApplyDTO = pddShopConverter.toAdapterInvoiceApplyDTO(invoiceApply);
        // 设置发票主体
        this.getInvoiceEntityId(shop, invoiceApplyDTO).ifPresent(invoiceApplyDTO::setInvoiceEntityId);
        return invoiceApplyDTO;
    }

    @Override
    public boolean uploadInvoiceFile(ShopDO shop, OrderUploadInvoiceCmd cmd, MultipartFile invoiceFile) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceBase64(ShopDO shop, InvoiceUploadCmd cmd) {
        if (InvoiceCreateType.RED == cmd.getCreateType()) {
            throw BizException.of("拼多多发票上传只支持蓝票上传");
        }
        // 只保留金额最大
        List<InvoiceItemCmd> invoiceItems = cmd.getInvoiceItems().stream()
                .max(Comparator.comparingInt(item -> MoneyUtil.yuanToCent(item.getPrice())))
                .map(Collections::singletonList)
                .orElse(Collections.emptyList());
        cmd.setInvoiceItems(invoiceItems);
        // 参数转换
        PddInvoiceDetailUploadRequest request = pddShopConverter.toPddInvoiceDetailUploadRequest(cmd);
        request.getInvoiceItemList().forEach(item -> {
            item.setInvoiceFileContent(cmd.getInvoiceFileBase());
            item.setInvoiceAmount(Long.valueOf(cmd.getInvoiceAmount()));
        });
        return pddRpc.invoiceUpload(shop, request);
    }

    @Override
    public InvoiceAmountDTO getInvoiceAmount(ShopDO shop, String orderNo) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<CommentDTO> pageComments(ShopDO shop, CommentPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<RefundDTO> pageRefunds(ShopDO shop, RefundPageQry qry) {
        PddRefundListIncrementGetRequest request = new PddRefundListIncrementGetRequest();
        request.setAfterSalesStatus(10);
        request.setAfterSalesType(1);
        request.setPage(qry.getPageIndex());
        request.setPageSize(qry.getPageSize());
        request.setStartUpdatedAt(DateUtil.toEpochSecond(qry.getUpdateTimeStart()));
        request.setEndUpdatedAt(DateUtil.toEpochSecond(qry.getUpdateTimeEnd()));
        request.setOrderSn(qry.getOrderNo());

        PddRefundListIncrementGetResponse.RefundIncrementGetResponse response = pddRpc.pageRefunds(shop, request);
        if (CollectionUtils.isEmpty(response.getRefundList())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }

        List<RefundDTO> listRefundDTO = pddShopConverter.toAdapterRefundDTO(response.getRefundList());
        return PageResponse.of(listRefundDTO, response.getTotalCount(), qry.getPageSize(), qry.getPageIndex());
    }

    @Override
    public RefundDTO getRefund(ShopDO shop, RefundGetQry qry) {
        PddRefundInformationGetResponse refund = pddRpc.getRefund(shop, qry.getOrderNo(), Long.parseLong(qry.getRefundId()));
        return pddShopConverter.toAdapterRefundDTO(refund);
    }

    @Override
    public ShopLogisticsOrderDTO createLogisticsOrder(ShopDO shop, ShopLogisticsOrderCreateCmd cmd) {
        PddLogisticsCompany logisticsCompany = PddLogisticsCompany.valueOf(cmd.getLogisticsCompany().name());
        PddWaybillGetRequest.ParamWaybillCloudPrintApplyNewRequestTradeOrderInfoDtosItem requestOrder =
                pddShopConverter.toPddWaybillGetRequestOrder(cmd, shop, logisticsCompany);
        PddWaybillGetRequest request = pddShopConverter.toPddWaybillGetRequest(cmd, requestOrder);
        try {
            PddWaybillGetResponse.InnerPddWaybillGetResponseModulesItem logisticsOrder = pddRpc.createLogisticsOrder(shop, request);
            return ShopLogisticsOrderDTO.builder()
                    .wayBillNo(logisticsOrder.getWaybillCode())
                    .orderDetail(logisticsOrder.getPrintData())
                    .build();
        } catch (Exception e) {
            // 根据异常信息定义业务异常，调用方根据 errCode 做特定的业务处理
            if (e.getMessage().contains("停发")) {
                throw BizException.of(BizErrorCode.B_ORDER_LogisticsOrderPackageNotReachable.getErrCode(), e.getMessage(), e);
            }
            throw e;
        }
    }

    @Override
    public boolean cancelLogisticsOrder(ShopDO shop, ShopLogisticsOrderCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void refundGoodsToWarehouse(ShopDO shop, RefundGoodsToWarehouseCmd cmd) {
        // 退货确认入仓
        pddRpc.refundGoodsToWarehouse(shop, cmd.getOrderNo(), cmd.getRefundId(), cmd.getWaybillNo());
        // 同意退款
        pddRpc.autoRefund(shop, cmd.getOrderNo(), cmd.getRefundId());
    }

    @Override
    public void barcodeUpload(ShopDO shop, BarcodeUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    /**
     * 根据订单明细里的 skuIds 拆分明细
     *
     * @param order 统一订单数据
     * @return 统一订单数据
     */
    private OrderDTO splitSkuIds(OrderDTO order) {
        // 拆分平台维护的 skuId 并且重新设置 itemNo
        List<OrderItemDTO> splitSkuItems = SkuUtil.splitSkuIdsAndCloneItem(
                order.getItems(),
                OrderItemDTO::getSkuErpCode,
                (item, skuId) -> {
                    item.setSkuErpCode(skuId);
                    item.setItemNo(order.getOrderNo() + "-" + skuId);
                }
        );
        // 所有明细的 sku 物料代码未维护，无需处理拆分逻辑直接返回
        if (CollectionUtils.isEmpty(splitSkuItems)) {
            return order;
        }
        // 子订单根据 skuErpCode 分组去重，然后累加 skuNum
        Collection<OrderItemDTO> groupBySkuErpCodeAndSumSkuNum = splitSkuItems.stream()
                .collect(Collectors.toMap(OrderItemDTO::getSkuErpCode, item -> item,
                        (item1, item2) -> {
                            item1.setNum(item1.getNum() + item2.getNum());
                            return item1;
                        })
                )
                .values();
        order.setItems(Lists.newArrayList(groupBySkuErpCodeAndSumSkuNum));
        return order;
    }

    /**
     * 检查订单已发货
     *
     * @param order 订单
     * @return 执行结果
     */
    private boolean checkOrderHasShipping(PddOrderInformationGetResponse.OrderInfoGetResponseOrderInfo order) {
        // 判断订单状态已发货，无需重复发货
        if (order.getOrderStatus() == 2 || order.getOrderStatus() == 3) {
            log.warn("订单已发货，无需重复发货 {} {}", order.getOrderSn(), order.getOrderStatus());
            return true;
        }
        // 判断订单状态不是待发货，抛异常
        // 拼多多订单关闭状态由退款成功状态判断，退款成功后还是待发货
        if (order.getOrderStatus() != 1 || order.getRefundStatus() == 4) {
            // 例：平台的订单状态不符合推送发货状态的条件 6923580254658762042 orderState: 2 stepOrderState: 1
            Integer stepOrderStatus = Optional.ofNullable(order.getStepOrderInfo())
                    .map(PddOrderInformationGetResponse.OrderInfoGetResponseOrderInfoStepOrderInfo::getStepTradeStatus)
                    .orElse(null);
            String msg = String.format("%s %s orderState: %s stepOrderState: %s",
                    BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrDesc(),
                    order.getOrderSn(), order.getOrderStatus(), stepOrderStatus);
            throw new BizException(BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrCode(), msg);
        }
        return false;
    }

    /**
     * 获取发票主体id
     *
     * @param shop            店铺
     * @param invoiceApplyDTO 发票
     * @return 发票主体id
     */
    private Optional<String> getInvoiceEntityId(ShopDO shop, InvoiceApplyDTO invoiceApplyDTO) {
        // 获取国补订单信息，取平台字段 subMallId 为发票主体id
        PddOrderTradeinInfoResponse nationalSubsidy = pddRpc.getNationalSubsidy(shop, invoiceApplyDTO.getOrderNo());
        return Optional.ofNullable(nationalSubsidy)
                .map(PddOrderTradeinInfoResponse::getResponse)
                .map(PddOrderTradeinInfoResponse.Response::getResult)
                .map(PddOrderTradeinInfoResponse.ResponseResult::getOrderActivityInfoMap)
                .map(map -> map.get(invoiceApplyDTO.getOrderNo()))
                .filter(invoice -> BooleanUtils.isTrue(invoice.getSubsidyEffective()))
                .map(PddOrderTradeinInfoResponse.ResponseResultOrderActivityInfoMapValue::getSubMallId)
                .map(String::valueOf);
    }

}
