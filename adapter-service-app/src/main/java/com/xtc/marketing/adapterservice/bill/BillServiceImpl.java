package com.xtc.marketing.adapterservice.bill;

import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.xtc.marketing.adapterservice.bill.dto.query.BillDownloadQry;
import com.xtc.marketing.adapterservice.bill.executor.extension.BillExtConstant;
import com.xtc.marketing.adapterservice.bill.executor.extension.BillExtPt;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class BillServiceImpl implements BillService {

    private final ExtensionExecutor extensionExecutor;

    @Override
    public Resource billDownload(BillDownloadQry qry) {
        BizScenario scenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID,
                BillExtConstant.USE_CASE, qry.getBillPlatform().name());
        return extensionExecutor.execute(BillExtPt.class, scenario, exe -> exe.billDownload(qry));
    }

}
