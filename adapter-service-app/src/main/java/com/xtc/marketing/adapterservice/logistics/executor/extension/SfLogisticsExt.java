package com.xtc.marketing.adapterservice.logistics.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.logistics.converter.SfLogisticsConverter;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsCloudPrintDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsOrderDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCancelOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCloudPrintCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsInterceptCmd;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsOrderGetQry;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsRouteListQry;
import com.xtc.marketing.adapterservice.rpc.sf.SfRpc;
import com.xtc.marketing.adapterservice.rpc.sf.SfWarehouseXmlRpc;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.SfBaseDTO;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.SfOrderDTO;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.SfRouteDTO;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.command.SfCreateOrderCmd;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.command.SfInterceptCmd;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.command.SfOutboundInterceptXmlCmd;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = LogisticsExtConstant.USE_CASE, scenario = LogisticsExtConstant.SCENARIO_SF)
public class SfLogisticsExt implements LogisticsExtPt {

    private final SfRpc sfRpc;
    private final SfLogisticsConverter sfLogisticsConverter;
    private final SfWarehouseXmlRpc sfWarehouseXmlRpc;

    @Override
    public List<LogisticsRouteDTO> routes(LogisticsAccountDO account, LogisticsRouteListQry qry) {
        List<SfRouteDTO> listRouteDTO = sfRpc.searchRoutes(account, qry.getWaybillNo());
        return sfLogisticsConverter.toAdapterRouteDTO(listRouteDTO);
    }

    @Override
    public LogisticsOrderDTO createOrder(LogisticsAccountDO account, LogisticsCreateOrderCmd cmd) {
        SfCreateOrderCmd sfCreateOrderCmd = sfLogisticsConverter.toSfCreateOrderCmd(cmd);
        // 解析平台参数，传平台自定义参数给平台
        JsonObject platformParams = GsonUtil.jsonToObject(cmd.getPlatformJson());
        if (platformParams != null) {
            // 读取 extraInfoList 值透传给平台
            Object extraInfoList = platformParams.isJsonObject() ? platformParams.get("extraInfoList") : null;
            sfCreateOrderCmd.setExtraInfoList(extraInfoList);
            // 读取 isDocall 值透传给平台
            Integer isDocall = GsonUtil.getAsInt(platformParams, "isDocall");
            sfCreateOrderCmd.setIsDocall(isDocall);
        }
        // 发起请求并解析响应
        SfOrderDTO sfOrderDTO = sfRpc.createOrder(account, sfCreateOrderCmd);
        return LogisticsOrderDTO.builder()
                .wayBillNo(sfOrderDTO.getRouteLabelInfo().get(0).getRouteLabelData().getWaybillNo())
                .orderDetail(GsonUtil.objectToJson(sfOrderDTO.getMsgData()))
                .build();
    }

    @Override
    public String getOrder(LogisticsAccountDO account, LogisticsOrderGetQry qry) {
        SfBaseDTO sfBaseDTO = sfRpc.searchOrder(account, qry.getOrderId());
        return GsonUtil.objectToJson(sfBaseDTO.getMsgData());
    }

    @Override
    public String getWaybillNo(LogisticsAccountDO account, LogisticsOrderGetQry qry) {
        return sfRpc.getWaybillNoByOrderId(account, qry.getOrderId());
    }

    @Override
    public boolean cancelOrder(LogisticsAccountDO account, LogisticsCancelOrderCmd cmd) {
        sfRpc.cancelOrder(account, cmd.getOrderId());
        return true;
    }

    @Override
    public boolean intercept(LogisticsAccountDO account, LogisticsInterceptCmd cmd) {
        if (StringUtils.isBlank(cmd.getBizAccount())) {
            throw BizException.of("顺丰仓库拦截出库失败，业务账号（月结卡号）不能为空");
        }
        // 物流订单拦截
        boolean sfIntercept = GsonUtil.getAsBoolean(cmd.getPlatformJson(), "sfIntercept");
        if (sfIntercept) {
            SfInterceptCmd sfInterceptCmd = sfLogisticsConverter.toSfInterceptCmd(cmd);
            sfRpc.intercept(account, sfInterceptCmd);
            return true;
        }
        // 仓库订单拦截
        SfOutboundInterceptXmlCmd.Address address = SfOutboundInterceptXmlCmd.Address.builder()
                .contact(cmd.getReceiverName()).phone(cmd.getReceiverMobile())
                .province(cmd.getReceiverProvince()).city(cmd.getReceiverCity()).county(cmd.getReceiverDistrict())
                .address(cmd.getReceiverAddress()).build();
        SfOutboundInterceptXmlCmd sfOutboundInterceptXmlCmd = SfOutboundInterceptXmlCmd.builder()
                .monthlyCardNo(cmd.getBizAccount()).waybillNo(cmd.getOrderId()).receiveAddress(address).build();
        sfWarehouseXmlRpc.interceptOutbound(sfOutboundInterceptXmlCmd);
        return true;
    }

    @Override
    public LogisticsCloudPrintDTO cloudPrint(LogisticsAccountDO account, LogisticsCloudPrintCmd cmd) {
        String templateUrl = sfRpc.cloudPrint(account, cmd.getWaybillNo());
        return LogisticsCloudPrintDTO.builder().templateUrl(templateUrl).build();
    }

}
