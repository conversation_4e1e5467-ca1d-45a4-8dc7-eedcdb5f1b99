package com.xtc.marketing.adapterservice.shop.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.pdd.pop.sdk.http.api.pop.request.PddFdsOrderListGetRequest;
import com.pdd.pop.sdk.http.api.pop.response.PddFdsOrderGetResponse;
import com.pdd.pop.sdk.http.api.pop.response.PddFdsOrderListGetResponse;
import com.pdd.pop.sdk.http.api.pop.response.PddFdsWaybillGetResponse;
import com.xtc.marketing.adapterservice.exception.BizErrorCode;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.pdd.PddRpc;
import com.xtc.marketing.adapterservice.rpc.pdd.pdddto.PddFdsWaybillGetRequest;
import com.xtc.marketing.adapterservice.rpc.pdd.pdddto.enums.PddLogisticsCompany;
import com.xtc.marketing.adapterservice.shop.converter.PddDistrShopConverter;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.domianservice.SetShopCodeUseCacheDomainService;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.dto.query.*;
import com.xtc.marketing.adapterservice.shop.enums.ShopTypeEnum;
import com.xtc.marketing.adapterservice.shop.executor.query.ShopGetQryExe;
import com.xtc.marketing.adapterservice.shop.util.SkuUtil;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = ShopExtConstant.USE_CASE, scenario = ShopExtConstant.SCENARIO_PDD_DISTR)
public class PddDistrShopExt implements ShopExtPt {

    private final PddRpc pddRpc;
    private final PddDistrShopConverter pddDistrShopConverter;
    private final ShopGetQryExe shopGetQryExe;
    private final SetShopCodeUseCacheDomainService setShopCodeUseCacheDomainService;

    @Override
    public ShopDO getShopToken(ShopDO shop) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<OrderDTO> pageOrders(ShopDO shop, OrderPageQry qry) {
        ShopDO relatedShop = shopGetQryExe.execute(shop.getShopId());

        PddFdsOrderListGetRequest.ParamFdsOrderListGetRequest param = new PddFdsOrderListGetRequest.ParamFdsOrderListGetRequest();
        param.setStartUpdatedTime(DateUtil.toEpochMilli(qry.getUpdateTimeStart()));
        param.setEndUpdatedTime(DateUtil.toEpochMilli(qry.getUpdateTimeEnd()));
        param.setPage(qry.getPageIndex());
        param.setPageSize(qry.getPageSize());
        PddFdsOrderListGetRequest request = new PddFdsOrderListGetRequest();
        request.setParamFdsOrderListGetRequest(param);

        PddFdsOrderListGetResponse.InnerPddFdsOrderListGetResponse orderListData = pddRpc.pageOrdersDistr(relatedShop, request);
        if (orderListData == null || CollectionUtils.isEmpty(orderListData.getOrderList())) {
            return PageResponse.of(qry.getPageSize(), qry.getPageIndex());
        }

        /*
        1. 代理店铺需要过滤店铺订单，工厂店铺无需过滤
        2. 拆分订单明细里的 skuIds 并且转换成统一订单数据
        3. 为每个订单关联店铺代码
         */
        List<OrderDTO> orders = orderListData.getOrderList().stream()
                .filter(order -> ShopTypeEnum.XTC == shop.getShopType() || shop.getAgentCode().equals(order.getMallMaskId()))
                .map(this::convertToAdapterOrderDTO)
                .peek(setShopCodeUseCacheDomainService::setShopCode)
                .collect(Collectors.toList());
        return PageResponse.of(orders, orderListData.getTotal(), qry.getPageSize(), qry.getPageIndex());
    }

    @Override
    public OrderDTO getOrder(ShopDO shop, OrderGetQry qry) {
        ShopDO relatedShop = this.getRelatedShop(shop);
        PddFdsOrderGetResponse.InnerPddFdsOrderGetResponse originOrder = pddRpc.getOrderDistr(relatedShop, qry.getOrderNo(), shop.getAgentCode());
        if (originOrder == null) {
            return null;
        }
        OrderDTO orderDTO = this.convertToAdapterOrderDTO(originOrder);
        orderDTO.setOriginOrderData(GsonUtil.objectToJson(originOrder));
        return orderDTO;
    }

    @Override
    public boolean orderRemark(ShopDO shop, OrderRemarkCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean orderShipping(ShopDO shop, OrderShippingCmd cmd) {
        ShopDO relatedShop = this.getRelatedShop(shop);
        boolean hasShipping = this.checkOrderHasShipping(relatedShop, cmd.getOrderNo(), shop.getAgentCode());
        if (hasShipping) {
            return true;
        }
        String logisticsCompany = cmd.getLogisticsCompany().name();
        return pddRpc.orderShippingDistr(relatedShop, shop.getAgentCode(), cmd.getOrderNo(), logisticsCompany, cmd.getWaybillNo());
    }

    @Override
    public boolean orderShippingCancel(ShopDO shop, OrderShippingCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean orderDummyShipping(ShopDO shop, OrderDummyShippingCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public OrderDecryptDTO orderDecrypt(ShopDO shop, OrderDecryptCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<InvoiceApplyDTO> pageInvoiceApply(ShopDO shop, InvoiceApplyPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public InvoiceApplyDTO getInvoiceApply(ShopDO shop, InvoiceApplyGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceFile(ShopDO shop, OrderUploadInvoiceCmd cmd, MultipartFile invoiceFile) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceBase64(ShopDO shop, InvoiceUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public InvoiceAmountDTO getInvoiceAmount(ShopDO shop, String orderNo) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<CommentDTO> pageComments(ShopDO shop, CommentPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<RefundDTO> pageRefunds(ShopDO shop, RefundPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public RefundDTO getRefund(ShopDO shop, RefundGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public ShopLogisticsOrderDTO createLogisticsOrder(ShopDO shop, ShopLogisticsOrderCreateCmd cmd) {
        ShopDO relatedShop = this.getRelatedShop(shop);
        PddLogisticsCompany logisticsCompany = PddLogisticsCompany.valueOf(cmd.getLogisticsCompany().name());
        PddFdsWaybillGetRequest.ParamFdsWaybillGetRequestTradeOrderInfoDtosItemOrderInfoTradeOrderListItem item =
                pddDistrShopConverter.toTradeOrder(shop, cmd);
        PddFdsWaybillGetRequest.ParamFdsWaybillGetRequestTradeOrderInfoDtosItem order =
                pddDistrShopConverter.toTradeOrderInfoDto(cmd, shop, logisticsCompany, item);
        PddFdsWaybillGetRequest request = pddDistrShopConverter.toWaybillGetRequest(cmd, order);

        PddFdsWaybillGetResponse.InnerPddFdsWaybillGetResponseModulesItem logisticsOrder =
                pddRpc.createLogisticsOrderDistr(relatedShop, request);

        return ShopLogisticsOrderDTO.builder()
                .wayBillNo(logisticsOrder.getWaybillCode())
                .orderDetail(logisticsOrder.getPrintData())
                .build();
    }

    @Override
    public boolean cancelLogisticsOrder(ShopDO shop, ShopLogisticsOrderCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void refundGoodsToWarehouse(ShopDO shop, RefundGoodsToWarehouseCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void barcodeUpload(ShopDO shop, BarcodeUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    /**
     * 订单列表数据转换
     *
     * @param platformOrder 平台订单
     * @return 订单数据
     */
    private OrderDTO convertToAdapterOrderDTO(PddFdsOrderGetResponse.InnerPddFdsOrderGetResponse platformOrder) {
        // 拆分平台维护的 skuId
        OrderItemDTO item = pddDistrShopConverter.toAdapterOrderItemDTO(platformOrder);
        List<OrderItemDTO> items = SkuUtil.splitSkuIdsAndCloneItem(item, OrderItemDTO::getSkuErpCode, OrderItemDTO::setSkuErpCode);
        // 转换统一的数据结构
        OrderDTO order = pddDistrShopConverter.toAdapterOrderDTO(platformOrder);
        order.setItems(items);
        return order;
    }

    /**
     * 订单列表数据转换
     *
     * @param platformOrder 平台订单
     * @return 订单数据
     */
    private OrderDTO convertToAdapterOrderDTO(PddFdsOrderListGetResponse.InnerPddFdsOrderListGetResponseOrderListItem platformOrder) {
        // 拆分平台维护的 skuId
        OrderItemDTO item = pddDistrShopConverter.toAdapterOrderItemDTO(platformOrder);
        List<OrderItemDTO> items = SkuUtil.splitSkuIdsAndCloneItem(item, OrderItemDTO::getSkuErpCode, OrderItemDTO::setSkuErpCode);
        // 转换统一的数据结构
        OrderDTO order = pddDistrShopConverter.toAdapterOrderDTO(platformOrder);
        order.setItems(items);
        return order;
    }

    /**
     * 检查订单已发货
     *
     * @param shop        店铺
     * @param orderNo     订单号
     * @param distrShopId 代发店铺id
     * @return 执行结果
     */
    private boolean checkOrderHasShipping(ShopDO shop, String orderNo, String distrShopId) {
        PddFdsOrderGetResponse.InnerPddFdsOrderGetResponse order = pddRpc.getOrderDistr(shop, orderNo, distrShopId);
        // 判断订单状态已发货，无需重复发货
        if (order.getReturnStatus() == 1) {
            log.warn("订单已发货，无需重复发货 {} {}", orderNo, order.getReturnStatus());
            return true;
        }
        // 判断订单状态不是待发货，抛异常
        if (order.getReturnStatus() != 0) {
            // 例：平台的订单状态不符合推送发货状态的条件 6923580254658762042 distrStatus: 0
            String msg = String.format("%s %s distrStatus: %s",
                    BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrDesc(), orderNo, order.getReturnStatus());
            throw new BizException(BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrCode(), msg);
        }
        return false;
    }

    /**
     * 查询关联的厂家店铺
     *
     * @param shop 代发店铺
     * @return 厂家店铺
     */
    private ShopDO getRelatedShop(ShopDO shop) {
        if (shop.getShopType() != ShopTypeEnum.AGENT) {
            throw BizException.of("店铺不是代理类型，不支持使用代发店铺的操作");
        }
        // 代发店铺通过 shopId 关联的厂家店铺
        return shopGetQryExe.execute(shop.getShopId());
    }

}
