package com.xtc.marketing.adapterservice.logistics.converter;

import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCargoCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsInterceptCmd;
import com.xtc.marketing.adapterservice.logistics.enums.YtoLogisticsStatusEnum;
import com.xtc.marketing.adapterservice.rpc.yto.ytodto.YtoRouteDTO;
import com.xtc.marketing.adapterservice.rpc.yto.ytodto.command.YtoCargoCmd;
import com.xtc.marketing.adapterservice.rpc.yto.ytodto.command.YtoCreateOrderCmd;
import com.xtc.marketing.adapterservice.rpc.yto.ytodto.command.YtoInterceptCmd;
import com.xtc.marketing.adapterservice.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.util.List;

@Mapper(
        componentModel = "spring",
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface YtoLogisticsConverter {

    /**
     * 转换平台下单参数
     *
     * @param source 统一下单参数
     * @return 平台下单参数
     */
    @Mapping(target = "logisticsNo", source = "orderId")
    @Mapping(target = "senderProvinceName", source = "senderProvince")
    @Mapping(target = "senderCityName", source = "senderCity")
    @Mapping(target = "senderCountyName", source = "senderDistrict")
    @Mapping(target = "senderTownName", source = "senderTown")
    @Mapping(target = "senderAddress", source = "senderAddress")
    @Mapping(target = "senderName", source = "senderName")
    @Mapping(target = "senderMobile", source = "senderMobile")
    @Mapping(target = "recipientProvinceName", source = "receiverProvince")
    @Mapping(target = "recipientCityName", source = "receiverCity")
    @Mapping(target = "recipientCountyName", source = "receiverDistrict")
    @Mapping(target = "recipientTownName", source = "receiverTown")
    @Mapping(target = "recipientAddress", source = "receiverAddress")
    @Mapping(target = "recipientName", source = "receiverName")
    @Mapping(target = "recipientMobile", source = "receiverMobile")
    @Mapping(target = "remark", source = "remark")
    @Mapping(target = "goods", source = "cargos", qualifiedByName = "toYtoCargoCmd")
    YtoCreateOrderCmd toYtoCreateOrderCmd(LogisticsCreateOrderCmd source);

    /**
     * 转换平台拦截参数
     *
     * @param source 统一拦截参数
     * @return 平台拦截参数
     */
    @Mapping(target = "wantedDesc", source = "interceptMemo")
    @Mapping(target = "receiveAddress", source = "receiverAddress")
    @Mapping(target = "receiveCountyName", source = "receiverDistrict")
    @Mapping(target = "receiveCityName", source = "receiverCity")
    @Mapping(target = "receiveProvName", source = "receiverProvince")
    @Mapping(target = "receiverTel", source = "receiverMobile")
    @Mapping(target = "receiverName", source = "receiverName")
    @Mapping(target = "wayBillNo", source = "orderId")
    YtoInterceptCmd toYtoInterceptCmd(LogisticsInterceptCmd source);

    /**
     * 转换平台货物参数
     *
     * @param source 统一货物参数
     * @return 平台货物参数
     */
    @Named("toYtoCargoCmd")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "quantity", source = "quantity")
    YtoCargoCmd toYtoCargoCmd(LogisticsCargoCmd source);

    /**
     * 转换统一路由数据
     *
     * @param source 平台路由数据
     * @return 统一路由数据
     */
    List<LogisticsRouteDTO> toAdapterRouteDTO(List<YtoRouteDTO> source);

    /**
     * 转换统一路由数据
     *
     * @param source 平台路由数据
     * @return 统一路由数据
     */
    @Mapping(target = "time", source = "uploadTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "detail", source = "processInfo")
    @Mapping(target = "code", source = "infoContent")
    LogisticsRouteDTO toAdapterRouteDTO(YtoRouteDTO source);

    /**
     * 设置路由数据
     *
     * @param route         统一路由数据
     * @param platformRoute 平台路由数据
     */
    @AfterMapping
    default void setAdapterRouteDTO(@MappingTarget LogisticsRouteDTO route, YtoRouteDTO platformRoute) {
        // 拼接节点数据：城市 + 区县
        String node = platformRoute.getCity() + platformRoute.getDistrict();
        route.setNode(node);
        // 解析物流状态
        String title = YtoLogisticsStatusEnum.valueOf(platformRoute.getInfoContent()).getDesc();
        route.setTitle(title);
    }

    /**
     * 时间转换
     *
     * @param time 时间字符串
     * @return LocalDateTime
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(String time) {
        if (StringUtils.isBlank(time)) {
            return null;
        }
        return DateUtil.toLocalDateTime(time);
    }

}
