package com.xtc.marketing.adapterservice.notify;

import com.xtc.marketing.adapterservice.notify.dto.PushLogDTO;
import com.xtc.marketing.adapterservice.notify.dto.ReceiveLogDTO;
import com.xtc.marketing.adapterservice.notify.dto.query.PushLogPageQry;
import com.xtc.marketing.adapterservice.notify.dto.query.ReceiveLogPageQry;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;

/**
 * 通知管理服务接口
 */
public interface NotifyAdminService {

    /**
     * 推送记录分页列表
     *
     * @param qry 参数
     * @return 推送记录分页列表
     */
    PageResponse<PushLogDTO> pagePushLogs(PushLogPageQry qry);

    /**
     * 接收记录分页列表
     *
     * @param qry 参数
     * @return 接收记录分页列表
     */
    PageResponse<ReceiveLogDTO> pageReceiveLogs(ReceiveLogPageQry qry);

    /**
     * 删除接收记录
     *
     * @param id 接收记录id
     */
    void removeReceiveLog(long id);

}
