package com.xtc.marketing.adapterservice.subscribe.executor.extension;

import com.alibaba.cola.extension.ExtensionPointI;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;

/**
 * 消息订阅扩展点
 */
public interface SubscribeExtPt extends ExtensionPointI {

    /**
     * 开启消息订阅
     *
     * @param shop     店铺
     * @param bizParam 业务参数
     * @return 执行结果
     */
    String connect(ShopDO shop, String bizParam);

    /**
     * 关闭消息订阅
     *
     * @param shop 店铺
     * @return 执行结果
     */
    String close(ShopDO shop);

}
