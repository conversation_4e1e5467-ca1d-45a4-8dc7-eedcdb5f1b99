package com.xtc.marketing.adapterservice.logistics.converter;

import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCargoCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsInterceptCmd;
import com.xtc.marketing.adapterservice.rpc.ems.emsdto.EmsRouteDTO;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.SfRouteDTO;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.command.SfCargoCmd;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.command.SfContactCmd;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.command.SfCreateOrderCmd;
import com.xtc.marketing.adapterservice.rpc.sf.sfdto.command.SfInterceptCmd;
import com.xtc.marketing.adapterservice.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Mapper(
        componentModel = "spring",
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface SfLogisticsConverter {

    /**
     * 转换平台下单参数
     *
     * @param source 统一下单参数
     * @return 平台下单参数
     */
    @Mapping(target = "orderId", source = "orderId")
    @Mapping(target = "monthlyCard", source = "bizAccount")
    @Mapping(target = "expressTypeId", source = "expressTypeId")
    @Mapping(target = "sendStartTm", source = "pickUpTimeStart", qualifiedByName = "localDateTimeToStr")
    @Mapping(target = "cargoDetails", source = "cargos", qualifiedByName = "toSfCargoCmd")
    SfCreateOrderCmd toSfCreateOrderCmd(LogisticsCreateOrderCmd source);

    /**
     * 转换平台货物参数
     *
     * @param source 统一货物参数
     * @return 平台货物参数
     */
    @Named("toSfCargoCmd")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "count", source = "quantity")
    SfCargoCmd toSfCargoCmd(LogisticsCargoCmd source);

    /**
     * 转换统一路由数据
     *
     * @param source 平台路由数据
     * @return 统一路由数据
     */
    List<LogisticsRouteDTO> toAdapterRouteDTO(List<SfRouteDTO> source);

    /**
     * 转换统一路由数据
     *
     * @param source 平台路由数据
     * @return 统一路由数据
     */
    @Mapping(target = "node", source = "acceptAddress")
    @Mapping(target = "time", source = "acceptTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "detail", source = "remark")
    @Mapping(target = "code", source = "opCode")
    LogisticsRouteDTO toAdapterRouteDTO(SfRouteDTO source);

    /**
     * 转换平台拦截参数
     *
     * @param source 统一拦截参数
     * @return 平台拦截参数
     */
    @Mapping(target = "waybillNo", source = "orderId")
    @Mapping(target = "monthlyCardNo", source = "bizAccount")
    @Mapping(target = "newDestAddress.contact", source = "receiverName")
    @Mapping(target = "newDestAddress.phone", source = "receiverMobile")
    @Mapping(target = "newDestAddress.province", source = "receiverProvince")
    @Mapping(target = "newDestAddress.city", source = "receiverCity")
    @Mapping(target = "newDestAddress.county", source = "receiverDistrict")
    @Mapping(target = "newDestAddress.address", source = "receiverAddress")
    SfInterceptCmd toSfInterceptCmd(LogisticsInterceptCmd source);

    /**
     * 转换平台下单参数时，组装寄件人、收件人
     *
     * @param target 平台下单参数
     * @param source 统一下单参数
     */
    @AfterMapping
    default void setSfCreateOrderCmd(@MappingTarget SfCreateOrderCmd target, LogisticsCreateOrderCmd source) {
        // 寄件人
        SfContactCmd sender = SfContactCmd.builder()
                .contact(source.getSenderName())
                .tel(source.getSenderMobile())
                .province(source.getSenderProvince())
                .city(source.getSenderCity())
                .county(source.getSenderDistrict())
                .address(source.getSenderAddress())
                .contactType(1)
                .build();
        // 收件人
        SfContactCmd receiver = SfContactCmd.builder()
                .contact(source.getReceiverName())
                .tel(source.getReceiverMobile())
                .province(source.getReceiverProvince())
                .city(source.getReceiverCity())
                .county(source.getReceiverDistrict())
                .address(source.getReceiverAddress())
                .contactType(2)
                .build();
        target.setContactInfoList(Arrays.asList(receiver, sender));
    }

    /**
     * 设置路由节点
     *
     * @param route         统一路由数据
     * @param platformRoute 平台路由数据
     */
    @AfterMapping
    default void setNode(@MappingTarget LogisticsRouteDTO route, EmsRouteDTO platformRoute) {
        String node = StringUtils.join(platformRoute.getOpOrgProvName(), platformRoute.getOpOrgCity());
        route.setNode(node);
    }

    /**
     * 字符串转时间
     *
     * @param time 时间字符串
     * @return 时间
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(String time) {
        return StringUtils.isNotBlank(time) ? DateUtil.toLocalDateTime(time) : null;
    }

    /**
     * 时间转字符串
     *
     * @param time 时间
     * @return 时间字符串
     */
    @Named("localDateTimeToStr")
    default String localDateTimeToStr(LocalDateTime time) {
        return time != null ? DateUtil.toString(time) : null;
    }

}
