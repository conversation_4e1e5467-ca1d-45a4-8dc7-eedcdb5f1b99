package com.xtc.marketing.adapterservice.logistics.converter;

import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsInterceptCmd;
import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.request.JtExpressCreateOrderRequest;
import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.request.JtExpressInterceptOrderRequest;
import com.xtc.marketing.adapterservice.rpc.jitu.jitudto.response.JtExpressRouteResponse;
import org.mapstruct.*;

import java.util.List;

/**
 * 极兔快递转换器
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface JtExpressConverter {

    /**
     * 将极兔轨迹响应列表转换为统一的路由列表
     */
    List<LogisticsRouteDTO> toLogisticsRouteDTO(List<JtExpressRouteResponse.JtExpressTraceDetail> details);

    /**
     * 将极兔轨迹响应转换为统一的路由列表
     */
    @Mapping(target = "detail", source = "desc")
    @Mapping(target = "title", source = "scanType")
    @Mapping(target = "code", source = "scanType")
    LogisticsRouteDTO toLogisticsRouteDTO(JtExpressRouteResponse.JtExpressTraceDetail detail);

    /**
     * 组装极兔下单参数
     */
    @Mapping(target = "customerCode", source = "account.bizAccount")
    @Mapping(target = "txlogisticId", source = "cmd.orderId")
    @Mapping(target = "expressType", constant = "EZ")
    @Mapping(target = "orderType", constant = "2")
    @Mapping(target = "serviceType", constant = "01")
    @Mapping(target = "deliveryType", constant = "06")
    @Mapping(target = "payType", constant = "PP_PM")
    @Mapping(target = "sender.name", source = "cmd.senderName")
    @Mapping(target = "sender.mobile", source = "cmd.senderMobile")
    @Mapping(target = "sender.prov", source = "cmd.senderProvince")
    @Mapping(target = "sender.area", source = "cmd.senderCity")
    @Mapping(target = "sender.city", source = "cmd.senderDistrict")
    @Mapping(target = "sender.countryCode", constant = "CHN")
    @Mapping(target = "sender.address", source = "cmd.senderAddress")
    @Mapping(target = "receiver.name", source = "cmd.receiverName")
    @Mapping(target = "receiver.mobile", source = "cmd.receiverMobile")
    @Mapping(target = "receiver.prov", source = "cmd.receiverProvince")
    @Mapping(target = "receiver.city", source = "cmd.receiverCity")
    @Mapping(target = "receiver.area", source = "cmd.receiverDistrict")
    @Mapping(target = "receiver.address", source = "cmd.receiverAddress")
    @Mapping(target = "receiver.countryCode", constant = "CHN")
    @Mapping(target = "goodsType", constant = "bm000002")
    @Mapping(target = "weight", constant = "1")
    @Mapping(target = "totalQuantity", constant = "1")
    JtExpressCreateOrderRequest toJtExpressCreateOrderRequest(LogisticsCreateOrderCmd cmd, LogisticsAccountDO account);

    /**
     * 组装极兔拦截订单参数
     */
    @Mapping(target = "mailNo", source = "cmd.orderId")
    @Mapping(target = "customerCode", source = "account.bizAccount")
    @Mapping(target = "reason", source = "cmd.interceptMemo")
    @Mapping(target = "applyTypeCode", constant = "4")
    @Mapping(target = "receiveProvince", source = "cmd.receiverProvince")
    @Mapping(target = "receiveCity", source = "cmd.receiverCity")
    @Mapping(target = "receiveDistrict", source = "cmd.receiverDistrict")
    @Mapping(target = "receiveAddress", source = "cmd.receiverAddress")
    @Mapping(target = "receiveUsername", source = "cmd.receiverName")
    @Mapping(target = "receiveMobilPhone", source = "cmd.receiverMobile")
    JtExpressInterceptOrderRequest toJtExpressInterceptOrderRequest(LogisticsInterceptCmd cmd, LogisticsAccountDO account);

}