package com.xtc.marketing.adapterservice.invoice.extension;

import com.alibaba.cola.extension.ExtensionPointI;
import com.xtc.marketing.adapterservice.invoice.dto.InvoiceResultDTO;
import com.xtc.marketing.adapterservice.invoice.dto.command.InvoiceCreateCmd;
import com.xtc.marketing.adapterservice.invoice.dto.command.InvoiceCreateRedCmd;
import com.xtc.marketing.adapterservice.invoice.dto.query.InvoiceResultQry;

import java.util.List;

/**
 * 开票扩展点
 */
public interface InvoiceExtPt extends ExtensionPointI {

    /**
     * 开票
     *
     * @param cmd 参数
     * @return 执行结果
     */
    boolean createInvoice(InvoiceCreateCmd cmd);

    /**
     * 冲红
     *
     * @param cmd 参数
     * @return 执行结果
     */
    boolean createRedInvoice(InvoiceCreateRedCmd cmd);

    /**
     * 查询开票结果
     *
     * @param qry 参数
     * @return 开票结果
     */
    List<InvoiceResultDTO> listInvoiceResult(InvoiceResultQry qry);

    /**
     * 生成发票流水号
     *
     * @param shopCode 店铺代码
     * @return 发票流水号
     */
    String createSerialNo(String shopCode);

}
