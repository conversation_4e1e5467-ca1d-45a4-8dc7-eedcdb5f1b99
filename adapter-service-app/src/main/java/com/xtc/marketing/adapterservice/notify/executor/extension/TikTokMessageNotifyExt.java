package com.xtc.marketing.adapterservice.notify.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.msg.trade_TradeAddressChangeApplied.TradeTradeAddressChangeAppliedRequest;
import com.doudian.open.msg.trade_TradeAddressChangeApplied.param.TradeTradeAddressChangeAppliedParam;
import com.doudian.open.msg.trade_TradeAddressChanged.TradeTradeAddressChangedRequest;
import com.doudian.open.msg.trade_TradeAddressChanged.param.TradeTradeAddressChangedParam;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.notify.dataobject.ReceiveLogDO;
import com.xtc.marketing.adapterservice.notify.dto.command.NotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.enums.NotifyEnum;
import com.xtc.marketing.adapterservice.rpc.tiktok.TikTokRpc;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.executor.query.ShopGetQryExe;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletRequest;

/**
 * 通知扩展点 - 抖音消息通知
 */
@Slf4j
@RequiredArgsConstructor
@Extension(bizId = NotifyExtConstant.BIZ_ID_TIKTOK,
        useCase = NotifyExtConstant.USE_CASE_SHOP, scenario = NotifyExtConstant.SCENARIO_MESSAGE)
public class TikTokMessageNotifyExt implements NotifyExtPt {

    private final TikTokRpc tikTokRpc;
    private final ShopGetQryExe shopGetQryExe;

    @Override
    public ReceiveLogDO createReceiveLog(NotifyEnum notify, NotifyReceiveCmd cmd) {
        String responseStr;
        String dataId = null;
        String rawData = cmd.getData();

        try {
            // 解析消息数据
            JsonArray messageArray = GsonUtil.jsonToBean(rawData, JsonArray.class);
            if (messageArray == null || messageArray.size() == 0) {
                log.warn("抖音消息数据为空");
                responseStr = buildResponse(100002, "消息数据为空");
                return ReceiveLogDO.builder().responseStr(responseStr).build();
            }

            // 检查是否为测试消息
            JsonObject firstMessage = messageArray.get(0).getAsJsonObject();
            String firstTag = firstMessage.get("tag").getAsString();

            ShopDO shop = null;
            if (!"0".equals(firstTag)) {
                // 非测试消息需要验签，获取店铺信息
                JsonObject firstData = firstMessage.getAsJsonObject("data");
                if (!firstData.has("shop_id")) {
                    log.error("消息数据缺少shop_id字段");
                    responseStr = buildResponse(100001, "验签失败");
                    return ReceiveLogDO.builder().responseStr(responseStr).build();
                }

                Long shopId = firstData.get("shop_id").getAsLong();
                shop = shopGetQryExe.getByShopId(shopId.toString());
                if (shop == null) {
                    log.error("店铺不存在: {}", shopId);
                    responseStr = buildResponse(100001, "验签失败");
                    return ReceiveLogDO.builder().responseStr(responseStr).build();
                }

                // 验证消息签名
                if (!verifySignature(cmd.getRequest(), rawData, shop)) {
                    log.warn("抖音消息验签失败");
                    responseStr = buildResponse(100001, "验签失败");
                    return ReceiveLogDO.builder().responseStr(responseStr).build();
                }
            }

            // 处理消息
            for (JsonElement element : messageArray) {
                JsonObject messageObj = element.getAsJsonObject();
                String tag = messageObj.get("tag").getAsString();
                String msgId = messageObj.get("msg_id").getAsString();
                JsonObject data = messageObj.getAsJsonObject("data");

                dataId = msgId;

                // 根据消息标签处理不同类型的消息
                if ("0".equals(tag)) {
                    // 测试消息，直接返回成功
                    log.info("收到抖音测试消息: {}", msgId);
                } else if ("doudian_trade_TradeAddressChangeApplied".equals(tag)) {
                    // 买家收货信息变更申请消息
                    handleAddressChangeApplied(data.toString());
                } else if ("doudian_trade_TradeAddressChanged".equals(tag)) {
                    // 买家收货信息变更成功消息
                    handleAddressChanged(data.toString());
                } else {
                    // 其他消息类型返回验签失败（按需求要求）
                    log.info("收到未支持的消息类型: {}", tag);
                    responseStr = buildResponse(100001, "验签失败");
                    return ReceiveLogDO.builder().responseStr(responseStr).build();
                }
            }

            // 所有消息处理成功
            responseStr = buildSuccessResponse();

        } catch (Exception e) {
            log.error("抖音消息处理异常", e);
            responseStr = buildResponse(100003, "系统错误");
        }

        return ReceiveLogDO.builder()
                .dataId(dataId)
                .rawData(rawData)
                .responseStr(responseStr)
                .build();
    }

    @Override
    public ResponseEntity<String> notifyResponse(String responseStr) {
        return ResponseEntity.ok().body(responseStr);
    }

    @Override
    public Object convertToNotifyBean(String data) {
        return GsonUtil.jsonToObject(data);
    }

    /**
     * 验证抖音消息签名
     */
    private boolean verifySignature(HttpServletRequest request, String body, ShopDO shop) {
        try {
            String eventSign = request.getHeader("event-sign");
            String appId = request.getHeader("app-id");

            if (StringUtils.isBlank(eventSign) || StringUtils.isBlank(appId)) {
                log.warn("缺少签名头信息: event-sign={}, app-id={}", eventSign, appId);
                return false;
            }

            // 验证 app-id 是否匹配
            if (!appId.equals(shop.getAppKey())) {
                log.warn("app-id 不匹配: 请求={}, 店铺={}", appId, shop.getAppKey());
                return false;
            }

            // 构建签名参数：appId + body + appSecret
            String signParam = appId + body + shop.getAppSecret();

            // 使用HMAC-SHA256算法生成签名
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(shop.getAppSecret().getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
            byte[] signBytes = mac.doFinal(signParam.getBytes(StandardCharsets.UTF_8));

            // 转换为十六进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : signBytes) {
                sb.append(String.format("%02x", b));
            }
            String calculatedSign = sb.toString();

            boolean isValid = eventSign.equals(calculatedSign);
            if (!isValid) {
                log.warn("签名验证失败: 请求签名={}, 计算签名={}", eventSign, calculatedSign);
            }

            return isValid;

        } catch (Exception e) {
            log.error("验证签名异常", e);
            return false;
        }
    }

    /**
     * 处理买家收货信息变更申请消息
     */
    private void handleAddressChangeApplied(String messageData) {
        log.info("处理买家收货信息变更申请消息: {}", messageData);

        try {
            // 直接解析JSON消息
            JsonObject data = GsonUtil.jsonToBean(messageData, JsonObject.class);
            String pId = data.get("p_id").getAsString();
            Long shopId = data.get("shop_id").getAsLong();

            // 获取店铺信息
            ShopDO shop = shopGetQryExe.getByShopId(shopId.toString());
            if (shop == null) {
                log.error("店铺不存在: {}", shopId);
                return;
            }

            // 实现审核逻辑
            // 1. 当订单已经开始拣货时，自动拒绝；
            // 2. 当订单进入出库环节时，自动拒绝；
            // 3. 非以上环节自动通过
            int auditResult = determineAuditResult(pId);

            // 调用addressConfirm接口进行审核
            callAddressConfirm(shop, pId, auditResult);

        } catch (Exception e) {
            log.error("处理买家收货信息变更申请消息异常", e);
        }
    }

    /**
     * 处理买家收货信息变更成功消息
     */
    private void handleAddressChanged(String messageData) {
        log.info("处理买家收货信息变更成功消息: {}", messageData);

        try {
            // 直接解析JSON消息
            JsonObject data = GsonUtil.jsonToBean(messageData, JsonObject.class);
            String pId = data.get("p_id").getAsString();
            JsonObject receiverMsg = data.getAsJsonObject("receiver_msg");

            // 更新本地订单地址信息
            updateLocalOrderAddress(pId, receiverMsg);

        } catch (Exception e) {
            log.error("处理买家收货信息变更成功消息异常", e);
        }
    }

    /**
     * 确定审核结果
     */
    private int determineAuditResult(String orderId) {
        // TODO: 根据订单状态确定审核结果
        // 这里需要查询订单状态，根据业务规则返回审核结果
        // 0: 确认地址变更申请
        // 1001: 订单已进入拣货环节
        // 1002: 订单已进入配货环节  
        // 1003: 订单已进入仓库环节
        // 1004: 订单已进入出库环节
        // 1005: 订单已进入发货环节

        // 默认通过（实际需要根据订单状态判断）
        return 0;
    }

    /**
     * 调用addressConfirm接口审核
     */
    private void callAddressConfirm(ShopDO shop, String orderId, int result) {
        try {
            boolean success = tikTokRpc.addressConfirm(shop, orderId, result);
            if (success) {
                log.info("地址变更审核成功: 订单={}, 结果={}", orderId, result);
            } else {
                log.warn("地址变更审核失败: 订单={}, 结果={}", orderId, result);
            }
        } catch (Exception e) {
            log.error("调用addressConfirm接口异常: 订单={}, 结果={}", orderId, result, e);
        }
    }

    /**
     * 更新本地订单地址
     */
    private void updateLocalOrderAddress(String orderId, Object receiverMsg) {
        try {
            // TODO: 更新本地系统的订单收货地址
            // 这里可以调用OMS系统接口更新订单地址信息
            log.info("更新订单 {} 收货地址: {}", orderId, GsonUtil.objectToJson(receiverMsg));
        } catch (Exception e) {
            log.error("更新本地订单地址异常", e);
        }
    }

    /**
     * 构建成功响应
     */
    private String buildSuccessResponse() {
        return buildResponse(0, "success");
    }

    /**
     * 构建响应
     */
    private String buildResponse(int code, String message) {
        JsonObject result = new JsonObject();
        result.addProperty("code", code);
        result.addProperty("msg", message);
        return result.toString();
    }
}