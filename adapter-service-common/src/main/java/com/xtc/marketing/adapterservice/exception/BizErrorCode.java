package com.xtc.marketing.adapterservice.exception;

/**
 * 错误码主要有3部分组成：类型+场景+自定义标识
 */
public enum BizErrorCode {
//    // 参数异常
//    P_USER_UserIdNotNull("P_USER_UserIdNotNull", "用户id不能为空"),

    // 业务异常 - 商户
    B_MCH_DataIncorrect("B_DIVIDEND_MchIncorrect", "获取商户异常"),

    // 业务异常 - 订单
    B_ORDER_OrderStatusNoAllowPushShippingStatus("B_ORDER_OrderStatusNoAllowPushShippingStatus",
            "平台的订单状态不符合推送发货状态的条件"),
    B_ORDER_LogisticsOrderPackageNotReachable("B_ORDER_LogisticsOrderPackageNotReachable",
            "物流公司网点停运"),
    B_ORDER_OrderStatusNoAllowCreateLogisticsOrder("B_ORDER_OrderStatusNoAllowCreateLogisticsOrder",
            "平台的订单状态不允许生成电子面单"),

    // 业务异常 - 通知
    B_NOTIFY_ReceiveFailed("B_NOTIFY_ReceiveFailed", "通知接收失败"),
    ;

    private final String errCode;
    private final String errDesc;

    BizErrorCode(String errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }

    public String getErrCode() {
        return errCode;
    }

    public String getErrDesc() {
        return errDesc;
    }
}
