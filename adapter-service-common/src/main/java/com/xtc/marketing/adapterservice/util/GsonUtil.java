package com.xtc.marketing.adapterservice.util;

import com.google.gson.*;
import com.google.gson.annotations.Expose;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.text.DateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;

/**
 * gson工具类
 */
public class GsonUtil {

    private GsonUtil() {
    }

    private static final String FORMAT_DATE = "yyyy-MM-dd";
    private static final String FORMAT_DATE_TIME = "yyyy-MM-dd HH:mm:ss";

    private static final Gson GSON = new GsonBuilder()
            // 序列化
            .registerTypeAdapter(LocalDateTime.class, (JsonSerializer<LocalDateTime>) (localDateTime, type, serializationContext) ->
                    new JsonPrimitive(localDateTime.format(DateTimeFormatter.ofPattern(FORMAT_DATE_TIME))))
            .registerTypeAdapter(LocalDate.class, (JsonSerializer<LocalDate>) (localDate, type, serializationContext) ->
                    new JsonPrimitive(localDate.format(DateTimeFormatter.ofPattern(FORMAT_DATE))))
            .registerTypeAdapter(DateFormat.class, (JsonSerializer<DateFormat>) (dateFormat, type, serializationContext) -> null)
            // 反序化
            .registerTypeAdapter(LocalDateTime.class, (JsonDeserializer<LocalDateTime>) (jsonElement, type, deserializationContext) ->
                    LocalDateTime.parse(jsonElement.getAsJsonPrimitive().getAsString(), DateTimeFormatter.ofPattern(FORMAT_DATE_TIME)))
            .registerTypeAdapter(LocalDate.class, (JsonDeserializer<LocalDate>) (jsonElement, type, deserializationContext) ->
                    LocalDate.parse(jsonElement.getAsJsonPrimitive().getAsString(), DateTimeFormatter.ofPattern(FORMAT_DATE)))
            .registerTypeAdapter(DateFormat.class, (JsonDeserializer<DateFormat>) (jsonElement, type, deserializationContext) -> null)
            // 序列化数值类型时，整数不带小数点
            .registerTypeAdapter(Integer.class, numberSerializer())
            .registerTypeAdapter(Long.class, numberSerializer())
            .registerTypeAdapter(Double.class, numberSerializer())
            // 排除 @Expose 注解的字段：序列化，反序列化
            .addSerializationExclusionStrategy(new ExposeExclusionStrategy())
            .addDeserializationExclusionStrategy(new ExposeExclusionStrategy())
            .serializeNulls()
            .disableHtmlEscaping()
            .create();

    /**
     * 对象转json字符串
     *
     * @param object 对象
     * @return json字符串
     */
    public static String objectToJson(Object object) {
        return GSON.toJson(object);
    }

    /**
     * 对象转json字符串，根据key顺序排序
     *
     * @param object 对象
     * @return json字符串
     */
    public static String objectToJsonSortedByKey(Object object) {
        String json = objectToJson(object);
        // 转成SortedMap会根据key顺序排序
        SortedMap<String, Object> sortedObject = jsonToSortedMap(json);
        return objectToJson(sortedObject);
    }

    /**
     * json字符串转JsonObject对象
     *
     * @param json json字符串
     * @return JsonObject对象
     */
    public static JsonObject jsonToObject(String json) {
        return GSON.fromJson(json, JsonObject.class);
    }

    /**
     * json转对象
     *
     * @param json json字符串
     * @param type 类型
     * @return T对象
     */
    public static <T> T jsonToBean(String json, Type type) {
        return GSON.fromJson(json, type);
    }

    /**
     * json转对象
     *
     * @param json     json字符串
     * @param rawType  最外层类型
     * @param argTypes 多个泛型类型
     * @return T对象
     */
    public static <T> T jsonToBean(String json, Type rawType, Type... argTypes) {
        Type type = buildType(rawType, argTypes);
        return GSON.fromJson(json, type);
    }

    /**
     * json转Map<String, Object>
     *
     * @param json json字符串
     * @return Map<String, Object>对象
     */
    public static Map<String, Object> jsonToMap(String json) {
        return jsonToBean(json, Map.class, String.class, Object.class);
    }

    /**
     * json转Map<String, T>
     *
     * @param json json字符串
     * @return Map<String, T>对象
     */
    public static <T> Map<String, T> jsonToMap(String json, Class<T> clazz) {
        return jsonToBean(json, Map.class, String.class, clazz);
    }

    /**
     * json转SortedMap<String, Object>
     *
     * @param json json字符串
     * @return SortedMap<String, Object>对象
     */
    public static SortedMap<String, Object> jsonToSortedMap(String json) {
        return jsonToBean(json, SortedMap.class, String.class, Object.class);
    }

    /**
     * json转Map<String, String>
     *
     * @param json json字符串
     * @return Map<String, String>对象
     */
    public static Map<String, String> jsonToMapString(String json) {
        return jsonToBean(json, Map.class, String.class, String.class);
    }

    /**
     * json转List<Map<String, Object>>
     *
     * @param json json字符串
     * @return List<Map < String, Object>>对象
     */
    public static List<Map<String, Object>> jsonToListMap(String json) {
        Type mapType = buildType(Map.class, String.class, Object.class);
        return jsonToBean(json, List.class, mapType);
    }

    /**
     * json转List
     *
     * @param json json字符串
     * @return List<Object>>对象
     */
    public static List<Object> jsonToList(String json) {
        return jsonToBean(json, List.class, Object.class);
    }

    /**
     * json转List<clazz>
     *
     * @param json json字符串
     * @return List<T>>对象
     */
    public static <T> List<T> jsonToList(String json, Class<T> clazz) {
        Type type = buildType(List.class, clazz);
        return jsonToBean(json, type);
    }

    /**
     * 生成类型
     * <p>Map&lt;String, Integer&gt; 对应 buildType(Map.class, String.class, Integer.class)</p>
     *
     * @param raw  最外层类型
     * @param args 多个泛型类型
     * @return 类型
     */
    public static Type buildType(Type raw, Type... args) {
        return new ParameterizedTypeImpl(raw, args);
    }

    /**
     * 实现ParameterizedType接口，封装 Type 泛型生成
     * <br><br>
     * 如果Map&lt;String, Integer&gt;通过ParameterizedType来解析
     * getRawType()方法返回的值是：Map.class
     * getActualTypeArguments()方法返回的值是：[ String.class, Integer.class ]
     * new ParameterizedTypeImpl(Map.class, String.class, Integer.class)
     * 该类的实现规则是：getRawType&lt;getActualTypeArguments&gt;
     */
    private static class ParameterizedTypeImpl implements ParameterizedType {

        private final Type raw;
        private final Type[] args;

        public ParameterizedTypeImpl(Type raw, Type... args) {
            this.raw = raw;
            this.args = args;
        }

        @Override
        public Type[] getActualTypeArguments() {
            return args;
        }

        @Override
        public Type getRawType() {
            return raw;
        }

        @Override
        public Type getOwnerType() {
            return null;
        }

    }

    /**
     * 序列化数值类型时，整数不带小数点
     *
     * @param <T> 数值类型
     * @return JsonSerializer
     */
    private static <T extends Number> JsonSerializer<T> numberSerializer() {
        return (number, type, context) -> {
            if (number instanceof Integer) {
                return new JsonPrimitive(number.intValue());
            }
            if (number instanceof Long) {
                return new JsonPrimitive(number.longValue());
            }
            if (number instanceof Double) {
                // 整数部分和原值（包含小数）相等时，返回整数部分，不包含小数
                long longValue = number.longValue();
                double doubleValue = number.doubleValue();
                if (longValue == doubleValue) {
                    return new JsonPrimitive(longValue);
                }
            }
            return new JsonPrimitive(number);
        };
    }

    /**
     * 排除 @Expose 注解的字段
     */
    private static class ExposeExclusionStrategy implements ExclusionStrategy {

        @Override
        public boolean shouldSkipField(FieldAttributes fieldAttributes) {
            Expose expose = fieldAttributes.getAnnotation(Expose.class);
            return expose != null;
        }

        @Override
        public boolean shouldSkipClass(Class<?> aClass) {
            return false;
        }

    }

    /**
     * 获取json字符串的字符串值
     *
     * @param json json字符串
     * @param key  键值
     * @return 键值对应的字符串值
     */
    public static String getAsString(String json, String... key) {
        try {
            JsonElement jsonElement = jsonToBean(json, JsonElement.class);
            JsonObject jsonObject = jsonElement.isJsonArray() ?
                    jsonElement.getAsJsonArray().get(0).getAsJsonObject() : jsonElement.getAsJsonObject();
            return getAsString(jsonObject, key);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 获取JsonObject的字符串
     *
     * @param jsonObject JsonObject对象
     * @param key        键值
     * @return 键值对应的字符串值
     */
    public static String getAsString(JsonObject jsonObject, String... key) {
        String empty = "";
        if (jsonObject == null) {
            return empty;
        }

        JsonObject obj = jsonObject;
        for (String s : key) {
            JsonElement element = obj.get(s);
            if (element == null) {
                return empty;
            }
            if (element.isJsonNull()) {
                return empty;
            }
            if (element.isJsonPrimitive()) {
                return element.getAsString();
            }
            obj = element.getAsJsonObject();
        }
        return empty;
    }

    /**
     * 获取json字符串的bool值
     *
     * @param json json字符串
     * @param key  键值
     * @return 键值对应的bool值
     */
    public static boolean getAsBoolean(String json, String... key) {
        try {
            JsonObject jsonObject = jsonToBean(json, JsonObject.class);
            return getAsBoolean(jsonObject, key);
        } catch (Exception e) {
            // e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取JsonObject的bool值
     *
     * @param jsonObject JsonObject对象
     * @param key        键值
     * @return 键值对应的bool值
     */
    public static boolean getAsBoolean(JsonObject jsonObject, String... key) {
        if (jsonObject == null) {
            throw new IllegalArgumentException("jsonObject is null");
        }

        JsonObject obj = jsonObject;
        for (String s : key) {
            JsonElement element = obj.get(s);
            if (element == null) {
                throw new IllegalArgumentException("jsonObject is null");
            }
            if (element.isJsonPrimitive()) {
                return element.getAsJsonPrimitive().isBoolean() && element.getAsBoolean();
            }
            obj = element.getAsJsonObject();
        }
        throw new IllegalArgumentException("jsonObject is null");
    }

    /**
     * 获取json字符串的数字值
     *
     * @param json json字符串
     * @param key  键值
     * @return 键值对应的数字值
     */
    public static Integer getAsInt(String json, String... key) {
        try {
            JsonElement jsonElement = jsonToBean(json, JsonElement.class);
            JsonObject jsonObject = jsonElement.isJsonArray() ?
                    jsonElement.getAsJsonArray().get(0).getAsJsonObject() : jsonElement.getAsJsonObject();
            return getAsInt(jsonObject, key);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取JsonObject的数字值
     *
     * @param jsonObject JsonObject对象
     * @param key        键值
     * @return 键值对应的数字值
     */
    public static Integer getAsInt(JsonObject jsonObject, String... key) {
        if (jsonObject == null) {
            return null;
        }

        JsonObject obj = jsonObject;
        for (String s : key) {
            JsonElement element = obj.get(s);
            if (element == null) {
                return null;
            }
            if (element.isJsonNull()) {
                return null;
            }
            if (element.isJsonPrimitive()) {
                return element.getAsJsonPrimitive().isNumber() ? element.getAsInt() : null;
            }
            obj = element.getAsJsonObject();
        }
        return null;
    }

}
