package com.xtc.marketing.adapterservice.util;

import java.math.BigDecimal;

/**
 * 金钱工具类
 * <p>禁止使用构造方法 BigDecimal(double) 的方式直接把 double 值转为 BigDecimal 对象
 */
public class MoneyUtil {

    private MoneyUtil() {
    }

    /**
     * 保留 2 位小数
     */
    private static final int SCALE_TWO = 2;

    /**
     * 分转元
     *
     * @param cent 分
     * @return 元
     */
    public static String centToYuan(int cent) {
        BigDecimal yuan = new BigDecimal(cent).movePointLeft(SCALE_TWO);
        return yuan.toPlainString();
    }

    /**
     * 分转元
     *
     * @param cent 分
     * @return 元
     */
    public static String centToYuan(long cent) {
        BigDecimal yuan = new BigDecimal(cent).movePointLeft(SCALE_TWO);
        return yuan.toPlainString();
    }

    /**
     * 分转元
     *
     * @param cent 分
     * @return 元
     */
    public static String centToYuan(String cent) {
        BigDecimal yuan = new BigDecimal(cent).movePointRight(SCALE_TWO);
        return yuan.toPlainString();
    }

    /**
     * 元转分
     *
     * @param yuan 元
     * @return 分
     */
    public static Integer yuanToCent(String yuan) {
        BigDecimal cent = new BigDecimal(yuan).movePointRight(SCALE_TWO);
        return cent.intValue();
    }

    /**
     * 元转分 Double 转 Integer
     */
    public static Integer yuanToCent(Double yuan) {
        if (yuan == null) {
            return null;
        }
        return yuanToCent(yuan.toString());
    }

    /**
     * 元转分 BigDecimal 转 Integer
     */
    public static Integer yuanToCent(BigDecimal yuan) {
        if (yuan == null) {
            return null;
        }
        BigDecimal cent = yuan.movePointRight(SCALE_TWO);
        return cent.intValue();
    }

}
