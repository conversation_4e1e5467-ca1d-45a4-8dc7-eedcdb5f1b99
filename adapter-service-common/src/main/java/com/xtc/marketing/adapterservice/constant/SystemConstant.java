package com.xtc.marketing.adapterservice.constant;

/**
 * 系统常量
 */
public class SystemConstant {

    private SystemConstant() {
    }

    /**
     * 系统名称
     */
    public static final String SYSTEM_NAME = "adapter-service";

    /**
     * 测试环境配置
     */
    public static final String PROFILE_TEST = "dev|test";

    /**
     * 业务代码
     */
    public static final String BIZ_CODE = "";

    /**
     * 业务名称
     */
    public static final String BIZ_NAME = "";

    /**
     * 判断测试环境
     *
     * @param profileActive 激活的配置
     * @return 执行结果
     */
    public static boolean isTestProfile(String profileActive) {
        if (profileActive == null) {
            return true;
        }
        return SystemConstant.PROFILE_TEST.contains(profileActive);
    }

}
