package com.xtc.marketing.adapterservice.api;

import com.xtc.marketing.adapterservice.notify.NotifyAdminService;
import com.xtc.marketing.adapterservice.notify.dto.PushLogDTO;
import com.xtc.marketing.adapterservice.notify.dto.ReceiveLogDTO;
import com.xtc.marketing.adapterservice.notify.dto.query.PushLogPageQry;
import com.xtc.marketing.adapterservice.notify.dto.query.ReceiveLogPageQry;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.marketing.marketingcomponentdto.dto.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * 通知管理接口
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class NotifyAdminController {

    private final NotifyAdminService notifyAdminService;

    /**
     * 推送记录分页列表
     *
     * @param qry 参数
     * @return 推送记录分页列表
     */
    @GetMapping("/notify-admin/push-logs")
    public PageResponse<PushLogDTO> pagePushLogs(@Valid PushLogPageQry qry) {
        return notifyAdminService.pagePushLogs(qry);
    }

    /**
     * 接收记录分页列表
     *
     * @param qry 参数
     * @return 接收记录分页列表
     */
    @GetMapping("/notify-admin/receive-logs")
    public PageResponse<ReceiveLogDTO> pageReceiveLogs(@Valid ReceiveLogPageQry qry) {
        return notifyAdminService.pageReceiveLogs(qry);
    }

    /**
     * 删除接收记录
     *
     * @param id 接收记录id
     * @return 执行结果
     */
    @DeleteMapping("/notify-admin/receive-logs/{id}")
    public Response removeReceiveLog(@NotNull @Positive @PathVariable Long id) {
        notifyAdminService.removeReceiveLog(id);
        return Response.buildSuccess();
    }

}
