package com.xtc.marketing.adapterservice.api;

import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.notify.NotifyService;
import com.xtc.marketing.adapterservice.notify.dto.command.NotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.dto.command.WechatChannelsShopNotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.enums.NotifyEnum;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.notify.WechatChannelsShopNotify;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.hibernate.validator.constraints.Length;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 通知接收接口
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api")
public class NotifyController {

    private final NotifyService notifyService;

    /**
     * 接收通知 - 顺丰入库
     *
     * @param data 数据
     * @return 响应结果
     */
    @PostMapping("/notify-receive/inbound-notify-sf")
    public ResponseEntity<String> inboundNotifySf(@NotBlank @RequestParam("logistics_interface") String data) {
        NotifyReceiveCmd cmd = NotifyReceiveCmd.builder().data(data).build();
        return notifyService.receiveData(NotifyEnum.SF_WAREHOUSE_INBOUND, cmd);
    }

    /**
     * 接收通知 - 顺丰出库
     *
     * @param data 数据
     * @return 响应结果
     */
    @PostMapping("/notify-receive/outbound-notify-sf")
    public ResponseEntity<String> outboundNotifySf(@NotBlank @RequestParam("logistics_interface") String data) {
        NotifyReceiveCmd cmd = NotifyReceiveCmd.builder().data(data).build();
        return notifyService.receiveData(NotifyEnum.SF_WAREHOUSE_OUTBOUND, cmd);
    }

    /**
     * 接收通知 - 顺丰物流国补通知
     *
     * @param requestId   请求唯一号UUID
     * @param partnerId   合作伙伴编码（即顾客编码）
     * @param serviceCode 接口服务代码
     * @param timestamp   调用接口时间戳
     * @param msgDigest   数字签名,使用数字签名方式认证时必填，不可与accessToken字段同时传参
     * @param msgData     业务数据报文
     * @param nonce       随机数
     * @return 响应结果
     */
    @PostMapping("/notify-receive/national-subsidy-sf")
    public ResponseEntity<String> nationalSubsidySf(@RequestParam("requestID") String requestId,
                                                    @RequestParam("partnerID") String partnerId,
                                                    @RequestParam String serviceCode,
                                                    @RequestParam String timestamp,
                                                    @RequestParam String msgDigest,
                                                    @RequestParam String msgData,
                                                    @RequestParam String nonce) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("requestID", requestId);
        jsonObject.addProperty("partnerID", partnerId);
        jsonObject.addProperty("serviceCode", serviceCode);
        jsonObject.addProperty("timestamp", timestamp);
        jsonObject.addProperty("msgDigest", msgDigest);
        jsonObject.addProperty("msgData", msgData);
        jsonObject.addProperty("nonce", nonce);
        NotifyReceiveCmd cmd = NotifyReceiveCmd.builder().data(jsonObject.toString()).build();
        return notifyService.receiveData(NotifyEnum.SF_LOGISTICS_NATIONAL_SUBSIDY, cmd);
    }

    /**
     * 微信视频号小店，回调 URL 验证
     *
     * @param toUserName 小店UserName（账号信息-原始ID）
     * @param timestamp  时间戳（秒）
     * @param nonce      随机数
     * @param signature  url签名
     * @param echostr    验证字符串
     * @return 验证字符串
     */
    @GetMapping("/notify-receive/wechat-channels-shop")
    public ResponseEntity<String> verifyWechatChannelsShop(@NotBlank @Length(max = 50) String toUserName,
                                                           @NotBlank @Length(max = 50) String timestamp,
                                                           @NotBlank @Length(max = 50) String nonce,
                                                           @NotBlank @Length(max = 50) String signature,
                                                           @NotBlank @Length(max = 50) String echostr) {
        WechatChannelsShopNotify wechatChannelsShopNotify = new WechatChannelsShopNotify(toUserName);
        boolean verifyNotifyServer = wechatChannelsShopNotify.verifyNotifyServer(timestamp, nonce, signature);
        if (BooleanUtils.isTrue(verifyNotifyServer)) {
            // 验签通过返回验证字符串
            return ResponseEntity.ok(echostr);
        }
        return ResponseEntity.internalServerError().body("验签失败");
    }

    /**
     * 接收通知 - 微信视频号小店
     *
     * @param toUserName   小店UserName（账号信息-原始ID）
     * @param timestamp    时间戳（秒）
     * @param nonce        随机数
     * @param signature    url签名
     * @param msgSignature 消息签名
     * @param body         消息内容
     * @return 响应结果
     */
    @PostMapping("/notify-receive/wechat-channels-shop")
    public ResponseEntity<String> receiveWechatChannelsShop(@NotBlank @Length(max = 50) @RequestParam String toUserName,
                                                            @NotBlank @Length(max = 50) @RequestParam String timestamp,
                                                            @NotBlank @Length(max = 50) @RequestParam String nonce,
                                                            @NotBlank @Length(max = 50) @RequestParam String signature,
                                                            @NotBlank @Length(max = 50) @RequestParam("msg_signature") String msgSignature,
                                                            @Valid @RequestBody WechatChannelsShopNotifyReceiveCmd body) {
        JsonObject data = new JsonObject();
        data.addProperty("toUserName", toUserName);
        data.addProperty("timestamp", timestamp);
        data.addProperty("nonce", nonce);
        data.addProperty("signature", signature);
        data.addProperty("msgSignature", msgSignature);
        data.addProperty("body", GsonUtil.objectToJson(body));
        NotifyReceiveCmd cmd = NotifyReceiveCmd.builder().data(data.toString()).build();
        return notifyService.receiveData(NotifyEnum.WECHAT_CHANNELS_SHOP_NOTIFY, cmd);
    }

    /**
     * 接收通知 - 天猫修改地址
     *
     * @param request  请求
     * @param shopCode 店铺代码
     * @return 响应结果
     */
    @PostMapping("/notify-receive/modify-address-tmall")
    public ResponseEntity<String> modifyAddressTmall(HttpServletRequest request,
                                                     @NotBlank @Length(max = 50) @RequestParam String shopCode) {
        NotifyReceiveCmd cmd = NotifyReceiveCmd.builder().shopCode(shopCode).request(request).build();
        return notifyService.receiveData(NotifyEnum.TMALL_SHOP_MODIFY_ADDRESS, cmd);
    }

    /**
     * 接收通知 - 抖音修改地址
     *
     * @param request 请求
     * @return 响应结果
     */
    @PostMapping("/notify-receive/modify-address-tiktok")
    public ResponseEntity<String> modifyAddressTiktok(HttpServletRequest request) {
        NotifyReceiveCmd cmd = NotifyReceiveCmd.builder().request(request).build();
        return notifyService.receiveData(NotifyEnum.TIKTOK_SHOP_MODIFY_ADDRESS, cmd);
    }

    /**
     * 接收通知 - 快手修改地址
     *
     * @param encryptedBody 加密的请求体
     * @return 响应结果
     */
    @PostMapping("/notify-receive/modify-address-kuaishou")
    public ResponseEntity<String> modifyAddressKuaishou(@Length(max = 5000) @RequestBody String encryptedBody) {
        NotifyReceiveCmd cmd = NotifyReceiveCmd.builder().data(encryptedBody).build();
        return notifyService.receiveData(NotifyEnum.KUAISHOU_SHOP_MODIFY_ADDRESS, cmd);
    }

    /**
     * 接收通知 - 小红书修改地址
     *
     * @param request 请求
     * @param body    消息内容
     * @return 响应结果
     */
    @PostMapping("/notify-receive/modify-address-xiaohongshu")
    public ResponseEntity<String> modifyAddressXiaohongshu(HttpServletRequest request,
                                                           @Length(max = 5000) @RequestBody String body) {
        NotifyReceiveCmd cmd = NotifyReceiveCmd.builder().request(request).data(body).build();
        return notifyService.receiveData(NotifyEnum.XIAOHONGSHU_SHOP_MODIFY_ADDRESS, cmd);
    }

    /**
     * 接收通知 - 抖音消息
     *
     * @param request 请求
     * @param body    消息内容
     * @return 响应结果
     */
    @PostMapping("/notify-receive/tiktok-message")
    public ResponseEntity<String> receiveTiktokMessage(HttpServletRequest request,
                                                       @Length(max = 10000) @RequestBody String body) {
        NotifyReceiveCmd cmd = NotifyReceiveCmd.builder().request(request).data(body).build();
        return notifyService.receiveData(NotifyEnum.TIKTOK_SHOP_MESSAGE, cmd);
    }

}
