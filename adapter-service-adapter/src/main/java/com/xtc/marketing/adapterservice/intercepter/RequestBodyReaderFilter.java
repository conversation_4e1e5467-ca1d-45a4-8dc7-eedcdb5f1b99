package com.xtc.marketing.adapterservice.intercepter;

import com.xtc.marketing.adapterservice.config.RequestBodyReaderWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * 请求体读取过滤器
 */
@Order(1)
@Component
public class RequestBodyReaderFilter implements Filter {

    /**
     * 白名单路径：通知接收接口
     */
    private static final String[] PATH_WHITELIST = {"/api/notify-receive/"};

    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                         FilterChain filterChain) throws IOException, ServletException {
        // 过滤无需处理的请求
        if (isWhitelist(request)) {
            filterChain.doFilter(request, response);
        } else {
            // 读取body里的参数
            ServletRequest requestBodyReaderWrapper = new RequestBodyReaderWrapper((HttpServletRequest) request);
            filterChain.doFilter(requestBodyReaderWrapper, response);
        }
    }

    /**
     * 白名单路径
     *
     * @param request 请求
     * @return 执行结果
     */
    private boolean isWhitelist(ServletRequest request) {
        // 排除文件上传类型
        String contentType = request.getContentType();
        if (StringUtils.isNotBlank(contentType) && contentType.contains(MediaType.MULTIPART_FORM_DATA_VALUE)) {
            return true;
        }

        // 排除指定路径
        String requestPath = ((HttpServletRequest) request).getServletPath();
        for (String path : PATH_WHITELIST) {
            if (requestPath.contains(path)) {
                return true;
            }
        }
        return false;
    }

}
