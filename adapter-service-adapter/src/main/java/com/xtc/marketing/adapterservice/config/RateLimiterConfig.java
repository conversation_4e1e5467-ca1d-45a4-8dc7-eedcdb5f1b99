package com.xtc.marketing.adapterservice.config;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.*;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.Optional;

/**
 * 流量限流器配置
 */
@Getter
@Setter
@ToString
@NacosConfigurationProperties(dataId = "rate-limiter.yaml", groupId = "adapter-service", prefix = "xtc.rate-limiter", autoRefreshed = true)
@Configuration
public class RateLimiterConfig {

    /**
     * 启用状态
     */
    private Boolean enabled = false;

    /**
     * 默认限流配置：总限制计数=100000，警告计数=20000
     */
    private LimitConfig defaultLimit = new LimitConfig(true, 100000, 20000);

    /**
     * 限流配置：key=配置id，value=限流配置
     */
    private Map<String, LimitConfig> limitConfigs;

    /**
     * 获取限流配置，不存在则返回默认配置
     *
     * @param limitId 限流配置id
     * @return 限流配置
     */
    public LimitConfig getLimitConfig(String limitId) {
        if (!this.enabled) {
            return null;
        }
        return Optional.ofNullable(limitId).map(limitConfigs::get).filter(LimitConfig::getEnabled).orElse(defaultLimit);
    }

    @Getter
    @Setter
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LimitConfig {

        /**
         * 启用状态
         */
        private Boolean enabled = false;

        /**
         * 总限制计数
         */
        private Integer limitCount = 100000;

        /**
         * 警告计数
         */
        private Integer warnCount = 20000;

    }

}
