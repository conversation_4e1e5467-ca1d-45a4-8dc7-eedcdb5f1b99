package com.xtc.marketing.adapterservice.api;

import com.xtc.marketing.adapterservice.warehouse.WarehouseService;
import com.xtc.marketing.adapterservice.warehouse.dto.OutboundDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.OutboundDetailDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.WarehouseStockDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.command.InboundApplyCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.InboundCancelCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.OutboundApplyCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.OutboundCancelCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.query.OutboundDetailQry;
import com.xtc.marketing.adapterservice.warehouse.dto.query.OutboundPageQry;
import com.xtc.marketing.adapterservice.warehouse.dto.query.WarehouseStockQry;
import com.xtc.marketing.marketingcomponentdto.dto.MultiResponse;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 仓库接口
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class WarehouseController {

    private final WarehouseService warehouseService;

    /**
     * 分页查询出库单列表
     *
     * @param qry 参数
     * @return 出库单分页列表
     */
    @GetMapping("/warehouse/outbound-page")
    public PageResponse<OutboundDTO> pageOutbound(@Valid OutboundPageQry qry) {
        return warehouseService.pageOutbound(qry);
    }

    /**
     * 查询出库单详情
     *
     * @param qry 参数
     * @return 出库单详情
     */
    @GetMapping("/warehouse/outbound-detail")
    public SingleResponse<OutboundDetailDTO> getOutboundDetail(@Valid OutboundDetailQry qry) {
        OutboundDetailDTO outboundDetails = warehouseService.getOutboundDetail(qry);
        return SingleResponse.of(outboundDetails);
    }

    /**
     * 查询库存
     *
     * @param qry 参数
     * @return 库存
     */
    @PostMapping("/warehouse/query-stocks")
    public MultiResponse<WarehouseStockDTO> queryStocks(@Valid @RequestBody WarehouseStockQry qry) {
        List<WarehouseStockDTO> stocks = warehouseService.queryStocks(qry);
        return MultiResponse.of(stocks);
    }

    /**
     * 申请入库
     *
     * @param cmd 参数
     * @return 平台入库单号
     */
    @PostMapping("/warehouse/apply-inbound")
    public SingleResponse<String> applyInbound(@Valid @RequestBody InboundApplyCmd cmd) {
        String platformOrderId = warehouseService.applyInbound(cmd);
        return SingleResponse.of(platformOrderId);
    }

    /**
     * 取消入库
     *
     * @param cmd 参数
     * @return 入库单号（业务方单号）
     */
    @PostMapping("/warehouse/cancel-inbound")
    public SingleResponse<String> cancelInbound(@Valid @RequestBody InboundCancelCmd cmd) {
        String orderId = warehouseService.cancelInbound(cmd);
        return SingleResponse.of(orderId);
    }

    /**
     * 申请出库
     *
     * @param cmd 参数
     * @return 平台出库单号
     */
    @PostMapping("/warehouse/apply-outbound")
    public SingleResponse<String> applyOutbound(@Valid @RequestBody OutboundApplyCmd cmd) {
        String platformOrderId = warehouseService.applyOutbound(cmd);
        return SingleResponse.of(platformOrderId);
    }

    /**
     * 取消出库
     *
     * @param cmd 参数
     * @return 出库单号（业务方单号）
     */
    @PostMapping("/warehouse/cancel-outbound")
    public SingleResponse<String> cancelOutbound(@Valid @RequestBody OutboundCancelCmd cmd) {
        String orderId = warehouseService.cancelOutbound(cmd);
        return SingleResponse.of(orderId);
    }

}
