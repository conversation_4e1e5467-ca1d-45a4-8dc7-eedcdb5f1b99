package com.xtc.marketing.adapterservice.api;

import com.xtc.marketing.adapterservice.logistics.LogisticsService;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsCloudPrintDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsOrderDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCancelOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCloudPrintCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsInterceptCmd;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsOrderGetQry;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsRouteListQry;
import com.xtc.marketing.marketingcomponentdto.dto.MultiResponse;
import com.xtc.marketing.marketingcomponentdto.dto.Response;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 物流接口
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class LogisticsController {

    private final LogisticsService logisticsService;

    /**
     * 查询快递路由
     *
     * @param qry 参数
     * @return 快递路由
     */
    @GetMapping("/logistics/routes")
    public MultiResponse<LogisticsRouteDTO> routes(@Valid LogisticsRouteListQry qry) {
        List<LogisticsRouteDTO> result = logisticsService.routes(qry);
        return MultiResponse.of(result);
    }

    /**
     * 下物流单并生成运单号
     *
     * @param cmd 参数
     * @return 订单详情
     * @apiNote 默认每次都使用新的【业务订单号】向物流公司【下单】生成新的【运单号】
     */
    @PostMapping("/logistics/create-order")
    public SingleResponse<LogisticsOrderDTO> createOrder(@Valid @RequestBody LogisticsCreateOrderCmd cmd) {
        LogisticsOrderDTO result = logisticsService.createOrder(cmd);
        return SingleResponse.of(result);
    }

    /**
     * 查询订单详情
     *
     * @param qry 参数
     * @return 执行结果
     */
    @GetMapping("/logistics/order")
    public SingleResponse<String> getOrder(@Valid LogisticsOrderGetQry qry) {
        String order = logisticsService.getOrder(qry);
        return SingleResponse.of(order);
    }

    /**
     * 查询运单号
     *
     * @param qry 参数
     * @return 运单号
     */
    @GetMapping("/logistics/waybill-no")
    public SingleResponse<String> getWaybillNo(@Valid LogisticsOrderGetQry qry) {
        String waybillNo = logisticsService.getWaybillNo(qry);
        return SingleResponse.of(waybillNo);
    }

    /**
     * 取消订单
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/logistics/cancel-order")
    public SingleResponse<Boolean> cancelOrder(@Valid @RequestBody LogisticsCancelOrderCmd cmd) {
        boolean result = logisticsService.cancelOrder(cmd);
        return SingleResponse.of(result);
    }

    /**
     * 拦截快递
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/logistics/intercept")
    public Response intercept(@Valid @RequestBody LogisticsInterceptCmd cmd) {
        logisticsService.intercept(cmd);
        return Response.buildSuccess();
    }

    /**
     * 面单云打印
     *
     * @param cmd 参数
     * @return 云打印数据
     */
    @PostMapping("/logistics/cloud-print")
    public SingleResponse<LogisticsCloudPrintDTO> cloudPrint(@Valid @RequestBody LogisticsCloudPrintCmd cmd) {
        LogisticsCloudPrintDTO dto = logisticsService.cloudPrint(cmd);
        return SingleResponse.of(dto);
    }

}
