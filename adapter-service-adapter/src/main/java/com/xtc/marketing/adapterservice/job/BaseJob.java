package com.xtc.marketing.adapterservice.job;

import com.xtc.marketing.adapterservice.intercepter.LogInterceptor;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.function.BiConsumer;
import java.util.function.UnaryOperator;

/**
 * 任务基类
 */
@Slf4j
public abstract class BaseJob {

    /**
     * 执行分片任务
     *
     * @param shardJob 分片任务（参数：分片索引，分片总数）
     */
    void executeShard(BiConsumer<Integer, Integer> shardJob) {
        if (XxlJobHelper.getShardTotal() == -1) {
            XxlJobHelper.handleFail("分片参数错误，请将任务配置为分片模式");
        }
        this.execute(() -> shardJob.accept(XxlJobHelper.getShardIndex(), XxlJobHelper.getShardTotal()));
    }

    /**
     * 执行任务
     *
     * @param job 任务（入参：任务参数；返回值：任务日志）
     */
    void executeWithJobParam(UnaryOperator<String> job) {
        execute(() -> {
            String jobParam = XxlJobHelper.getJobParam();
            this.log("job param: {}", jobParam);
            String result = job.apply(jobParam);
            this.log(result);
        });
    }

    /**
     * 执行任务
     *
     * @param job 任务
     */
    void execute(Runnable job) {
        // 生成日志id
        String traceId = LogInterceptor.logTraceId();
        this.log("put trace.id: {}", traceId);

        // 打印任务信息
        LocalDateTime startTime = LocalDateTime.now();
        String jobName = Arrays.stream(Thread.currentThread().getStackTrace(), 2, 4)
                .filter(stack -> stack.getClassName().equals(this.getClass().getName()))
                .findFirst()
                .map(StackTraceElement::getMethodName)
                .orElse("unknown job name");
        this.log("job start: {}", jobName);

        // 执行任务，捕获异常输出异常日志
        try {
            job.run();
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            this.log("job failed: {}", jobName, e);
            XxlJobHelper.handleFail();
            XxlJobHelper.log(e);
        } finally {
            // 计算任务耗时
            LocalDateTime endTime = LocalDateTime.now();
            this.log("job end: {}, cost: {} ms", jobName, Duration.between(startTime, endTime).toMillis());
        }
    }

    /**
     * 打印日志
     *
     * @param format 日志格式
     * @param args   日志参数
     */
    public void log(String format, Object... args) {
        log.info(format, args);
        XxlJobHelper.log(format, args);
    }

}
