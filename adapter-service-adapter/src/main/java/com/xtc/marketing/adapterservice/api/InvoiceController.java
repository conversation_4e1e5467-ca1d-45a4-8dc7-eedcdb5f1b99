package com.xtc.marketing.adapterservice.api;

import com.xtc.marketing.adapterservice.invoice.InvoiceService;
import com.xtc.marketing.adapterservice.invoice.dto.InvoiceResultDTO;
import com.xtc.marketing.adapterservice.invoice.dto.command.InvoiceCreateCmd;
import com.xtc.marketing.adapterservice.invoice.dto.command.InvoiceCreateRedCmd;
import com.xtc.marketing.adapterservice.invoice.dto.command.InvoiceSerialNoCmd;
import com.xtc.marketing.adapterservice.invoice.dto.query.InvoiceResultQry;
import com.xtc.marketing.marketingcomponentdto.dto.MultiResponse;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 发票接口
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class InvoiceController {

    private final InvoiceService invoiceService;

    /**
     * 开票
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/invoice/create-invoice")
    public boolean createInvoice(@RequestBody @Valid InvoiceCreateCmd cmd) {
        return invoiceService.createInvoice(cmd);
    }

    /**
     * 冲红
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/invoice/create-red-invoice")
    public boolean createRedInvoice(@RequestBody @Valid InvoiceCreateRedCmd cmd) {
        return invoiceService.createRedInvoice(cmd);
    }

    /**
     * 查询开票结果
     *
     * @param qry 参数
     * @return 开票结果
     */
    @GetMapping("/invoice/result")
    public MultiResponse<InvoiceResultDTO> listInvoiceResult(@Valid InvoiceResultQry qry) {
        List<InvoiceResultDTO> results = invoiceService.listInvoiceResult(qry);
        return MultiResponse.of(results);
    }

    /**
     * 生成开票流水号
     *
     * @param cmd 参数
     * @return 开票流水号
     */
    @GetMapping("/invoice/create-serial-no")
    public SingleResponse<String> createSerialNo(@Valid InvoiceSerialNoCmd cmd) {
        String serialNo = invoiceService.createSerialNo(cmd);
        return SingleResponse.of(serialNo);
    }

}
