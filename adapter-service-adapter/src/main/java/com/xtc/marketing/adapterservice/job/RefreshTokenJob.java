package com.xtc.marketing.adapterservice.job;

import com.xtc.marketing.adapterservice.shop.ShopService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 刷新店铺 token 数据定时任务
 * <p>只在正式环境运行</p>
 */
@RequiredArgsConstructor
@Component
public class RefreshTokenJob extends BaseJob {

    private final ShopService shopService;

    /**
     * 刷新店铺 token 数据
     */
    @XxlJob("refreshTokenJob")
    public void refreshTokenJob() {
        execute(shopService::refreshToken);
    }

}
