package com.xtc.marketing.adapterservice.api;

import com.xtc.marketing.adapterservice.annotation.RateLimiter;
import com.xtc.marketing.adapterservice.shop.ShopService;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.dto.query.*;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.marketing.marketingcomponentdto.dto.Response;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import lombok.RequiredArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 店铺接口
 */
@RateLimiter(limitParam = "shopCode")
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class ShopController {

    private final ShopService shopService;

    /**
     * 查询店铺 AccessToken
     *
     * @param shopCode 店铺代码
     * @return 店铺 AccessToken
     */
    @GetMapping("/shop/accessToken")
    public SingleResponse<ShopAccessTokenDTO> getAccessToken(@Length(max = 50) @NotBlank String shopCode) {
        ShopAccessTokenDTO accessToken = shopService.getAccessToken(shopCode);
        return SingleResponse.of(accessToken);
    }

    /**
     * 分页查询订单列表
     *
     * @param qry 参数
     * @return 订单分页列表
     */
    @GetMapping("/shop/orders")
    public PageResponse<OrderDTO> pageOrders(@Valid OrderPageQry qry) {
        return shopService.pageOrders(qry);
    }

    /**
     * 查询订单
     *
     * @param qry 参数
     * @return 订单
     */
    @GetMapping("/shop/order/detail")
    public SingleResponse<OrderDTO> getOrder(@Valid OrderGetQry qry) {
        OrderDTO result = shopService.getOrder(qry);
        return SingleResponse.of(result);
    }

    /**
     * 订单发货
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/shop/order/shipping")
    public SingleResponse<Boolean> shipping(@Valid @RequestBody OrderShippingCmd cmd) {
        boolean result = shopService.orderShipping(cmd);
        return SingleResponse.of(result);
    }

    /**
     * 订单取消发货
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/shop/order/shipping-cancel")
    public SingleResponse<Boolean> shippingCancel(@Valid @RequestBody OrderShippingCancelCmd cmd) {
        boolean result = shopService.orderShippingCancel(cmd);
        return SingleResponse.of(result);
    }

    /**
     * 订单无需物流发货
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/shop/order/dummy-shipping")
    public SingleResponse<Boolean> dummyShip(@Valid @RequestBody OrderDummyShippingCmd cmd) {
        boolean result = shopService.orderDummyShipping(cmd);
        return SingleResponse.of(result);
    }

    /**
     * 订单备注
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/shop/order/remark")
    public SingleResponse<Boolean> remark(@Valid @RequestBody OrderRemarkCmd cmd) {
        boolean result = shopService.orderRemark(cmd);
        return SingleResponse.of(result);
    }

    /**
     * 订单解密
     *
     * @param cmd 参数
     * @return 解密数据
     */
    @PostMapping("/shop/order/decrypt")
    public SingleResponse<OrderDecryptDTO> decrypt(@Valid @RequestBody OrderDecryptCmd cmd) {
        OrderDecryptDTO result = shopService.orderDecrypt(cmd);
        return SingleResponse.of(result);
    }

    /**
     * 分页查询发票申请列表
     *
     * @param qry 参数
     * @return 发票申请分页列表
     */
    @GetMapping("/shop/order/invoice-apply")
    public PageResponse<InvoiceApplyDTO> pageInvoiceApply(@Valid InvoiceApplyPageQry qry) {
        return shopService.pageInvoiceApply(qry);
    }

    /**
     * 查询发票申请
     *
     * @param qry 参数
     * @return 发票申请
     */
    @GetMapping("/shop/order/invoice-apply/detail")
    public SingleResponse<InvoiceApplyDTO> getInvoiceApply(@Valid InvoiceApplyGetQry qry) {
        InvoiceApplyDTO result = shopService.getInvoiceApply(qry);
        return SingleResponse.of(result);
    }

    /**
     * 上传发票 - 文件流
     *
     * @param cmd         参数
     * @param invoiceFile 发票文件
     * @return 执行结果
     */
    @PostMapping("/shop/order/uploadInvoice")
    public SingleResponse<Boolean> uploadInvoiceFile(@Valid OrderUploadInvoiceCmd cmd,
                                                     @RequestPart MultipartFile invoiceFile) {
        boolean result = shopService.uploadInvoiceFile(cmd, invoiceFile);
        return SingleResponse.of(result);
    }

    /**
     * 上传发票 - base64字符串
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/shop/order/invoice-upload")
    public SingleResponse<Boolean> uploadInvoiceBase64(@Valid @RequestBody InvoiceUploadCmd cmd) {
        boolean invoiceUpload = shopService.uploadInvoiceBase64(cmd);
        return SingleResponse.of(invoiceUpload);
    }

    /**
     * 查询发票开票金额
     *
     * @param qry 参数
     * @return 发票开票金额
     */
    @GetMapping("/shop/order/invoice-amount")
    public SingleResponse<InvoiceAmountDTO> getInvoiceAmount(@Valid InvoiceAmountGetQry qry) {
        InvoiceAmountDTO result = shopService.getInvoiceAmount(qry);
        return SingleResponse.of(result);
    }

    /**
     * 分页查询评价列表
     *
     * @param qry 参数
     * @return 评价分页列表
     */
    @GetMapping("/shop/comments")
    public PageResponse<CommentDTO> pageComments(@Valid CommentPageQry qry) {
        return shopService.pageComments(qry);
    }

    /**
     * 分页查询退款单列表
     *
     * @param qry 参数
     * @return 退款单分页列表
     */
    @GetMapping("/shop/refunds")
    public PageResponse<RefundDTO> pageRefunds(@Valid RefundPageQry qry) {
        return shopService.pageRefunds(qry);
    }

    /**
     * 查询退款单
     *
     * @param qry 参数
     * @return 退款单
     */
    @GetMapping("/shop/refund/detail")
    public SingleResponse<RefundDTO> getRefund(@Valid RefundGetQry qry) {
        RefundDTO result = shopService.getRefund(qry);
        return SingleResponse.of(result);
    }

    /**
     * 生成电子面单
     *
     * @param cmd 参数
     * @return 电子面单
     */
    @PostMapping("/shop/logistics/create-order")
    public SingleResponse<ShopLogisticsOrderDTO> createLogisticsOrder(@Valid @RequestBody ShopLogisticsOrderCreateCmd cmd) {
        ShopLogisticsOrderDTO logisticsOrder = shopService.createLogisticsOrder(cmd);
        return SingleResponse.of(logisticsOrder);
    }

    /**
     * 取消电子面单
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/shop/logistics/cancel-order")
    public SingleResponse<Boolean> cancelLogisticsOrder(@Valid @RequestBody ShopLogisticsOrderCancelCmd cmd) {
        boolean result = shopService.cancelLogisticsOrder(cmd);
        return SingleResponse.of(result);
    }

    /**
     * 退货确认入仓
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/shop/refund/goods-to-warehouse")
    public Response refundGoodsToWarehouse(@Valid @RequestBody RefundGoodsToWarehouseCmd cmd) {
        shopService.refundGoodsToWarehouse(cmd);
        return Response.buildSuccess();
    }

    /**
     * 条码上传
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/shop/barcode/upload")
    public Response barcodeUpload(@Valid @RequestBody BarcodeUploadCmd cmd) {
        shopService.barcodeUpload(cmd);
        return Response.buildSuccess();
    }

}
