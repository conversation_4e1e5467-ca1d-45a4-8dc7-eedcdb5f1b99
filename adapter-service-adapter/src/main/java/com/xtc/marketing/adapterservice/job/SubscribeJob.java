package com.xtc.marketing.adapterservice.job;

import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.subscribe.SubscribeService;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 消息订阅任务
 */
@RequiredArgsConstructor
@Component
public class SubscribeJob extends BaseJob {

    private final SubscribeService subscribeService;

    /**
     * 开启消息订阅
     */
    @XxlJob("subscribeConnectJob")
    public void subscribeConnectJob() {
        executeWithJobParam(param -> {
            JsonObject paramObj = GsonUtil.jsonToObject(param);
            String shopCode = GsonUtil.getAsString(paramObj, "shopCode");
            String bizParam = Optional.ofNullable(paramObj.get("bizParam"))
                    .filter(bizParamJson -> BooleanUtils.isFalse(bizParamJson.isJsonNull()))
                    .map(bizParamJson -> bizParamJson.isJsonPrimitive() ? bizParamJson.getAsString() : bizParamJson.toString())
                    .orElse(null);
            return subscribeService.connect(shopCode, bizParam);
        });
    }

    /**
     * 关闭消息订阅
     */
    @XxlJob("subscribeCloseJob")
    public void subscribeCloseJob() {
        executeWithJobParam(subscribeService::close);
    }

}
