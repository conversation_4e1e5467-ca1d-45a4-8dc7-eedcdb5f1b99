package com.xtc.marketing.adapterservice.job;

import com.xtc.marketing.adapterservice.notify.NotifyService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 通知数据推送任务
 */
@RequiredArgsConstructor
@Component
public class NotifyPushJob extends BaseJob {

    private final NotifyService notifyService;

    /**
     * 通知数据推送任务
     */
    @XxlJob("notifyPushJob")
    public void notifyPushJob() {
        executeShard((index, total) -> notifyService.notifyPush(index));
    }

}
