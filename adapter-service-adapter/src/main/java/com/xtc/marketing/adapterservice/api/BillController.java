package com.xtc.marketing.adapterservice.api;

import com.xtc.marketing.adapterservice.bill.BillService;
import com.xtc.marketing.adapterservice.bill.dto.query.BillDownloadQry;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 账单接口
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class BillController {

    private final BillService billService;

    /**
     * 下载账单文件
     *
     * @param qry 参数
     * @return 账单文件
     * @apiNote 只能获取单个日期的账单，多个日期账单需要自行遍历处理
     */
    @GetMapping("/bill/download")
    public ResponseEntity<Resource> billDownload(@Valid BillDownloadQry qry) {
        Resource bill = billService.billDownload(qry);
        String filename = qry.getBillPlatform() + "_" + qry.getBillDate().toString() + ".csv";
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_TYPE, "text/csv")
                .header(HttpHeaders.CONTENT_DISPOSITION, String.format("attachment; filename=%s", filename))
                .body(bill);
    }

}
