<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.xtc.marketing.adapterservice</groupId>
    <artifactId>adapter-service-parent</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>adapter-service</name>

    <properties>
        <!--System-->
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>${maven.compiler.source}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!--Misc-->
        <druid-starter.version>1.2.18</druid-starter.version>
        <mybatis-plus-starter.version>3.5.6</mybatis-plus-starter.version>
        <redisson-starter.version>3.23.2</redisson-starter.version>
        <lombok.version>1.18.34</lombok.version>
        <mapstruct.version>1.6.0.RC1</mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <gson.version>2.11.0</gson.version>
        <guava.version>33.2.1-jre</guava.version>
        <commons-lang3.version>3.16.0</commons-lang3.version>
        <commons-collections.version>4.5.0-M2</commons-collections.version>
        <commons-io.version>2.16.1</commons-io.version>
        <commons-codec.version>1.17.1</commons-codec.version>
        <arthas-starter.version>3.7.2</arthas-starter.version>
        <nacos-starter.version>0.2.12</nacos-starter.version>
        <ijapy.version>2.8.4</ijapy.version>
        <xxl-job.version>2.4.0</xxl-job.version>
        <easyexcel.version>3.3.4</easyexcel.version>
        <!--XTC Client-->
        <adapter-service-client.version>1.5.5</adapter-service-client.version>
        <order-service-client.version>1.3.4</order-service-client.version>
        <dividend-center.version>1.2.3</dividend-center.version>
        <!--Open sdk-->
        <taobao.version>1741068966553-20250304</taobao.version>
        <tiktok.version>1.1.0-20250506175806</tiktok.version>
        <sf.version>2.1.7-20220224</sf.version>
        <jd.version>2.0-20250801</jd.version>
        <jdcloud.version>1.3.5</jdcloud.version>
        <jd-lop.version>1.0.28</jd-lop.version>
        <jd-cloudprint.version>1.5_20240620201728</jd-cloudprint.version>
        <jd-EcapSDK.version>1.0</jd-EcapSDK.version>
        <jd-IntegratedSupply.version>4.7_20250305153851</jd-IntegratedSupply.version>
        <pdd.version>1.19.89</pdd.version>
        <kuaishou.version>1.0.7601-20250703-1</kuaishou.version>
        <xiaohongshu.version>20250724</xiaohongshu.version>
        <!--Framework-->
        <marketing-components-dto.version>1.0.0</marketing-components-dto.version>
        <cola.components.version>4.3.2</cola.components.version>
        <spring-cloud.version>2021.0.8</spring-cloud.version>
        <spring-boot.version>2.7.18</spring-boot.version>
        <spring-framework.version>5.3.39</spring-framework.version>
        <start-class>com.xtc.marketing.adapterservice.Application</start-class>
    </properties>

    <modules>
        <module>adapter-service-client</module>
        <module>adapter-service-adapter</module>
        <module>adapter-service-app</module>
        <module>adapter-service-domain</module>
        <module>adapter-service-infrastructure</module>
        <module>adapter-service-common</module>
        <module>start</module>
    </modules>

    <repositories>
        <!--阿里云镜像库-->
        <repository>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <layout>default</layout>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <!--公司内部库-->
        <repository>
            <id>maven-public</id>
            <url>http://packages.bbkedu.com/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </releases>
        </repository>
    </repositories>

    <dependencies>
        <!--Hot reload agent-->
        <dependency>
            <groupId>org.hotswapagent</groupId>
            <artifactId>hotswap-agent-spring-boot-plugin</artifactId>
            <version>2.0.1</version>
            <scope>provided</scope>
        </dependency>
        <!--Hot reload agent End-->
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!--Project modules-->
            <dependency>
                <groupId>com.xtc.marketing.adapterservice</groupId>
                <artifactId>adapter-service-adapter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xtc.marketing.adapterservice</groupId>
                <artifactId>adapter-service-client</artifactId>
                <version>${adapter-service-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xtc.marketing.adapterservice</groupId>
                <artifactId>adapter-service-app</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xtc.marketing.adapterservice</groupId>
                <artifactId>adapter-service-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xtc.marketing.adapterservice</groupId>
                <artifactId>adapter-service-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xtc.marketing.adapterservice</groupId>
                <artifactId>adapter-service-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!--Project modules End-->

            <!--COLA Components-->
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-components-bom</artifactId>
                <version>${cola.components.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--COLA Components End-->

            <!--Marketing Components-->
            <dependency>
                <groupId>com.xtc.marketing</groupId>
                <artifactId>marketing-component-dto</artifactId>
                <version>${marketing-components-dto.version}</version>
            </dependency>
            <!--Marketing Components End-->

            <!--Spring Cloud-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--Spring Cloud End-->

            <!--Spring Boot-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--Fix UriComponentsBuilder CVE-2024-22243 5.3.x users should upgrade to 5.3.32-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring-framework.version}</version>
                <scope>compile</scope>
            </dependency>
            <!--Fix Include repeatable annotation container in MergedAnnotations results upgrade to 5.3.35-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.redisson</groupId>
                        <artifactId>redisson-spring-data-30</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.redisson</groupId>
                        <artifactId>redisson-spring-data-31</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-data-27</artifactId>
                <version>${redisson-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.arthas</groupId>
                <artifactId>arthas-spring-boot-starter</artifactId>
                <version>${arthas-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${nacos-starter.version}</version>
            </dependency>
            <!--Spring Boot End-->

            <!--Datasource-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-starter.version}</version>
            </dependency>
            <!--Datasource End-->

            <!--XTC Client-->
            <dependency>
                <groupId>com.xtc.marketing.orderservice</groupId>
                <artifactId>order-service-client</artifactId>
                <version>${order-service-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xtc.dividend</groupId>
                <artifactId>dividend-center-client</artifactId>
                <version>${dividend-center.version}</version>
            </dependency>
            <!--XTC Client End-->

            <!--Open sdk-->
            <dependency>
                <groupId>com.taobao</groupId>
                <artifactId>taobao-sdk-java-auto</artifactId>
                <version>${taobao.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tiktok</groupId>
                <artifactId>doudian-sdk-java</artifactId>
                <version>${tiktok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sf</groupId>
                <artifactId>sf-csim-express-sdk</artifactId>
                <version>${sf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>open-api-sdk</artifactId>
                <version>${jd.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdcloud</groupId>
                <artifactId>jcq-java-sdk</artifactId>
                <version>${jdcloud.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>lop-opensdk-support</artifactId>
                <version>${jd-lop.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>jdcloudprint</artifactId>
                <version>${jd-cloudprint.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>EcapSDK</artifactId>
                <version>${jd-EcapSDK.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>IntegratedSupplyChain</artifactId>
                <version>${jd-IntegratedSupply.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pdd</groupId>
                <artifactId>pop-sdk</artifactId>
                <version>${pdd.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kuaishou</groupId>
                <artifactId>open_kwaishop_sdk</artifactId>
                <version>${kuaishou.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaohongshu</groupId>
                <artifactId>fe-platform-sdk</artifactId>
                <version>${xiaohongshu.version}</version>
            </dependency>
            <!--Open sdk End-->

            <!--Misc-->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.errorprone</groupId>
                        <artifactId>error_prone_annotations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.javen205</groupId>
                <artifactId>IJPay-WxPay</artifactId>
                <version>${ijapy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.20</version>
            </dependency>
            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>2.1.4</version>
            </dependency>
            <!--Misc End-->
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.10.1</version>
                    <configuration>
                        <annotationProcessorPaths>
                            <!--必须要添加 lombok 的 path，解决和 mapstruct 的冲突-->
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok-mapstruct-binding</artifactId>
                                <version>${lombok-mapstruct-binding.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.2.1</version>
                    <!--发布源码-->
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <phase>package</phase>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.4.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>

                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <configuration>
                        <mainClass>${start-class}</mainClass>
                        <includeSystemScope>true</includeSystemScope>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>maven-releases</name>
            <url>http://packages.bbkedu.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://packages.bbkedu.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>