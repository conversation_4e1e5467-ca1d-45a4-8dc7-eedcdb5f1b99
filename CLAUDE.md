# CLAUDE.md

这个文件为Claude Code (claude.ai/code) 在这个代码库工作时提供指导。

## 项目概述

这是一个基于COLA架构的多平台营销适配器服务，用于统一接入各大电商平台的API（抖音、拼多多、天猫、京东、快手、小红书等）。

## 技术架构

**分层架构（基于COLA框架）：**
- `adapter-service-adapter`: 适配器层，包含Controller和API接口
- `adapter-service-app`: 应用服务层，包含业务逻辑和扩展点实现
- `adapter-service-domain`: 领域层（目前较少使用）
- `adapter-service-infrastructure`: 基础设施层，包含数据访问和外部RPC调用
- `adapter-service-client`: 客户端包，提供Feign接口和DTO
- `adapter-service-common`: 通用工具类和异常处理
- `start`: 启动模块，包含Application主类

**核心业务模块：**
- `shop`: 店铺管理（订单、商品、库存等）
- `logistics`: 物流管理（快递查询、下单等）
- `warehouse`: 仓储管理
- `invoice`: 发票管理
- `notify`: 消息通知
- `bill`: 账单管理
- `subscribe`: 消息订阅

## 扩展点机制

项目采用COLA的扩展点模式实现多平台适配：
- `ExtPt`接口：定义扩展点契约
- `ExtConstant`类：定义用例和场景常量
- 各平台实现类：如`TmallShopExt`、`TikTokShopExt`、`PddShopExt`等

**支持的平台场景：**
- TMALL（天猫）
- JD（京东）  
- TIKTOK/TIKTOK_DISTR（抖音/抖音代发）
- PDD/PDD_DISTR（拼多多/拼多多代发）
- KUAISHOU/KUAISHOU_DISTR（快手/快手代发）
- WECHAT_CHANNELS_SHOP（微信视频号）
- XIAOHONGSHU（小红书）
- INTERNAL_SHOP（内部购机）
- XTC（官方商城、会员商城）

## 开发命令

**构建和运行：**
```bash
# 编译整个项目
mvn clean compile

# 打包
mvn clean package

# 运行应用（需要配置相关环境）
java -jar start/target/start.jar

# 运行API文档生成（smart-doc）
mvn compile
```

**开发环境（DevSpace）：**
```bash
# 启动开发环境
devspace dev

# 查看日志
devspace run logs

# 启动应用
devspace run start

# 查看可用命令
devspace list commands
```

## 技术栈

- **框架**: Spring Boot 2.7.18, Spring Cloud 2021.0.8
- **架构**: COLA 4.3.2
- **数据库**: MyBatis Plus 3.5.6, Druid连接池
- **缓存**: Redisson 3.23.2
- **配置**: Nacos 0.2.12
- **任务调度**: XXL-Job 2.4.0
- **监控**: Arthas 3.7.2
- **文档**: Smart-doc 3.0.6

## 环境配置

**Profile配置：**
- `dev`: 开发环境
- `test`: 测试环境  
- `grey`: 灰度环境
- `prod`: 生产环境

**必需的配置include：**
- `druid`: 数据库连接池配置
- `mybatis-plus`: ORM配置
- `nacos`: 配置中心
- `xxl-job`: 任务调度配置

## 外部依赖

项目集成了多个第三方平台SDK：
- 淘宝/天猫: taobao-sdk-java-auto
- 抖音: doudian-sdk-java  
- 京东: open-api-sdk, jcq-java-sdk
- 拼多多: pop-sdk
- 快手: open_kwaishop_sdk
- 小红书: fe-platform-sdk
- 顺丰: sf-csim-express-sdk

## 开发注意事项

1. **扩展点开发**: 新增平台支持时需要在对应的ExtConstant中添加场景常量，并实现相应的ExtPt接口
2. **消息推送**: 项目支持多种消息推送机制，涉及限流和重试机制
3. **多数据源**: 使用@Transactional时需要配置Propagation.REQUIRES_NEW
4. **热部署**: 开发环境支持HotSwap Agent进行代码热更新
5. **跨域配置**: 已配置CORS支持，包括小红书等平台的自定义头部