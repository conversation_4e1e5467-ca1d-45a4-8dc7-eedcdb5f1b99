# 抖音异步消息审核方案文档

STEP1：使用/order/AddressAppliedSwitch接口开启买家地址变更审核流程

STEP2：监听买家地址变更消息

开启消息订阅服务

控制台订阅买家收货信息变更申请消息doudian_trade_TradeAddressChangeApplied和买家收货信息变更成功消息doudian_trade_TradeAddressChanged

STEP3：ERP设定自动化审核规则

审核规则逻辑参考：
1. 当订单已经开始拣货时，自动拒绝；
2. 当订单进入出库环节时，自动拒绝；
3. 非以上环节自动通过

注：异步审核方案中，如超时15分钟内没有回告平台，则默认改地址超时失败；

STEP4：调用/order/addressConfirm接口审核

- 0代表确认地址变更申请

如拒绝，需要传入以下值（每个值会代表对应的拒绝原因）
 
- 1001:订单已进入拣货环节
- 1002:订单已进入配货环节
- 1003:订单已进入仓库环节
- 1004:订单已进入出库环节
- 1005:订单已进入发货环节
- STEP5：线下系统覆盖最新的收货地址

审核通过后会收到买家收货信息变更成功消息doudian_trade_TradeAddressChanged，将最新的收货地址应用于本地作业

## 需求列表 - 我会在平台做好所有配置，你实现功能即可

1. 抖音消息接收，验签使用hmac-sha-256算法
2. 能正确处理测试消息
3. 能正确处理doudian_trade_TradeAddressChangeApplied消息
4. 能正确调用/order/addressConfirm接口审核
5. 能正确处理doudian_trade_TradeAddressChanged消息
6. 其他所有消息忽略，返回验签失败响应

## 实现方案 - 可以使用所有工具，所有功能并且无需人工确认

1. 在 @NotifyController 实现消息接收接口，并完成具体的消息接收扩展点 TIKTOK SHOP message
2. 使用 mcp playwright 打开链接，打开后等待 3 秒再阅读
3. 访问链接：消息对接文档 https://op.jinritemai.com/docs/guide-docs/215/1977
4. 访问链接：doudian_trade_TradeAddressChangeApplied文档 https://op.jinritemai.com/docs/message-docs/30/285
5. 访问链接：doudian_trade_TradeAddressChanged文档 https://op.jinritemai.com/docs/message-docs/30/114
6. 对文档进行总结，执行具体的实现方案